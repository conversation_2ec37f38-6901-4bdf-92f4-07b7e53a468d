{"version": 3, "sources": ["../../node_modules/@smithy/middleware-stack/dist-es/MiddlewareStack.js", "../../node_modules/@smithy/middleware-stack/dist-es/index.js", "../../node_modules/@smithy/smithy-client/dist-es/client.js", "../../node_modules/@smithy/types/dist-es/abort.js", "../../node_modules/@smithy/types/dist-es/auth/auth.js", "../../node_modules/@smithy/types/dist-es/auth/HttpApiKeyAuth.js", "../../node_modules/@smithy/types/dist-es/auth/HttpAuthScheme.js", "../../node_modules/@smithy/types/dist-es/auth/HttpAuthSchemeProvider.js", "../../node_modules/@smithy/types/dist-es/auth/HttpSigner.js", "../../node_modules/@smithy/types/dist-es/auth/IdentityProviderConfig.js", "../../node_modules/@smithy/types/dist-es/auth/index.js", "../../node_modules/@smithy/types/dist-es/blob/blob-payload-input-types.js", "../../node_modules/@smithy/types/dist-es/checksum.js", "../../node_modules/@smithy/types/dist-es/client.js", "../../node_modules/@smithy/types/dist-es/command.js", "../../node_modules/@smithy/types/dist-es/connection/config.js", "../../node_modules/@smithy/types/dist-es/connection/manager.js", "../../node_modules/@smithy/types/dist-es/connection/pool.js", "../../node_modules/@smithy/types/dist-es/connection/index.js", "../../node_modules/@smithy/types/dist-es/crypto.js", "../../node_modules/@smithy/types/dist-es/encode.js", "../../node_modules/@smithy/types/dist-es/endpoint.js", "../../node_modules/@smithy/types/dist-es/endpoints/EndpointRuleObject.js", "../../node_modules/@smithy/types/dist-es/endpoints/ErrorRuleObject.js", "../../node_modules/@smithy/types/dist-es/endpoints/RuleSetObject.js", "../../node_modules/@smithy/types/dist-es/endpoints/shared.js", "../../node_modules/@smithy/types/dist-es/endpoints/TreeRuleObject.js", "../../node_modules/@smithy/types/dist-es/endpoints/index.js", "../../node_modules/@smithy/types/dist-es/eventStream.js", "../../node_modules/@smithy/types/dist-es/extensions/checksum.js", "../../node_modules/@smithy/types/dist-es/extensions/defaultClientConfiguration.js", "../../node_modules/@smithy/types/dist-es/extensions/defaultExtensionConfiguration.js", "../../node_modules/@smithy/types/dist-es/extensions/index.js", "../../node_modules/@smithy/types/dist-es/feature-ids.js", "../../node_modules/@smithy/types/dist-es/http.js", "../../node_modules/@smithy/types/dist-es/http/httpHandlerInitialization.js", "../../node_modules/@smithy/types/dist-es/identity/apiKeyIdentity.js", "../../node_modules/@smithy/types/dist-es/identity/awsCredentialIdentity.js", "../../node_modules/@smithy/types/dist-es/identity/identity.js", "../../node_modules/@smithy/types/dist-es/identity/tokenIdentity.js", "../../node_modules/@smithy/types/dist-es/identity/index.js", "../../node_modules/@smithy/types/dist-es/logger.js", "../../node_modules/@smithy/types/dist-es/middleware.js", "../../node_modules/@smithy/types/dist-es/pagination.js", "../../node_modules/@smithy/types/dist-es/profile.js", "../../node_modules/@smithy/types/dist-es/response.js", "../../node_modules/@smithy/types/dist-es/retry.js", "../../node_modules/@smithy/types/dist-es/serde.js", "../../node_modules/@smithy/types/dist-es/shapes.js", "../../node_modules/@smithy/types/dist-es/signature.js", "../../node_modules/@smithy/types/dist-es/stream.js", "../../node_modules/@smithy/types/dist-es/streaming-payload/streaming-blob-common-types.js", "../../node_modules/@smithy/types/dist-es/streaming-payload/streaming-blob-payload-input-types.js", "../../node_modules/@smithy/types/dist-es/streaming-payload/streaming-blob-payload-output-types.js", "../../node_modules/@smithy/types/dist-es/transfer.js", "../../node_modules/@smithy/types/dist-es/transform/client-payload-blob-type-narrow.js", "../../node_modules/@smithy/types/dist-es/transform/no-undefined.js", "../../node_modules/@smithy/types/dist-es/transform/type-transform.js", "../../node_modules/@smithy/types/dist-es/uri.js", "../../node_modules/@smithy/types/dist-es/util.js", "../../node_modules/@smithy/types/dist-es/waiter.js", "../../node_modules/@smithy/types/dist-es/index.js", "../../node_modules/@smithy/smithy-client/dist-es/command.js", "../../node_modules/@smithy/util-middleware/dist-es/getSmithyContext.js", "../../node_modules/@smithy/util-middleware/dist-es/normalizeProvider.js", "../../node_modules/@smithy/util-middleware/dist-es/index.js", "../../node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/resolveAuthOptions.js", "../../node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js", "../../node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemeEndpointRuleSetPlugin.js", "../../node_modules/@smithy/middleware-serde/dist-es/deserializerMiddleware.js", "../../node_modules/@smithy/middleware-serde/dist-es/serializerMiddleware.js", "../../node_modules/@smithy/middleware-serde/dist-es/serdePlugin.js", "../../node_modules/@smithy/middleware-serde/dist-es/index.js", "../../node_modules/@smithy/protocol-http/dist-es/extensions/httpExtensionConfiguration.js", "../../node_modules/@smithy/protocol-http/dist-es/extensions/index.js", "../../node_modules/@smithy/protocol-http/dist-es/Field.js", "../../node_modules/@smithy/protocol-http/dist-es/Fields.js", "../../node_modules/@smithy/protocol-http/dist-es/httpHandler.js", "../../node_modules/@smithy/protocol-http/dist-es/httpRequest.js", "../../node_modules/@smithy/protocol-http/dist-es/httpResponse.js", "../../node_modules/@smithy/protocol-http/dist-es/isValidHostname.js", "../../node_modules/@smithy/protocol-http/dist-es/types.js", "../../node_modules/@smithy/protocol-http/dist-es/index.js", "../../node_modules/@smithy/core/dist-es/middleware-http-signing/httpSigningMiddleware.js", "../../node_modules/@smithy/core/dist-es/middleware-http-signing/getHttpSigningMiddleware.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/DefaultIdentityProviderConfig.js", "../../node_modules/@smithy/core/dist-es/getSmithyContext.js", "../../node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemePlugin.js", "../../node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/index.js", "../../node_modules/@smithy/core/dist-es/middleware-http-signing/index.js", "../../node_modules/@smithy/core/dist-es/normalizeProvider.js", "../../node_modules/@smithy/core/dist-es/pagination/createPaginator.js", "../../node_modules/@smithy/util-base64/dist-es/constants.browser.js", "../../node_modules/@smithy/util-base64/dist-es/fromBase64.browser.js", "../../node_modules/@smithy/util-utf8/dist-es/fromUtf8.browser.js", "../../node_modules/@smithy/util-utf8/dist-es/toUint8Array.js", "../../node_modules/@smithy/util-utf8/dist-es/toUtf8.browser.js", "../../node_modules/@smithy/util-utf8/dist-es/index.js", "../../node_modules/@smithy/util-base64/dist-es/toBase64.browser.js", "../../node_modules/@smithy/util-base64/dist-es/index.js", "../../node_modules/@smithy/util-stream/dist-es/blob/transforms.js", "../../node_modules/@smithy/util-stream/dist-es/blob/Uint8ArrayBlobAdapter.js", "../../node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.browser.js", "../../node_modules/@smithy/util-stream/dist-es/stream-type-check.js", "../../node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.browser.js", "../../node_modules/@smithy/util-stream/dist-es/ByteArrayCollector.js", "../../node_modules/@smithy/util-stream/dist-es/createBufferedReadableStream.js", "../../node_modules/@smithy/util-stream/dist-es/getAwsChunkedEncodingStream.browser.js", "../../node_modules/@smithy/util-stream/dist-es/headStream.browser.js", "../../node_modules/@smithy/util-uri-escape/dist-es/escape-uri.js", "../../node_modules/@smithy/util-uri-escape/dist-es/escape-uri-path.js", "../../node_modules/@smithy/util-uri-escape/dist-es/index.js", "../../node_modules/@smithy/querystring-builder/dist-es/index.js", "../../node_modules/@smithy/fetch-http-handler/dist-es/create-request.js", "../../node_modules/@smithy/fetch-http-handler/dist-es/request-timeout.js", "../../node_modules/@smithy/fetch-http-handler/dist-es/fetch-http-handler.js", "../../node_modules/@smithy/fetch-http-handler/dist-es/stream-collector.js", "../../node_modules/@smithy/fetch-http-handler/dist-es/index.js", "../../node_modules/@smithy/util-hex-encoding/dist-es/index.js", "../../node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.browser.js", "../../node_modules/@smithy/util-stream/dist-es/splitStream.browser.js", "../../node_modules/@smithy/util-stream/dist-es/index.js", "../../node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js", "../../node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js", "../../node_modules/@smithy/core/dist-es/submodules/protocols/resolve-path.js", "../../node_modules/@smithy/core/dist-es/submodules/protocols/requestBuilder.js", "../../node_modules/@smithy/core/dist-es/submodules/protocols/index.js", "../../node_modules/@smithy/core/dist-es/protocols/requestBuilder.js", "../../node_modules/@smithy/core/dist-es/setFeature.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpApiKeyAuth.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpBearerAuth.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/noAuth.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/index.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/memoizeIdentityProvider.js", "../../node_modules/@smithy/core/dist-es/util-identity-and-auth/index.js", "../../node_modules/@smithy/core/dist-es/index.js", "../../node_modules/@smithy/smithy-client/dist-es/constants.js", "../../node_modules/@smithy/smithy-client/dist-es/create-aggregated-client.js", "../../node_modules/@smithy/smithy-client/dist-es/exceptions.js", "../../node_modules/@smithy/smithy-client/dist-es/collect-stream-body.js", "../../node_modules/@smithy/smithy-client/dist-es/parse-utils.js", "../../node_modules/@smithy/smithy-client/dist-es/date-utils.js", "../../node_modules/@smithy/smithy-client/dist-es/default-error-handler.js", "../../node_modules/@smithy/smithy-client/dist-es/defaults-mode.js", "../../node_modules/@smithy/smithy-client/dist-es/emitWarningIfUnsupportedVersion.js", "../../node_modules/@smithy/smithy-client/dist-es/extended-encode-uri-component.js", "../../node_modules/@smithy/smithy-client/dist-es/extensions/checksum.js", "../../node_modules/@smithy/smithy-client/dist-es/extensions/retry.js", "../../node_modules/@smithy/smithy-client/dist-es/extensions/defaultExtensionConfiguration.js", "../../node_modules/@smithy/smithy-client/dist-es/extensions/index.js", "../../node_modules/@smithy/smithy-client/dist-es/get-array-if-single-item.js", "../../node_modules/@smithy/smithy-client/dist-es/get-value-from-text-node.js", "../../node_modules/@smithy/smithy-client/dist-es/is-serializable-header-value.js", "../../node_modules/@smithy/smithy-client/dist-es/lazy-json.js", "../../node_modules/@smithy/smithy-client/dist-es/NoOpLogger.js", "../../node_modules/@smithy/smithy-client/dist-es/object-mapping.js", "../../node_modules/@smithy/smithy-client/dist-es/quote-header.js", "../../node_modules/@smithy/smithy-client/dist-es/resolve-path.js", "../../node_modules/@smithy/smithy-client/dist-es/ser-utils.js", "../../node_modules/@smithy/smithy-client/dist-es/serde-json.js", "../../node_modules/@smithy/smithy-client/dist-es/split-every.js", "../../node_modules/@smithy/smithy-client/dist-es/split-header.js", "../../node_modules/@smithy/smithy-client/dist-es/index.js", "../../node_modules/@aws-crypto/sha256-js/src/constants.ts", "../../node_modules/@aws-crypto/sha256-js/src/RawSha256.ts", "../../node_modules/@aws-crypto/util/node_modules/@smithy/util-utf8/dist-es/fromUtf8.browser.js", "../../node_modules/@aws-crypto/util/node_modules/@smithy/util-utf8/dist-es/toUint8Array.js", "../../node_modules/@aws-crypto/util/node_modules/@smithy/util-utf8/dist-es/toUtf8.browser.js", "../../node_modules/@aws-crypto/util/node_modules/@smithy/util-utf8/dist-es/index.js", "../../node_modules/@aws-crypto/util/src/convertToBuffer.ts", "../../node_modules/@aws-crypto/util/src/isEmptyData.ts", "../../node_modules/@aws-crypto/util/src/numToUint8.ts", "../../node_modules/@aws-crypto/util/src/uint32ArrayFrom.ts", "../../node_modules/@aws-crypto/util/src/index.ts", "../../node_modules/@aws-crypto/sha256-js/src/jsSha256.ts", "../../node_modules/@aws-crypto/sha256-js/src/index.ts", "../../node_modules/@smithy/property-provider/dist-es/memoize.js", "../../node_modules/@smithy/property-provider/dist-es/ProviderError.js", "../../node_modules/@smithy/property-provider/dist-es/CredentialsProviderError.js", "../../node_modules/@smithy/property-provider/dist-es/TokenProviderError.js", "../../node_modules/@smithy/property-provider/dist-es/chain.js", "../../node_modules/@smithy/property-provider/dist-es/fromStatic.js", "../../node_modules/@smithy/property-provider/dist-es/index.js"], "sourcesContent": ["const getAllAliases = (name, aliases) => {\n    const _aliases = [];\n    if (name) {\n        _aliases.push(name);\n    }\n    if (aliases) {\n        for (const alias of aliases) {\n            _aliases.push(alias);\n        }\n    }\n    return _aliases;\n};\nconst getMiddlewareNameWithAliases = (name, aliases) => {\n    return `${name || \"anonymous\"}${aliases && aliases.length > 0 ? ` (a.k.a. ${aliases.join(\",\")})` : \"\"}`;\n};\nexport const constructStack = () => {\n    let absoluteEntries = [];\n    let relativeEntries = [];\n    let identifyOnResolve = false;\n    const entriesNameSet = new Set();\n    const sort = (entries) => entries.sort((a, b) => stepWeights[b.step] - stepWeights[a.step] ||\n        priorityWeights[b.priority || \"normal\"] - priorityWeights[a.priority || \"normal\"]);\n    const removeByName = (toRemove) => {\n        let isRemoved = false;\n        const filterCb = (entry) => {\n            const aliases = getAllAliases(entry.name, entry.aliases);\n            if (aliases.includes(toRemove)) {\n                isRemoved = true;\n                for (const alias of aliases) {\n                    entriesNameSet.delete(alias);\n                }\n                return false;\n            }\n            return true;\n        };\n        absoluteEntries = absoluteEntries.filter(filterCb);\n        relativeEntries = relativeEntries.filter(filterCb);\n        return isRemoved;\n    };\n    const removeByReference = (toRemove) => {\n        let isRemoved = false;\n        const filterCb = (entry) => {\n            if (entry.middleware === toRemove) {\n                isRemoved = true;\n                for (const alias of getAllAliases(entry.name, entry.aliases)) {\n                    entriesNameSet.delete(alias);\n                }\n                return false;\n            }\n            return true;\n        };\n        absoluteEntries = absoluteEntries.filter(filterCb);\n        relativeEntries = relativeEntries.filter(filterCb);\n        return isRemoved;\n    };\n    const cloneTo = (toStack) => {\n        absoluteEntries.forEach((entry) => {\n            toStack.add(entry.middleware, { ...entry });\n        });\n        relativeEntries.forEach((entry) => {\n            toStack.addRelativeTo(entry.middleware, { ...entry });\n        });\n        toStack.identifyOnResolve?.(stack.identifyOnResolve());\n        return toStack;\n    };\n    const expandRelativeMiddlewareList = (from) => {\n        const expandedMiddlewareList = [];\n        from.before.forEach((entry) => {\n            if (entry.before.length === 0 && entry.after.length === 0) {\n                expandedMiddlewareList.push(entry);\n            }\n            else {\n                expandedMiddlewareList.push(...expandRelativeMiddlewareList(entry));\n            }\n        });\n        expandedMiddlewareList.push(from);\n        from.after.reverse().forEach((entry) => {\n            if (entry.before.length === 0 && entry.after.length === 0) {\n                expandedMiddlewareList.push(entry);\n            }\n            else {\n                expandedMiddlewareList.push(...expandRelativeMiddlewareList(entry));\n            }\n        });\n        return expandedMiddlewareList;\n    };\n    const getMiddlewareList = (debug = false) => {\n        const normalizedAbsoluteEntries = [];\n        const normalizedRelativeEntries = [];\n        const normalizedEntriesNameMap = {};\n        absoluteEntries.forEach((entry) => {\n            const normalizedEntry = {\n                ...entry,\n                before: [],\n                after: [],\n            };\n            for (const alias of getAllAliases(normalizedEntry.name, normalizedEntry.aliases)) {\n                normalizedEntriesNameMap[alias] = normalizedEntry;\n            }\n            normalizedAbsoluteEntries.push(normalizedEntry);\n        });\n        relativeEntries.forEach((entry) => {\n            const normalizedEntry = {\n                ...entry,\n                before: [],\n                after: [],\n            };\n            for (const alias of getAllAliases(normalizedEntry.name, normalizedEntry.aliases)) {\n                normalizedEntriesNameMap[alias] = normalizedEntry;\n            }\n            normalizedRelativeEntries.push(normalizedEntry);\n        });\n        normalizedRelativeEntries.forEach((entry) => {\n            if (entry.toMiddleware) {\n                const toMiddleware = normalizedEntriesNameMap[entry.toMiddleware];\n                if (toMiddleware === undefined) {\n                    if (debug) {\n                        return;\n                    }\n                    throw new Error(`${entry.toMiddleware} is not found when adding ` +\n                        `${getMiddlewareNameWithAliases(entry.name, entry.aliases)} ` +\n                        `middleware ${entry.relation} ${entry.toMiddleware}`);\n                }\n                if (entry.relation === \"after\") {\n                    toMiddleware.after.push(entry);\n                }\n                if (entry.relation === \"before\") {\n                    toMiddleware.before.push(entry);\n                }\n            }\n        });\n        const mainChain = sort(normalizedAbsoluteEntries)\n            .map(expandRelativeMiddlewareList)\n            .reduce((wholeList, expandedMiddlewareList) => {\n            wholeList.push(...expandedMiddlewareList);\n            return wholeList;\n        }, []);\n        return mainChain;\n    };\n    const stack = {\n        add: (middleware, options = {}) => {\n            const { name, override, aliases: _aliases } = options;\n            const entry = {\n                step: \"initialize\",\n                priority: \"normal\",\n                middleware,\n                ...options,\n            };\n            const aliases = getAllAliases(name, _aliases);\n            if (aliases.length > 0) {\n                if (aliases.some((alias) => entriesNameSet.has(alias))) {\n                    if (!override)\n                        throw new Error(`Duplicate middleware name '${getMiddlewareNameWithAliases(name, _aliases)}'`);\n                    for (const alias of aliases) {\n                        const toOverrideIndex = absoluteEntries.findIndex((entry) => entry.name === alias || entry.aliases?.some((a) => a === alias));\n                        if (toOverrideIndex === -1) {\n                            continue;\n                        }\n                        const toOverride = absoluteEntries[toOverrideIndex];\n                        if (toOverride.step !== entry.step || entry.priority !== toOverride.priority) {\n                            throw new Error(`\"${getMiddlewareNameWithAliases(toOverride.name, toOverride.aliases)}\" middleware with ` +\n                                `${toOverride.priority} priority in ${toOverride.step} step cannot ` +\n                                `be overridden by \"${getMiddlewareNameWithAliases(name, _aliases)}\" middleware with ` +\n                                `${entry.priority} priority in ${entry.step} step.`);\n                        }\n                        absoluteEntries.splice(toOverrideIndex, 1);\n                    }\n                }\n                for (const alias of aliases) {\n                    entriesNameSet.add(alias);\n                }\n            }\n            absoluteEntries.push(entry);\n        },\n        addRelativeTo: (middleware, options) => {\n            const { name, override, aliases: _aliases } = options;\n            const entry = {\n                middleware,\n                ...options,\n            };\n            const aliases = getAllAliases(name, _aliases);\n            if (aliases.length > 0) {\n                if (aliases.some((alias) => entriesNameSet.has(alias))) {\n                    if (!override)\n                        throw new Error(`Duplicate middleware name '${getMiddlewareNameWithAliases(name, _aliases)}'`);\n                    for (const alias of aliases) {\n                        const toOverrideIndex = relativeEntries.findIndex((entry) => entry.name === alias || entry.aliases?.some((a) => a === alias));\n                        if (toOverrideIndex === -1) {\n                            continue;\n                        }\n                        const toOverride = relativeEntries[toOverrideIndex];\n                        if (toOverride.toMiddleware !== entry.toMiddleware || toOverride.relation !== entry.relation) {\n                            throw new Error(`\"${getMiddlewareNameWithAliases(toOverride.name, toOverride.aliases)}\" middleware ` +\n                                `${toOverride.relation} \"${toOverride.toMiddleware}\" middleware cannot be overridden ` +\n                                `by \"${getMiddlewareNameWithAliases(name, _aliases)}\" middleware ${entry.relation} ` +\n                                `\"${entry.toMiddleware}\" middleware.`);\n                        }\n                        relativeEntries.splice(toOverrideIndex, 1);\n                    }\n                }\n                for (const alias of aliases) {\n                    entriesNameSet.add(alias);\n                }\n            }\n            relativeEntries.push(entry);\n        },\n        clone: () => cloneTo(constructStack()),\n        use: (plugin) => {\n            plugin.applyToStack(stack);\n        },\n        remove: (toRemove) => {\n            if (typeof toRemove === \"string\")\n                return removeByName(toRemove);\n            else\n                return removeByReference(toRemove);\n        },\n        removeByTag: (toRemove) => {\n            let isRemoved = false;\n            const filterCb = (entry) => {\n                const { tags, name, aliases: _aliases } = entry;\n                if (tags && tags.includes(toRemove)) {\n                    const aliases = getAllAliases(name, _aliases);\n                    for (const alias of aliases) {\n                        entriesNameSet.delete(alias);\n                    }\n                    isRemoved = true;\n                    return false;\n                }\n                return true;\n            };\n            absoluteEntries = absoluteEntries.filter(filterCb);\n            relativeEntries = relativeEntries.filter(filterCb);\n            return isRemoved;\n        },\n        concat: (from) => {\n            const cloned = cloneTo(constructStack());\n            cloned.use(from);\n            cloned.identifyOnResolve(identifyOnResolve || cloned.identifyOnResolve() || (from.identifyOnResolve?.() ?? false));\n            return cloned;\n        },\n        applyToStack: cloneTo,\n        identify: () => {\n            return getMiddlewareList(true).map((mw) => {\n                const step = mw.step ??\n                    mw.relation +\n                        \" \" +\n                        mw.toMiddleware;\n                return getMiddlewareNameWithAliases(mw.name, mw.aliases) + \" - \" + step;\n            });\n        },\n        identifyOnResolve(toggle) {\n            if (typeof toggle === \"boolean\")\n                identifyOnResolve = toggle;\n            return identifyOnResolve;\n        },\n        resolve: (handler, context) => {\n            for (const middleware of getMiddlewareList()\n                .map((entry) => entry.middleware)\n                .reverse()) {\n                handler = middleware(handler, context);\n            }\n            if (identifyOnResolve) {\n                console.log(stack.identify());\n            }\n            return handler;\n        },\n    };\n    return stack;\n};\nconst stepWeights = {\n    initialize: 5,\n    serialize: 4,\n    build: 3,\n    finalizeRequest: 2,\n    deserialize: 1,\n};\nconst priorityWeights = {\n    high: 3,\n    normal: 2,\n    low: 1,\n};\n", "export * from \"./MiddlewareStack\";\n", "import { constructStack } from \"@smithy/middleware-stack\";\nexport class Client {\n    constructor(config) {\n        this.config = config;\n        this.middlewareStack = constructStack();\n    }\n    send(command, optionsOrCb, cb) {\n        const options = typeof optionsOrCb !== \"function\" ? optionsOrCb : undefined;\n        const callback = typeof optionsOrCb === \"function\" ? optionsOrCb : cb;\n        const useHandlerCache = options === undefined && this.config.cacheMiddleware === true;\n        let handler;\n        if (useHandlerCache) {\n            if (!this.handlers) {\n                this.handlers = new WeakMap();\n            }\n            const handlers = this.handlers;\n            if (handlers.has(command.constructor)) {\n                handler = handlers.get(command.constructor);\n            }\n            else {\n                handler = command.resolveMiddleware(this.middlewareStack, this.config, options);\n                handlers.set(command.constructor, handler);\n            }\n        }\n        else {\n            delete this.handlers;\n            handler = command.resolveMiddleware(this.middlewareStack, this.config, options);\n        }\n        if (callback) {\n            handler(command)\n                .then((result) => callback(null, result.output), (err) => callback(err))\n                .catch(() => { });\n        }\n        else {\n            return handler(command).then((result) => result.output);\n        }\n    }\n    destroy() {\n        this.config?.requestHandler?.destroy?.();\n        delete this.handlers;\n    }\n}\n", "export {};\n", "export var HttpAuthLocation;\n(function (HttpAuthLocation) {\n    HttpAuthLocation[\"HEADER\"] = \"header\";\n    HttpAuthLocation[\"QUERY\"] = \"query\";\n})(HttpAuthLocation || (HttpAuthLocation = {}));\n", "export var HttpApiKeyAuthLocation;\n(function (HttpApiKeyAuthLocation) {\n    HttpApiKeyAuthLocation[\"HEADER\"] = \"header\";\n    HttpApiKeyAuthLocation[\"QUERY\"] = \"query\";\n})(HttpApiKeyAuthLocation || (HttpApiKeyAuthLocation = {}));\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./auth\";\nexport * from \"./HttpApiKeyAuth\";\nexport * from \"./HttpAuthScheme\";\nexport * from \"./HttpAuthSchemeProvider\";\nexport * from \"./HttpSigner\";\nexport * from \"./IdentityProviderConfig\";\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./config\";\nexport * from \"./manager\";\nexport * from \"./pool\";\n", "export {};\n", "export {};\n", "export var EndpointURLScheme;\n(function (EndpointURLScheme) {\n    EndpointURLScheme[\"HTTP\"] = \"http\";\n    EndpointURLScheme[\"HTTPS\"] = \"https\";\n})(EndpointURLScheme || (EndpointURLScheme = {}));\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./EndpointRuleObject\";\nexport * from \"./ErrorRuleObject\";\nexport * from \"./RuleSetObject\";\nexport * from \"./shared\";\nexport * from \"./TreeRuleObject\";\n", "export {};\n", "export var AlgorithmId;\n(function (AlgorithmId) {\n    AlgorithmId[\"MD5\"] = \"md5\";\n    AlgorithmId[\"CRC32\"] = \"crc32\";\n    AlgorithmId[\"CRC32C\"] = \"crc32c\";\n    AlgorithmId[\"SHA1\"] = \"sha1\";\n    AlgorithmId[\"SHA256\"] = \"sha256\";\n})(AlgorithmId || (AlgorithmId = {}));\nexport const getChecksumConfiguration = (runtimeConfig) => {\n    const checksumAlgorithms = [];\n    if (runtimeConfig.sha256 !== undefined) {\n        checksumAlgorithms.push({\n            algorithmId: () => AlgorithmId.SHA256,\n            checksumConstructor: () => runtimeConfig.sha256,\n        });\n    }\n    if (runtimeConfig.md5 != undefined) {\n        checksumAlgorithms.push({\n            algorithmId: () => AlgorithmId.MD5,\n            checksumConstructor: () => runtimeConfig.md5,\n        });\n    }\n    return {\n        addChecksumAlgorithm(algo) {\n            checksumAlgorithms.push(algo);\n        },\n        checksumAlgorithms() {\n            return checksumAlgorithms;\n        },\n    };\n};\nexport const resolveChecksumRuntimeConfig = (clientConfig) => {\n    const runtimeConfig = {};\n    clientConfig.checksumAlgorithms().forEach((checksumAlgorithm) => {\n        runtimeConfig[checksumAlgorithm.algorithmId()] = checksumAlgorithm.checksumConstructor();\n    });\n    return runtimeConfig;\n};\n", "import { getChecksumConfiguration, resolveChecksumRuntimeConfig } from \"./checksum\";\nexport const getDefaultClientConfiguration = (runtimeConfig) => {\n    return getChecksumConfiguration(runtimeConfig);\n};\nexport const resolveDefaultRuntimeConfig = (config) => {\n    return resolveChecksumRuntimeConfig(config);\n};\n", "export {};\n", "export * from \"./defaultClientConfiguration\";\nexport * from \"./defaultExtensionConfiguration\";\nexport { AlgorithmId } from \"./checksum\";\n", "export {};\n", "export var FieldPosition;\n(function (FieldPosition) {\n    FieldPosition[FieldPosition[\"HEADER\"] = 0] = \"HEADER\";\n    FieldPosition[FieldPosition[\"TRAILER\"] = 1] = \"TRAILER\";\n})(FieldPosition || (FieldPosition = {}));\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./apiKeyIdentity\";\nexport * from \"./awsCredentialIdentity\";\nexport * from \"./identity\";\nexport * from \"./tokenIdentity\";\n", "export {};\n", "export const SMITHY_CONTEXT_KEY = \"__smithy_context\";\n", "export {};\n", "export var IniSectionType;\n(function (IniSectionType) {\n    IniSectionType[\"PROFILE\"] = \"profile\";\n    IniSectionType[\"SSO_SESSION\"] = \"sso-session\";\n    IniSectionType[\"SERVICES\"] = \"services\";\n})(IniSectionType || (IniSectionType = {}));\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export var RequestHandlerProtocol;\n(function (RequestHandlerProtocol) {\n    RequestHandlerProtocol[\"HTTP_0_9\"] = \"http/0.9\";\n    RequestHandlerProtocol[\"HTTP_1_0\"] = \"http/1.0\";\n    RequestHandlerProtocol[\"TDS_8_0\"] = \"tds/8.0\";\n})(RequestHandlerProtocol || (RequestHandlerProtocol = {}));\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./abort\";\nexport * from \"./auth\";\nexport * from \"./blob/blob-payload-input-types\";\nexport * from \"./checksum\";\nexport * from \"./client\";\nexport * from \"./command\";\nexport * from \"./connection\";\nexport * from \"./crypto\";\nexport * from \"./encode\";\nexport * from \"./endpoint\";\nexport * from \"./endpoints\";\nexport * from \"./eventStream\";\nexport * from \"./extensions\";\nexport * from \"./feature-ids\";\nexport * from \"./http\";\nexport * from \"./http/httpHandlerInitialization\";\nexport * from \"./identity\";\nexport * from \"./logger\";\nexport * from \"./middleware\";\nexport * from \"./pagination\";\nexport * from \"./profile\";\nexport * from \"./response\";\nexport * from \"./retry\";\nexport * from \"./serde\";\nexport * from \"./shapes\";\nexport * from \"./signature\";\nexport * from \"./stream\";\nexport * from \"./streaming-payload/streaming-blob-common-types\";\nexport * from \"./streaming-payload/streaming-blob-payload-input-types\";\nexport * from \"./streaming-payload/streaming-blob-payload-output-types\";\nexport * from \"./transfer\";\nexport * from \"./transform/client-payload-blob-type-narrow\";\nexport * from \"./transform/no-undefined\";\nexport * from \"./transform/type-transform\";\nexport * from \"./uri\";\nexport * from \"./util\";\nexport * from \"./waiter\";\n", "import { constructStack } from \"@smithy/middleware-stack\";\nimport { SMITHY_CONTEXT_KEY } from \"@smithy/types\";\nexport class Command {\n    constructor() {\n        this.middlewareStack = constructStack();\n    }\n    static classBuilder() {\n        return new ClassBuilder();\n    }\n    resolveMiddlewareWithContext(clientStack, configuration, options, { middlewareFn, clientName, commandName, inputFilterSensitiveLog, outputFilterSensitiveLog, smithyContext, additionalContext, CommandCtor, }) {\n        for (const mw of middlewareFn.bind(this)(CommandCtor, clientStack, configuration, options)) {\n            this.middlewareStack.use(mw);\n        }\n        const stack = clientStack.concat(this.middlewareStack);\n        const { logger } = configuration;\n        const handlerExecutionContext = {\n            logger,\n            clientName,\n            commandName,\n            inputFilterSensitiveLog,\n            outputFilterSensitiveLog,\n            [SMITHY_CONTEXT_KEY]: {\n                commandInstance: this,\n                ...smithyContext,\n            },\n            ...additionalContext,\n        };\n        const { requestHandler } = configuration;\n        return stack.resolve((request) => requestHandler.handle(request.request, options || {}), handlerExecutionContext);\n    }\n}\nclass ClassBuilder {\n    constructor() {\n        this._init = () => { };\n        this._ep = {};\n        this._middlewareFn = () => [];\n        this._commandName = \"\";\n        this._clientName = \"\";\n        this._additionalContext = {};\n        this._smithyContext = {};\n        this._inputFilterSensitiveLog = (_) => _;\n        this._outputFilterSensitiveLog = (_) => _;\n        this._serializer = null;\n        this._deserializer = null;\n    }\n    init(cb) {\n        this._init = cb;\n    }\n    ep(endpointParameterInstructions) {\n        this._ep = endpointParameterInstructions;\n        return this;\n    }\n    m(middlewareSupplier) {\n        this._middlewareFn = middlewareSupplier;\n        return this;\n    }\n    s(service, operation, smithyContext = {}) {\n        this._smithyContext = {\n            service,\n            operation,\n            ...smithyContext,\n        };\n        return this;\n    }\n    c(additionalContext = {}) {\n        this._additionalContext = additionalContext;\n        return this;\n    }\n    n(clientName, commandName) {\n        this._clientName = clientName;\n        this._commandName = commandName;\n        return this;\n    }\n    f(inputFilter = (_) => _, outputFilter = (_) => _) {\n        this._inputFilterSensitiveLog = inputFilter;\n        this._outputFilterSensitiveLog = outputFilter;\n        return this;\n    }\n    ser(serializer) {\n        this._serializer = serializer;\n        return this;\n    }\n    de(deserializer) {\n        this._deserializer = deserializer;\n        return this;\n    }\n    build() {\n        const closure = this;\n        let CommandRef;\n        return (CommandRef = class extends Command {\n            static getEndpointParameterInstructions() {\n                return closure._ep;\n            }\n            constructor(...[input]) {\n                super();\n                this.serialize = closure._serializer;\n                this.deserialize = closure._deserializer;\n                this.input = input ?? {};\n                closure._init(this);\n            }\n            resolveMiddleware(stack, configuration, options) {\n                return this.resolveMiddlewareWithContext(stack, configuration, options, {\n                    CommandCtor: CommandRef,\n                    middlewareFn: closure._middlewareFn,\n                    clientName: closure._clientName,\n                    commandName: closure._commandName,\n                    inputFilterSensitiveLog: closure._inputFilterSensitiveLog,\n                    outputFilterSensitiveLog: closure._outputFilterSensitiveLog,\n                    smithyContext: closure._smithyContext,\n                    additionalContext: closure._additionalContext,\n                });\n            }\n        });\n    }\n}\n", "import { SMITHY_CONTEXT_KEY } from \"@smithy/types\";\nexport const getSmithyContext = (context) => context[SMITHY_CONTEXT_KEY] || (context[SMITHY_CONTEXT_KEY] = {});\n", "export const normalizeProvider = (input) => {\n    if (typeof input === \"function\")\n        return input;\n    const promisified = Promise.resolve(input);\n    return () => promisified;\n};\n", "export * from \"./getSmithyContext\";\nexport * from \"./normalizeProvider\";\n", "export const resolveAuthOptions = (candidateAuthOptions, authSchemePreference) => {\n    if (!authSchemePreference || authSchemePreference.length === 0) {\n        return candidateAuthOptions;\n    }\n    const preferredAuthOptions = [];\n    for (const preferredSchemeName of authSchemePreference) {\n        for (const candidateAuthOption of candidateAuthOptions) {\n            const candidateAuthSchemeName = candidateAuthOption.schemeId.split(\"#\")[1];\n            if (candidateAuthSchemeName === preferredSchemeName) {\n                preferredAuthOptions.push(candidateAuthOption);\n            }\n        }\n    }\n    for (const candidateAuthOption of candidateAuthOptions) {\n        if (!preferredAuthOptions.find(({ schemeId }) => schemeId === candidateAuthOption.schemeId)) {\n            preferredAuthOptions.push(candidateAuthOption);\n        }\n    }\n    return preferredAuthOptions;\n};\n", "import { SMITHY_CONTEXT_KEY, } from \"@smithy/types\";\nimport { getSmithyContext } from \"@smithy/util-middleware\";\nimport { resolveAuthOptions } from \"./resolveAuthOptions\";\nfunction convertHttpAuthSchemesToMap(httpAuthSchemes) {\n    const map = new Map();\n    for (const scheme of httpAuthSchemes) {\n        map.set(scheme.schemeId, scheme);\n    }\n    return map;\n}\nexport const httpAuthSchemeMiddleware = (config, mwOptions) => (next, context) => async (args) => {\n    const options = config.httpAuthSchemeProvider(await mwOptions.httpAuthSchemeParametersProvider(config, context, args.input));\n    const authSchemePreference = config.authSchemePreference ? await config.authSchemePreference() : [];\n    const resolvedOptions = resolveAuthOptions(options, authSchemePreference);\n    const authSchemes = convertHttpAuthSchemesToMap(config.httpAuthSchemes);\n    const smithyContext = getSmithyContext(context);\n    const failureReasons = [];\n    for (const option of resolvedOptions) {\n        const scheme = authSchemes.get(option.schemeId);\n        if (!scheme) {\n            failureReasons.push(`HttpAuthScheme \\`${option.schemeId}\\` was not enabled for this service.`);\n            continue;\n        }\n        const identityProvider = scheme.identityProvider(await mwOptions.identityProviderConfigProvider(config));\n        if (!identityProvider) {\n            failureReasons.push(`HttpAuthScheme \\`${option.schemeId}\\` did not have an IdentityProvider configured.`);\n            continue;\n        }\n        const { identityProperties = {}, signingProperties = {} } = option.propertiesExtractor?.(config, context) || {};\n        option.identityProperties = Object.assign(option.identityProperties || {}, identityProperties);\n        option.signingProperties = Object.assign(option.signingProperties || {}, signingProperties);\n        smithyContext.selectedHttpAuthScheme = {\n            httpAuthOption: option,\n            identity: await identityProvider(option.identityProperties),\n            signer: scheme.signer,\n        };\n        break;\n    }\n    if (!smithyContext.selectedHttpAuthScheme) {\n        throw new Error(failureReasons.join(\"\\n\"));\n    }\n    return next(args);\n};\n", "import { httpAuthSchemeMiddleware } from \"./httpAuthSchemeMiddleware\";\nexport const httpAuthSchemeEndpointRuleSetMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"HTTP_AUTH_SCHEME\"],\n    name: \"httpAuthSchemeMiddleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: \"endpointV2Middleware\",\n};\nexport const getHttpAuthSchemeEndpointRuleSetPlugin = (config, { httpAuthSchemeParametersProvider, identityProviderConfigProvider, }) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(httpAuthSchemeMiddleware(config, {\n            httpAuthSchemeParametersProvider,\n            identityProviderConfigProvider,\n        }), httpAuthSchemeEndpointRuleSetMiddlewareOptions);\n    },\n});\n", "export const deserializerMiddleware = (options, deserializer) => (next, context) => async (args) => {\n    const { response } = await next(args);\n    try {\n        const parsed = await deserializer(response, options);\n        return {\n            response,\n            output: parsed,\n        };\n    }\n    catch (error) {\n        Object.defineProperty(error, \"$response\", {\n            value: response,\n        });\n        if (!(\"$metadata\" in error)) {\n            const hint = `Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`;\n            try {\n                error.message += \"\\n  \" + hint;\n            }\n            catch (e) {\n                if (!context.logger || context.logger?.constructor?.name === \"NoOpLogger\") {\n                    console.warn(hint);\n                }\n                else {\n                    context.logger?.warn?.(hint);\n                }\n            }\n            if (typeof error.$responseBodyText !== \"undefined\") {\n                if (error.$response) {\n                    error.$response.body = error.$responseBodyText;\n                }\n            }\n        }\n        throw error;\n    }\n};\n", "export const serializerMiddleware = (options, serializer) => (next, context) => async (args) => {\n    const endpoint = context.endpointV2?.url && options.urlParser\n        ? async () => options.urlParser(context.endpointV2.url)\n        : options.endpoint;\n    if (!endpoint) {\n        throw new Error(\"No valid endpoint provider available.\");\n    }\n    const request = await serializer(args.input, { ...options, endpoint });\n    return next({\n        ...args,\n        request,\n    });\n};\n", "import { deserializerMiddleware } from \"./deserializerMiddleware\";\nimport { serializerMiddleware } from \"./serializerMiddleware\";\nexport const deserializerMiddlewareOption = {\n    name: \"deserializerMiddleware\",\n    step: \"deserialize\",\n    tags: [\"DESERIALIZER\"],\n    override: true,\n};\nexport const serializerMiddlewareOption = {\n    name: \"serializerMiddleware\",\n    step: \"serialize\",\n    tags: [\"SERIALIZER\"],\n    override: true,\n};\nexport function getSerdePlugin(config, serializer, deserializer) {\n    return {\n        applyToStack: (commandStack) => {\n            commandStack.add(deserializerMiddleware(config, deserializer), deserializerMiddlewareOption);\n            commandStack.add(serializerMiddleware(config, serializer), serializerMiddlewareOption);\n        },\n    };\n}\n", "export * from \"./deserializerMiddleware\";\nexport * from \"./serdePlugin\";\nexport * from \"./serializerMiddleware\";\n", "export const getHttpHandlerExtensionConfiguration = (runtimeConfig) => {\n    return {\n        setHttpHandler(handler) {\n            runtimeConfig.httpHandler = handler;\n        },\n        httpHandler() {\n            return runtimeConfig.httpHandler;\n        },\n        updateHttpClientConfig(key, value) {\n            runtimeConfig.httpHandler?.updateHttpClientConfig(key, value);\n        },\n        httpHandlerConfigs() {\n            return runtimeConfig.httpHandler.httpHandlerConfigs();\n        },\n    };\n};\nexport const resolveHttpHandlerRuntimeConfig = (httpHandlerExtensionConfiguration) => {\n    return {\n        httpHandler: httpHandlerExtensionConfiguration.httpHandler(),\n    };\n};\n", "export * from \"./httpExtensionConfiguration\";\n", "import { FieldPosition } from \"@smithy/types\";\nexport class Field {\n    constructor({ name, kind = FieldPosition.HEADER, values = [] }) {\n        this.name = name;\n        this.kind = kind;\n        this.values = values;\n    }\n    add(value) {\n        this.values.push(value);\n    }\n    set(values) {\n        this.values = values;\n    }\n    remove(value) {\n        this.values = this.values.filter((v) => v !== value);\n    }\n    toString() {\n        return this.values.map((v) => (v.includes(\",\") || v.includes(\" \") ? `\"${v}\"` : v)).join(\", \");\n    }\n    get() {\n        return this.values;\n    }\n}\n", "export class Fields {\n    constructor({ fields = [], encoding = \"utf-8\" }) {\n        this.entries = {};\n        fields.forEach(this.setField.bind(this));\n        this.encoding = encoding;\n    }\n    setField(field) {\n        this.entries[field.name.toLowerCase()] = field;\n    }\n    getField(name) {\n        return this.entries[name.toLowerCase()];\n    }\n    removeField(name) {\n        delete this.entries[name.toLowerCase()];\n    }\n    getByType(kind) {\n        return Object.values(this.entries).filter((field) => field.kind === kind);\n    }\n}\n", "export {};\n", "export class HttpRequest {\n    constructor(options) {\n        this.method = options.method || \"GET\";\n        this.hostname = options.hostname || \"localhost\";\n        this.port = options.port;\n        this.query = options.query || {};\n        this.headers = options.headers || {};\n        this.body = options.body;\n        this.protocol = options.protocol\n            ? options.protocol.slice(-1) !== \":\"\n                ? `${options.protocol}:`\n                : options.protocol\n            : \"https:\";\n        this.path = options.path ? (options.path.charAt(0) !== \"/\" ? `/${options.path}` : options.path) : \"/\";\n        this.username = options.username;\n        this.password = options.password;\n        this.fragment = options.fragment;\n    }\n    static clone(request) {\n        const cloned = new HttpRequest({\n            ...request,\n            headers: { ...request.headers },\n        });\n        if (cloned.query) {\n            cloned.query = cloneQuery(cloned.query);\n        }\n        return cloned;\n    }\n    static isInstance(request) {\n        if (!request) {\n            return false;\n        }\n        const req = request;\n        return (\"method\" in req &&\n            \"protocol\" in req &&\n            \"hostname\" in req &&\n            \"path\" in req &&\n            typeof req[\"query\"] === \"object\" &&\n            typeof req[\"headers\"] === \"object\");\n    }\n    clone() {\n        return HttpRequest.clone(this);\n    }\n}\nfunction cloneQuery(query) {\n    return Object.keys(query).reduce((carry, paramName) => {\n        const param = query[paramName];\n        return {\n            ...carry,\n            [paramName]: Array.isArray(param) ? [...param] : param,\n        };\n    }, {});\n}\n", "export class HttpResponse {\n    constructor(options) {\n        this.statusCode = options.statusCode;\n        this.reason = options.reason;\n        this.headers = options.headers || {};\n        this.body = options.body;\n    }\n    static isInstance(response) {\n        if (!response)\n            return false;\n        const resp = response;\n        return typeof resp.statusCode === \"number\" && typeof resp.headers === \"object\";\n    }\n}\n", "export function isValidHostname(hostname) {\n    const hostPattern = /^[a-z0-9][a-z0-9\\.\\-]*[a-z0-9]$/;\n    return hostPattern.test(hostname);\n}\n", "export {};\n", "export * from \"./extensions\";\nexport * from \"./Field\";\nexport * from \"./Fields\";\nexport * from \"./httpHandler\";\nexport * from \"./httpRequest\";\nexport * from \"./httpResponse\";\nexport * from \"./isValidHostname\";\nexport * from \"./types\";\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { SMITHY_CONTEXT_KEY, } from \"@smithy/types\";\nimport { getSmithyContext } from \"@smithy/util-middleware\";\nconst defaultErrorHandler = (signingProperties) => (error) => {\n    throw error;\n};\nconst defaultSuccessHandler = (httpResponse, signingProperties) => { };\nexport const httpSigningMiddleware = (config) => (next, context) => async (args) => {\n    if (!HttpRequest.isInstance(args.request)) {\n        return next(args);\n    }\n    const smithyContext = getSmithyContext(context);\n    const scheme = smithyContext.selectedHttpAuthScheme;\n    if (!scheme) {\n        throw new Error(`No HttpAuthScheme was selected: unable to sign request`);\n    }\n    const { httpAuthOption: { signingProperties = {} }, identity, signer, } = scheme;\n    const output = await next({\n        ...args,\n        request: await signer.sign(args.request, identity, signingProperties),\n    }).catch((signer.errorHandler || defaultErrorHandler)(signingProperties));\n    (signer.successHandler || defaultSuccessHandler)(output.response, signingProperties);\n    return output;\n};\n", "import { httpSigningMiddleware } from \"./httpSigningMiddleware\";\nexport const httpSigningMiddlewareOptions = {\n    step: \"finalizeRequest\",\n    tags: [\"HTTP_SIGNING\"],\n    name: \"httpSigningMiddleware\",\n    aliases: [\"apiKeyMiddleware\", \"tokenMiddleware\", \"awsAuthMiddleware\"],\n    override: true,\n    relation: \"after\",\n    toMiddleware: \"retryMiddleware\",\n};\nexport const getHttpSigningPlugin = (config) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(httpSigningMiddleware(config), httpSigningMiddlewareOptions);\n    },\n});\n", "export class DefaultIdentityProviderConfig {\n    constructor(config) {\n        this.authSchemes = new Map();\n        for (const [key, value] of Object.entries(config)) {\n            if (value !== undefined) {\n                this.authSchemes.set(key, value);\n            }\n        }\n    }\n    getIdentityProvider(schemeId) {\n        return this.authSchemes.get(schemeId);\n    }\n}\n", "import { SMITHY_CONTEXT_KEY } from \"@smithy/types\";\nexport const getSmithyContext = (context) => context[SMITHY_CONTEXT_KEY] || (context[SMITHY_CONTEXT_KEY] = {});\n", "import { serializerMiddlewareOption } from \"@smithy/middleware-serde\";\nimport { httpAuthSchemeMiddleware } from \"./httpAuthSchemeMiddleware\";\nexport const httpAuthSchemeMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"HTTP_AUTH_SCHEME\"],\n    name: \"httpAuthSchemeMiddleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: serializerMiddlewareOption.name,\n};\nexport const getHttpAuthSchemePlugin = (config, { httpAuthSchemeParametersProvider, identityProviderConfigProvider, }) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(httpAuthSchemeMiddleware(config, {\n            httpAuthSchemeParametersProvider,\n            identityProviderConfigProvider,\n        }), httpAuthSchemeMiddlewareOptions);\n    },\n});\n", "export * from \"./httpAuthSchemeMiddleware\";\nexport * from \"./getHttpAuthSchemeEndpointRuleSetPlugin\";\nexport * from \"./getHttpAuthSchemePlugin\";\n", "export * from \"./httpSigningMiddleware\";\nexport * from \"./getHttpSigningMiddleware\";\n", "export const normalizeProvider = (input) => {\n    if (typeof input === \"function\")\n        return input;\n    const promisified = Promise.resolve(input);\n    return () => promisified;\n};\n", "const makePagedClientRequest = async (CommandCtor, client, input, withCommand = (_) => _, ...args) => {\n    let command = new CommandCtor(input);\n    command = withCommand(command) ?? command;\n    return await client.send(command, ...args);\n};\nexport function createPaginator(ClientCtor, CommandCtor, inputTokenName, outputTokenName, pageSizeTokenName) {\n    return async function* paginateOperation(config, input, ...additionalArguments) {\n        const _input = input;\n        let token = config.startingToken ?? _input[inputTokenName];\n        let hasNext = true;\n        let page;\n        while (hasNext) {\n            _input[inputTokenName] = token;\n            if (pageSizeTokenName) {\n                _input[pageSizeTokenName] = _input[pageSizeTokenName] ?? config.pageSize;\n            }\n            if (config.client instanceof ClientCtor) {\n                page = await makePagedClientRequest(CommandCtor, config.client, input, config.withCommand, ...additionalArguments);\n            }\n            else {\n                throw new Error(`Invalid client, expected instance of ${ClientCtor.name}`);\n            }\n            yield page;\n            const prevToken = token;\n            token = get(page, outputTokenName);\n            hasNext = !!(token && (!config.stopOnSameToken || token !== prevToken));\n        }\n        return undefined;\n    };\n}\nconst get = (fromObject, path) => {\n    let cursor = fromObject;\n    const pathComponents = path.split(\".\");\n    for (const step of pathComponents) {\n        if (!cursor || typeof cursor !== \"object\") {\n            return undefined;\n        }\n        cursor = cursor[step];\n    }\n    return cursor;\n};\n", "const alphabetByEncoding = {};\nconst alphabetByValue = new Array(64);\nfor (let i = 0, start = \"A\".charCodeAt(0), limit = \"Z\".charCodeAt(0); i + start <= limit; i++) {\n    const char = String.fromCharCode(i + start);\n    alphabetByEncoding[char] = i;\n    alphabetByValue[i] = char;\n}\nfor (let i = 0, start = \"a\".charCodeAt(0), limit = \"z\".charCodeAt(0); i + start <= limit; i++) {\n    const char = String.fromCharCode(i + start);\n    const index = i + 26;\n    alphabetByEncoding[char] = index;\n    alphabetByValue[index] = char;\n}\nfor (let i = 0; i < 10; i++) {\n    alphabetByEncoding[i.toString(10)] = i + 52;\n    const char = i.toString(10);\n    const index = i + 52;\n    alphabetByEncoding[char] = index;\n    alphabetByValue[index] = char;\n}\nalphabetByEncoding[\"+\"] = 62;\nalphabetByValue[62] = \"+\";\nalphabetByEncoding[\"/\"] = 63;\nalphabetByValue[63] = \"/\";\nconst bitsPerLetter = 6;\nconst bitsPerByte = 8;\nconst maxLetterValue = 0b111111;\nexport { alphabetByEncoding, alphabetByValue, bitsPerLetter, bitsPerByte, maxLetterValue };\n", "import { alphabetByEncoding, bitsPerByte, bitsPerLetter } from \"./constants.browser\";\nexport const fromBase64 = (input) => {\n    let totalByteLength = (input.length / 4) * 3;\n    if (input.slice(-2) === \"==\") {\n        totalByteLength -= 2;\n    }\n    else if (input.slice(-1) === \"=\") {\n        totalByteLength--;\n    }\n    const out = new ArrayBuffer(totalByteLength);\n    const dataView = new DataView(out);\n    for (let i = 0; i < input.length; i += 4) {\n        let bits = 0;\n        let bitLength = 0;\n        for (let j = i, limit = i + 3; j <= limit; j++) {\n            if (input[j] !== \"=\") {\n                if (!(input[j] in alphabetByEncoding)) {\n                    throw new TypeError(`Invalid character ${input[j]} in base64 string.`);\n                }\n                bits |= alphabetByEncoding[input[j]] << ((limit - j) * bitsPerLetter);\n                bitLength += bitsPerLetter;\n            }\n            else {\n                bits >>= bitsPerLetter;\n            }\n        }\n        const chunkOffset = (i / 4) * 3;\n        bits >>= bitLength % bitsPerByte;\n        const byteLength = Math.floor(bitLength / bitsPerByte);\n        for (let k = 0; k < byteLength; k++) {\n            const offset = (byteLength - k - 1) * bitsPerByte;\n            dataView.setUint8(chunkOffset + k, (bits & (255 << offset)) >> offset);\n        }\n    }\n    return new Uint8Array(out);\n};\n", "export const fromUtf8 = (input) => new TextEncoder().encode(input);\n", "import { fromUtf8 } from \"./fromUtf8\";\nexport const toUint8Array = (data) => {\n    if (typeof data === \"string\") {\n        return fromUtf8(data);\n    }\n    if (ArrayBuffer.isView(data)) {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n    }\n    return new Uint8Array(data);\n};\n", "export const toUtf8 = (input) => {\n    if (typeof input === \"string\") {\n        return input;\n    }\n    if (typeof input !== \"object\" || typeof input.byteOffset !== \"number\" || typeof input.byteLength !== \"number\") {\n        throw new Error(\"@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.\");\n    }\n    return new TextDecoder(\"utf-8\").decode(input);\n};\n", "export * from \"./fromUtf8\";\nexport * from \"./toUint8Array\";\nexport * from \"./toUtf8\";\n", "import { fromUtf8 } from \"@smithy/util-utf8\";\nimport { alphabetByValue, bitsPerByte, bitsPerLetter, maxLetterValue } from \"./constants.browser\";\nexport function toBase64(_input) {\n    let input;\n    if (typeof _input === \"string\") {\n        input = fromUtf8(_input);\n    }\n    else {\n        input = _input;\n    }\n    const isArrayLike = typeof input === \"object\" && typeof input.length === \"number\";\n    const isUint8Array = typeof input === \"object\" &&\n        typeof input.byteOffset === \"number\" &&\n        typeof input.byteLength === \"number\";\n    if (!isArrayLike && !isUint8Array) {\n        throw new Error(\"@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.\");\n    }\n    let str = \"\";\n    for (let i = 0; i < input.length; i += 3) {\n        let bits = 0;\n        let bitLength = 0;\n        for (let j = i, limit = Math.min(i + 3, input.length); j < limit; j++) {\n            bits |= input[j] << ((limit - j - 1) * bitsPerByte);\n            bitLength += bitsPerByte;\n        }\n        const bitClusterCount = Math.ceil(bitLength / bitsPerLetter);\n        bits <<= bitClusterCount * bitsPerLetter - bitLength;\n        for (let k = 1; k <= bitClusterCount; k++) {\n            const offset = (bitClusterCount - k) * bitsPerLetter;\n            str += alphabetByValue[(bits & (maxLetterValue << offset)) >> offset];\n        }\n        str += \"==\".slice(0, 4 - bitClusterCount);\n    }\n    return str;\n}\n", "export * from \"./fromBase64\";\nexport * from \"./toBase64\";\n", "import { fromBase64, toBase64 } from \"@smithy/util-base64\";\nimport { fromUtf8, toUtf8 } from \"@smithy/util-utf8\";\nimport { Uint8ArrayBlobAdapter } from \"./Uint8ArrayBlobAdapter\";\nexport function transformToString(payload, encoding = \"utf-8\") {\n    if (encoding === \"base64\") {\n        return toBase64(payload);\n    }\n    return toUtf8(payload);\n}\nexport function transformFromString(str, encoding) {\n    if (encoding === \"base64\") {\n        return Uint8ArrayBlobAdapter.mutate(fromBase64(str));\n    }\n    return Uint8ArrayBlobAdapter.mutate(fromUtf8(str));\n}\n", "import { transformFromString, transformToString } from \"./transforms\";\nexport class Uint8ArrayBlobAdapter extends Uint8Array {\n    static fromString(source, encoding = \"utf-8\") {\n        switch (typeof source) {\n            case \"string\":\n                return transformFromString(source, encoding);\n            default:\n                throw new Error(`Unsupported conversion from ${typeof source} to Uint8ArrayBlobAdapter.`);\n        }\n    }\n    static mutate(source) {\n        Object.setPrototypeOf(source, Uint8ArrayBlobAdapter.prototype);\n        return source;\n    }\n    transformToString(encoding = \"utf-8\") {\n        return transformToString(this, encoding);\n    }\n}\n", "const ReadableStreamRef = typeof ReadableStream === \"function\" ? ReadableStream : function () { };\nexport class ChecksumStream extends ReadableStreamRef {\n}\n", "export const isReadableStream = (stream) => typeof ReadableStream === \"function\" &&\n    (stream?.constructor?.name === ReadableStream.name || stream instanceof ReadableStream);\nexport const isBlob = (blob) => {\n    return typeof Blob === \"function\" && (blob?.constructor?.name === Blob.name || blob instanceof Blob);\n};\n", "import { toBase64 } from \"@smithy/util-base64\";\nimport { isReadableStream } from \"../stream-type-check\";\nimport { ChecksumStream } from \"./ChecksumStream.browser\";\nexport const createChecksumStream = ({ expectedChecksum, checksum, source, checksumSourceLocation, base64Encoder, }) => {\n    if (!isReadableStream(source)) {\n        throw new Error(`@smithy/util-stream: unsupported source type ${source?.constructor?.name ?? source} in ChecksumStream.`);\n    }\n    const encoder = base64Encoder ?? toBase64;\n    if (typeof TransformStream !== \"function\") {\n        throw new Error(\"@smithy/util-stream: unable to instantiate ChecksumStream because API unavailable: ReadableStream/TransformStream.\");\n    }\n    const transform = new TransformStream({\n        start() { },\n        async transform(chunk, controller) {\n            checksum.update(chunk);\n            controller.enqueue(chunk);\n        },\n        async flush(controller) {\n            const digest = await checksum.digest();\n            const received = encoder(digest);\n            if (expectedChecksum !== received) {\n                const error = new Error(`Checksum mismatch: expected \"${expectedChecksum}\" but received \"${received}\"` +\n                    ` in response header \"${checksumSourceLocation}\".`);\n                controller.error(error);\n            }\n            else {\n                controller.terminate();\n            }\n        },\n    });\n    source.pipeThrough(transform);\n    const readable = transform.readable;\n    Object.setPrototypeOf(readable, ChecksumStream.prototype);\n    return readable;\n};\n", "export class ByteArrayCollector {\n    constructor(allocByteArray) {\n        this.allocByteArray = allocByteArray;\n        this.byteLength = 0;\n        this.byteArrays = [];\n    }\n    push(byteArray) {\n        this.byteArrays.push(byteArray);\n        this.byteLength += byteArray.byteLength;\n    }\n    flush() {\n        if (this.byteArrays.length === 1) {\n            const bytes = this.byteArrays[0];\n            this.reset();\n            return bytes;\n        }\n        const aggregation = this.allocByteArray(this.byteLength);\n        let cursor = 0;\n        for (let i = 0; i < this.byteArrays.length; ++i) {\n            const bytes = this.byteArrays[i];\n            aggregation.set(bytes, cursor);\n            cursor += bytes.byteLength;\n        }\n        this.reset();\n        return aggregation;\n    }\n    reset() {\n        this.byteArrays = [];\n        this.byteLength = 0;\n    }\n}\n", "import { ByteArrayCollector } from \"./ByteArrayCollector\";\nexport function createBufferedReadableStream(upstream, size, logger) {\n    const reader = upstream.getReader();\n    let streamBufferingLoggedWarning = false;\n    let bytesSeen = 0;\n    const buffers = [\"\", new ByteArrayCollector((size) => new Uint8Array(size))];\n    let mode = -1;\n    const pull = async (controller) => {\n        const { value, done } = await reader.read();\n        const chunk = value;\n        if (done) {\n            if (mode !== -1) {\n                const remainder = flush(buffers, mode);\n                if (sizeOf(remainder) > 0) {\n                    controller.enqueue(remainder);\n                }\n            }\n            controller.close();\n        }\n        else {\n            const chunkMode = modeOf(chunk, false);\n            if (mode !== chunkMode) {\n                if (mode >= 0) {\n                    controller.enqueue(flush(buffers, mode));\n                }\n                mode = chunkMode;\n            }\n            if (mode === -1) {\n                controller.enqueue(chunk);\n                return;\n            }\n            const chunkSize = sizeOf(chunk);\n            bytesSeen += chunkSize;\n            const bufferSize = sizeOf(buffers[mode]);\n            if (chunkSize >= size && bufferSize === 0) {\n                controller.enqueue(chunk);\n            }\n            else {\n                const newSize = merge(buffers, mode, chunk);\n                if (!streamBufferingLoggedWarning && bytesSeen > size * 2) {\n                    streamBufferingLoggedWarning = true;\n                    logger?.warn(`@smithy/util-stream - stream chunk size ${chunkSize} is below threshold of ${size}, automatically buffering.`);\n                }\n                if (newSize >= size) {\n                    controller.enqueue(flush(buffers, mode));\n                }\n                else {\n                    await pull(controller);\n                }\n            }\n        }\n    };\n    return new ReadableStream({\n        pull,\n    });\n}\nexport const createBufferedReadable = createBufferedReadableStream;\nexport function merge(buffers, mode, chunk) {\n    switch (mode) {\n        case 0:\n            buffers[0] += chunk;\n            return sizeOf(buffers[0]);\n        case 1:\n        case 2:\n            buffers[mode].push(chunk);\n            return sizeOf(buffers[mode]);\n    }\n}\nexport function flush(buffers, mode) {\n    switch (mode) {\n        case 0:\n            const s = buffers[0];\n            buffers[0] = \"\";\n            return s;\n        case 1:\n        case 2:\n            return buffers[mode].flush();\n    }\n    throw new Error(`@smithy/util-stream - invalid index ${mode} given to flush()`);\n}\nexport function sizeOf(chunk) {\n    return chunk?.byteLength ?? chunk?.length ?? 0;\n}\nexport function modeOf(chunk, allowBuffer = true) {\n    if (allowBuffer && typeof Buffer !== \"undefined\" && chunk instanceof Buffer) {\n        return 2;\n    }\n    if (chunk instanceof Uint8Array) {\n        return 1;\n    }\n    if (typeof chunk === \"string\") {\n        return 0;\n    }\n    return -1;\n}\n", "export const getAwsChunkedEncodingStream = (readableStream, options) => {\n    const { base64Enco<PERSON>, bodyLength<PERSON><PERSON><PERSON>, checksumAlgorithmFn, checksumLocationName, streamHasher } = options;\n    const checksumRequired = base64Encoder !== undefined &&\n        bodyLengthChecker !== undefined &&\n        checksumAlgorithmFn !== undefined &&\n        checksumLocationName !== undefined &&\n        streamHasher !== undefined;\n    const digest = checksumRequired ? streamHasher(checksumAlgorithmFn, readableStream) : undefined;\n    const reader = readableStream.getReader();\n    return new ReadableStream({\n        async pull(controller) {\n            const { value, done } = await reader.read();\n            if (done) {\n                controller.enqueue(`0\\r\\n`);\n                if (checksumRequired) {\n                    const checksum = base64Encoder(await digest);\n                    controller.enqueue(`${checksumLocationName}:${checksum}\\r\\n`);\n                    controller.enqueue(`\\r\\n`);\n                }\n                controller.close();\n            }\n            else {\n                controller.enqueue(`${(bodyLengthChecker(value) || 0).toString(16)}\\r\\n${value}\\r\\n`);\n            }\n        },\n    });\n};\n", "export async function headStream(stream, bytes) {\n    let byteLengthCounter = 0;\n    const chunks = [];\n    const reader = stream.getReader();\n    let isDone = false;\n    while (!isDone) {\n        const { done, value } = await reader.read();\n        if (value) {\n            chunks.push(value);\n            byteLengthCounter += value?.byteLength ?? 0;\n        }\n        if (byteLengthCounter >= bytes) {\n            break;\n        }\n        isDone = done;\n    }\n    reader.releaseLock();\n    const collected = new Uint8Array(Math.min(bytes, byteLengthCounter));\n    let offset = 0;\n    for (const chunk of chunks) {\n        if (chunk.byteLength > collected.byteLength - offset) {\n            collected.set(chunk.subarray(0, collected.byteLength - offset), offset);\n            break;\n        }\n        else {\n            collected.set(chunk, offset);\n        }\n        offset += chunk.length;\n    }\n    return collected;\n}\n", "export const escapeUri = (uri) => encodeURIComponent(uri).replace(/[!'()*]/g, hexEncode);\nconst hexEncode = (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`;\n", "import { escapeUri } from \"./escape-uri\";\nexport const escapeUriPath = (uri) => uri.split(\"/\").map(escapeUri).join(\"/\");\n", "export * from \"./escape-uri\";\nexport * from \"./escape-uri-path\";\n", "import { escapeUri } from \"@smithy/util-uri-escape\";\nexport function buildQueryString(query) {\n    const parts = [];\n    for (let key of Object.keys(query).sort()) {\n        const value = query[key];\n        key = escapeUri(key);\n        if (Array.isArray(value)) {\n            for (let i = 0, iLen = value.length; i < iLen; i++) {\n                parts.push(`${key}=${escapeUri(value[i])}`);\n            }\n        }\n        else {\n            let qsEntry = key;\n            if (value || typeof value === \"string\") {\n                qsEntry += `=${escapeUri(value)}`;\n            }\n            parts.push(qsEntry);\n        }\n    }\n    return parts.join(\"&\");\n}\n", "export function createRequest(url, requestOptions) {\n    return new Request(url, requestOptions);\n}\n", "export function requestTimeout(timeoutInMs = 0) {\n    return new Promise((resolve, reject) => {\n        if (timeoutInMs) {\n            setTimeout(() => {\n                const timeoutError = new Error(`Request did not complete within ${timeoutInMs} ms`);\n                timeoutError.name = \"TimeoutError\";\n                reject(timeoutError);\n            }, timeoutInMs);\n        }\n    });\n}\n", "import { HttpResponse } from \"@smithy/protocol-http\";\nimport { buildQueryString } from \"@smithy/querystring-builder\";\nimport { createRequest } from \"./create-request\";\nimport { requestTimeout } from \"./request-timeout\";\nexport const keepAliveSupport = {\n    supported: undefined,\n};\nexport class FetchHttpHandler {\n    static create(instanceOrOptions) {\n        if (typeof instanceOrOptions?.handle === \"function\") {\n            return instanceOrOptions;\n        }\n        return new FetchHttpHandler(instanceOrOptions);\n    }\n    constructor(options) {\n        if (typeof options === \"function\") {\n            this.configProvider = options().then((opts) => opts || {});\n        }\n        else {\n            this.config = options ?? {};\n            this.configProvider = Promise.resolve(this.config);\n        }\n        if (keepAliveSupport.supported === undefined) {\n            keepAliveSupport.supported = Boolean(typeof Request !== \"undefined\" && \"keepalive\" in createRequest(\"https://[::1]\"));\n        }\n    }\n    destroy() {\n    }\n    async handle(request, { abortSignal } = {}) {\n        if (!this.config) {\n            this.config = await this.configProvider;\n        }\n        const requestTimeoutInMs = this.config.requestTimeout;\n        const keepAlive = this.config.keepAlive === true;\n        const credentials = this.config.credentials;\n        if (abortSignal?.aborted) {\n            const abortError = new Error(\"Request aborted\");\n            abortError.name = \"AbortError\";\n            return Promise.reject(abortError);\n        }\n        let path = request.path;\n        const queryString = buildQueryString(request.query || {});\n        if (queryString) {\n            path += `?${queryString}`;\n        }\n        if (request.fragment) {\n            path += `#${request.fragment}`;\n        }\n        let auth = \"\";\n        if (request.username != null || request.password != null) {\n            const username = request.username ?? \"\";\n            const password = request.password ?? \"\";\n            auth = `${username}:${password}@`;\n        }\n        const { port, method } = request;\n        const url = `${request.protocol}//${auth}${request.hostname}${port ? `:${port}` : \"\"}${path}`;\n        const body = method === \"GET\" || method === \"HEAD\" ? undefined : request.body;\n        const requestOptions = {\n            body,\n            headers: new Headers(request.headers),\n            method: method,\n            credentials,\n        };\n        if (this.config?.cache) {\n            requestOptions.cache = this.config.cache;\n        }\n        if (body) {\n            requestOptions.duplex = \"half\";\n        }\n        if (typeof AbortController !== \"undefined\") {\n            requestOptions.signal = abortSignal;\n        }\n        if (keepAliveSupport.supported) {\n            requestOptions.keepalive = keepAlive;\n        }\n        if (typeof this.config.requestInit === \"function\") {\n            Object.assign(requestOptions, this.config.requestInit(request));\n        }\n        let removeSignalEventListener = () => { };\n        const fetchRequest = createRequest(url, requestOptions);\n        const raceOfPromises = [\n            fetch(fetchRequest).then((response) => {\n                const fetchHeaders = response.headers;\n                const transformedHeaders = {};\n                for (const pair of fetchHeaders.entries()) {\n                    transformedHeaders[pair[0]] = pair[1];\n                }\n                const hasReadableStream = response.body != undefined;\n                if (!hasReadableStream) {\n                    return response.blob().then((body) => ({\n                        response: new HttpResponse({\n                            headers: transformedHeaders,\n                            reason: response.statusText,\n                            statusCode: response.status,\n                            body,\n                        }),\n                    }));\n                }\n                return {\n                    response: new HttpResponse({\n                        headers: transformedHeaders,\n                        reason: response.statusText,\n                        statusCode: response.status,\n                        body: response.body,\n                    }),\n                };\n            }),\n            requestTimeout(requestTimeoutInMs),\n        ];\n        if (abortSignal) {\n            raceOfPromises.push(new Promise((resolve, reject) => {\n                const onAbort = () => {\n                    const abortError = new Error(\"Request aborted\");\n                    abortError.name = \"AbortError\";\n                    reject(abortError);\n                };\n                if (typeof abortSignal.addEventListener === \"function\") {\n                    const signal = abortSignal;\n                    signal.addEventListener(\"abort\", onAbort, { once: true });\n                    removeSignalEventListener = () => signal.removeEventListener(\"abort\", onAbort);\n                }\n                else {\n                    abortSignal.onabort = onAbort;\n                }\n            }));\n        }\n        return Promise.race(raceOfPromises).finally(removeSignalEventListener);\n    }\n    updateHttpClientConfig(key, value) {\n        this.config = undefined;\n        this.configProvider = this.configProvider.then((config) => {\n            config[key] = value;\n            return config;\n        });\n    }\n    httpHandlerConfigs() {\n        return this.config ?? {};\n    }\n}\n", "import { fromBase64 } from \"@smithy/util-base64\";\nexport const streamCollector = async (stream) => {\n    if ((typeof Blob === \"function\" && stream instanceof Blob) || stream.constructor?.name === \"Blob\") {\n        if (Blob.prototype.arrayBuffer !== undefined) {\n            return new Uint8Array(await stream.arrayBuffer());\n        }\n        return collectBlob(stream);\n    }\n    return collectStream(stream);\n};\nasync function collectBlob(blob) {\n    const base64 = await readToBase64(blob);\n    const arrayBuffer = fromBase64(base64);\n    return new Uint8Array(arrayBuffer);\n}\nasync function collectStream(stream) {\n    const chunks = [];\n    const reader = stream.getReader();\n    let isDone = false;\n    let length = 0;\n    while (!isDone) {\n        const { done, value } = await reader.read();\n        if (value) {\n            chunks.push(value);\n            length += value.length;\n        }\n        isDone = done;\n    }\n    const collected = new Uint8Array(length);\n    let offset = 0;\n    for (const chunk of chunks) {\n        collected.set(chunk, offset);\n        offset += chunk.length;\n    }\n    return collected;\n}\nfunction readToBase64(blob) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n            if (reader.readyState !== 2) {\n                return reject(new Error(\"Reader aborted too early\"));\n            }\n            const result = (reader.result ?? \"\");\n            const commaIndex = result.indexOf(\",\");\n            const dataOffset = commaIndex > -1 ? commaIndex + 1 : result.length;\n            resolve(result.substring(dataOffset));\n        };\n        reader.onabort = () => reject(new Error(\"Read aborted\"));\n        reader.onerror = () => reject(reader.error);\n        reader.readAsDataURL(blob);\n    });\n}\n", "export * from \"./fetch-http-handler\";\nexport * from \"./stream-collector\";\n", "const SHORT_TO_HEX = {};\nconst HEX_TO_SHORT = {};\nfor (let i = 0; i < 256; i++) {\n    let encodedByte = i.toString(16).toLowerCase();\n    if (encodedByte.length === 1) {\n        encodedByte = `0${encodedByte}`;\n    }\n    SHORT_TO_HEX[i] = encodedByte;\n    HEX_TO_SHORT[encodedByte] = i;\n}\nexport function fromHex(encoded) {\n    if (encoded.length % 2 !== 0) {\n        throw new Error(\"Hex encoded strings must have an even number length\");\n    }\n    const out = new Uint8Array(encoded.length / 2);\n    for (let i = 0; i < encoded.length; i += 2) {\n        const encodedByte = encoded.slice(i, i + 2).toLowerCase();\n        if (encodedByte in HEX_TO_SHORT) {\n            out[i / 2] = HEX_TO_SHORT[encodedByte];\n        }\n        else {\n            throw new Error(`Cannot decode unrecognized sequence ${encodedByte} as hexadecimal`);\n        }\n    }\n    return out;\n}\nexport function toHex(bytes) {\n    let out = \"\";\n    for (let i = 0; i < bytes.byteLength; i++) {\n        out += SHORT_TO_HEX[bytes[i]];\n    }\n    return out;\n}\n", "import { streamCollector } from \"@smithy/fetch-http-handler\";\nimport { toBase64 } from \"@smithy/util-base64\";\nimport { toHex } from \"@smithy/util-hex-encoding\";\nimport { toUtf8 } from \"@smithy/util-utf8\";\nimport { isReadableStream } from \"./stream-type-check\";\nconst ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED = \"The stream has already been transformed.\";\nexport const sdkStreamMixin = (stream) => {\n    if (!isBlobInstance(stream) && !isReadableStream(stream)) {\n        const name = stream?.__proto__?.constructor?.name || stream;\n        throw new Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${name}`);\n    }\n    let transformed = false;\n    const transformToByteArray = async () => {\n        if (transformed) {\n            throw new Error(ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED);\n        }\n        transformed = true;\n        return await streamCollector(stream);\n    };\n    const blobToWebStream = (blob) => {\n        if (typeof blob.stream !== \"function\") {\n            throw new Error(\"Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.\\n\" +\n                \"If you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body\");\n        }\n        return blob.stream();\n    };\n    return Object.assign(stream, {\n        transformToByteArray: transformToByteArray,\n        transformToString: async (encoding) => {\n            const buf = await transformToByteArray();\n            if (encoding === \"base64\") {\n                return toBase64(buf);\n            }\n            else if (encoding === \"hex\") {\n                return toHex(buf);\n            }\n            else if (encoding === undefined || encoding === \"utf8\" || encoding === \"utf-8\") {\n                return toUtf8(buf);\n            }\n            else if (typeof TextDecoder === \"function\") {\n                return new TextDecoder(encoding).decode(buf);\n            }\n            else {\n                throw new Error(\"TextDecoder is not available, please make sure polyfill is provided.\");\n            }\n        },\n        transformToWebStream: () => {\n            if (transformed) {\n                throw new Error(ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED);\n            }\n            transformed = true;\n            if (isBlobInstance(stream)) {\n                return blobToWebStream(stream);\n            }\n            else if (isReadableStream(stream)) {\n                return stream;\n            }\n            else {\n                throw new Error(`Cannot transform payload to web stream, got ${stream}`);\n            }\n        },\n    });\n};\nconst isBlobInstance = (stream) => typeof Blob === \"function\" && stream instanceof Blob;\n", "export async function splitStream(stream) {\n    if (typeof stream.stream === \"function\") {\n        stream = stream.stream();\n    }\n    const readableStream = stream;\n    return readableStream.tee();\n}\n", "export * from \"./blob/Uint8ArrayBlobAdapter\";\nexport * from \"./checksum/ChecksumStream\";\nexport * from \"./checksum/createChecksumStream\";\nexport * from \"./createBufferedReadable\";\nexport * from \"./getAwsChunkedEncodingStream\";\nexport * from \"./headStream\";\nexport * from \"./sdk-stream-mixin\";\nexport * from \"./splitStream\";\nexport * from \"./stream-type-check\";\n", "import { Uint8ArrayBlobAdapter } from \"@smithy/util-stream\";\nexport const collectBody = async (streamBody = new Uint8Array(), context) => {\n    if (streamBody instanceof Uint8Array) {\n        return Uint8ArrayBlobAdapter.mutate(streamBody);\n    }\n    if (!streamBody) {\n        return Uint8ArrayBlobAdapter.mutate(new Uint8Array());\n    }\n    const fromContext = context.streamCollector(streamBody);\n    return Uint8ArrayBlobAdapter.mutate(await fromContext);\n};\n", "export function extendedEncodeURIComponent(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {\n        return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n    });\n}\n", "import { extendedEncodeURIComponent } from \"./extended-encode-uri-component\";\nexport const resolvedPath = (resolvedPath, input, memberName, labelValueProvider, uriLabel, isGreedyLabel) => {\n    if (input != null && input[memberName] !== undefined) {\n        const labelValue = labelValueProvider();\n        if (labelValue.length <= 0) {\n            throw new Error(\"Empty value provided for input HTTP label: \" + memberName + \".\");\n        }\n        resolvedPath = resolvedPath.replace(uriLabel, isGreedyLabel\n            ? labelValue\n                .split(\"/\")\n                .map((segment) => extendedEncodeURIComponent(segment))\n                .join(\"/\")\n            : extendedEncodeURIComponent(labelValue));\n    }\n    else {\n        throw new Error(\"No value provided for input HTTP label: \" + memberName + \".\");\n    }\n    return resolvedPath;\n};\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { resolvedPath } from \"./resolve-path\";\nexport function requestBuilder(input, context) {\n    return new RequestBuilder(input, context);\n}\nexport class RequestBuilder {\n    constructor(input, context) {\n        this.input = input;\n        this.context = context;\n        this.query = {};\n        this.method = \"\";\n        this.headers = {};\n        this.path = \"\";\n        this.body = null;\n        this.hostname = \"\";\n        this.resolvePathStack = [];\n    }\n    async build() {\n        const { hostname, protocol = \"https\", port, path: basePath } = await this.context.endpoint();\n        this.path = basePath;\n        for (const resolvePath of this.resolvePathStack) {\n            resolvePath(this.path);\n        }\n        return new HttpRequest({\n            protocol,\n            hostname: this.hostname || hostname,\n            port,\n            method: this.method,\n            path: this.path,\n            query: this.query,\n            body: this.body,\n            headers: this.headers,\n        });\n    }\n    hn(hostname) {\n        this.hostname = hostname;\n        return this;\n    }\n    bp(uriLabel) {\n        this.resolvePathStack.push((basePath) => {\n            this.path = `${basePath?.endsWith(\"/\") ? basePath.slice(0, -1) : basePath || \"\"}` + uriLabel;\n        });\n        return this;\n    }\n    p(memberName, labelValueProvider, uriLabel, isGreedyLabel) {\n        this.resolvePathStack.push((path) => {\n            this.path = resolvedPath(path, this.input, memberName, labelValueProvider, uriLabel, isGreedyLabel);\n        });\n        return this;\n    }\n    h(headers) {\n        this.headers = headers;\n        return this;\n    }\n    q(query) {\n        this.query = query;\n        return this;\n    }\n    b(body) {\n        this.body = body;\n        return this;\n    }\n    m(method) {\n        this.method = method;\n        return this;\n    }\n}\n", "export * from \"./collect-stream-body\";\nexport * from \"./extended-encode-uri-component\";\nexport * from \"./requestBuilder\";\nexport * from \"./resolve-path\";\n", "export { requestBuilder } from \"@smithy/core/protocols\";\n", "export function setFeature(context, feature, value) {\n    if (!context.__smithy_context) {\n        context.__smithy_context = {\n            features: {},\n        };\n    }\n    else if (!context.__smithy_context.features) {\n        context.__smithy_context.features = {};\n    }\n    context.__smithy_context.features[feature] = value;\n}\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { HttpApiKeyAuthLocation } from \"@smithy/types\";\nexport class HttpApiKeyAuthSigner {\n    async sign(httpRequest, identity, signingProperties) {\n        if (!signingProperties) {\n            throw new Error(\"request could not be signed with `api<PERSON><PERSON>` since the `name` and `in` signer properties are missing\");\n        }\n        if (!signingProperties.name) {\n            throw new Error(\"request could not be signed with `api<PERSON><PERSON>` since the `name` signer property is missing\");\n        }\n        if (!signingProperties.in) {\n            throw new Error(\"request could not be signed with `api<PERSON>ey` since the `in` signer property is missing\");\n        }\n        if (!identity.apiKey) {\n            throw new Error(\"request could not be signed with `api<PERSON><PERSON>` since the `apiKey` is not defined\");\n        }\n        const clonedRequest = HttpRequest.clone(httpRequest);\n        if (signingProperties.in === HttpApiKeyAuthLocation.QUERY) {\n            clonedRequest.query[signingProperties.name] = identity.apiKey;\n        }\n        else if (signingProperties.in === HttpApiKeyAuthLocation.HEADER) {\n            clonedRequest.headers[signingProperties.name] = signingProperties.scheme\n                ? `${signingProperties.scheme} ${identity.apiKey}`\n                : identity.apiKey;\n        }\n        else {\n            throw new Error(\"request can only be signed with `apiKey` locations `query` or `header`, \" +\n                \"but found: `\" +\n                signingProperties.in +\n                \"`\");\n        }\n        return clonedRequest;\n    }\n}\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nexport class HttpBearerAuthSigner {\n    async sign(httpRequest, identity, signingProperties) {\n        const clonedRequest = HttpRequest.clone(httpRequest);\n        if (!identity.token) {\n            throw new Error(\"request could not be signed with `token` since the `token` is not defined\");\n        }\n        clonedRequest.headers[\"Authorization\"] = `Bearer ${identity.token}`;\n        return clonedRequest;\n    }\n}\n", "export class NoAuthSigner {\n    async sign(httpRequest, identity, signingProperties) {\n        return httpRequest;\n    }\n}\n", "export * from \"./httpApiKeyAuth\";\nexport * from \"./httpBearerAuth\";\nexport * from \"./noAuth\";\n", "export const createIsIdentityExpiredFunction = (expirationMs) => (identity) => doesIdentityRequireRefresh(identity) && identity.expiration.getTime() - Date.now() < expirationMs;\nexport const EXPIRATION_MS = 300000;\nexport const isIdentityExpired = createIsIdentityExpiredFunction(EXPIRATION_MS);\nexport const doesIdentityRequireRefresh = (identity) => identity.expiration !== undefined;\nexport const memoizeIdentityProvider = (provider, isExpired, requiresRefresh) => {\n    if (provider === undefined) {\n        return undefined;\n    }\n    const normalizedProvider = typeof provider !== \"function\" ? async () => Promise.resolve(provider) : provider;\n    let resolved;\n    let pending;\n    let hasResult;\n    let isConstant = false;\n    const coalesceProvider = async (options) => {\n        if (!pending) {\n            pending = normalizedProvider(options);\n        }\n        try {\n            resolved = await pending;\n            hasResult = true;\n            isConstant = false;\n        }\n        finally {\n            pending = undefined;\n        }\n        return resolved;\n    };\n    if (isExpired === undefined) {\n        return async (options) => {\n            if (!hasResult || options?.forceRefresh) {\n                resolved = await coalesceProvider(options);\n            }\n            return resolved;\n        };\n    }\n    return async (options) => {\n        if (!hasResult || options?.forceRefresh) {\n            resolved = await coalesceProvider(options);\n        }\n        if (isConstant) {\n            return resolved;\n        }\n        if (!requiresRefresh(resolved)) {\n            isConstant = true;\n            return resolved;\n        }\n        if (isExpired(resolved)) {\n            await coalesceProvider(options);\n            return resolved;\n        }\n        return resolved;\n    };\n};\n", "export * from \"./DefaultIdentityProviderConfig\";\nexport * from \"./httpAuthSchemes\";\nexport * from \"./memoizeIdentityProvider\";\n", "export * from \"./getSmithyContext\";\nexport * from \"./middleware-http-auth-scheme\";\nexport * from \"./middleware-http-signing\";\nexport * from \"./normalizeProvider\";\nexport { createPaginator } from \"./pagination/createPaginator\";\nexport * from \"./protocols/requestBuilder\";\nexport * from \"./setFeature\";\nexport * from \"./util-identity-and-auth\";\n", "export const SENSITIVE_STRING = \"***SensitiveInformation***\";\n", "export const createAggregatedClient = (commands, Client) => {\n    for (const command of Object.keys(commands)) {\n        const CommandCtor = commands[command];\n        const methodImpl = async function (args, optionsOrCb, cb) {\n            const command = new CommandCtor(args);\n            if (typeof optionsOrCb === \"function\") {\n                this.send(command, optionsOrCb);\n            }\n            else if (typeof cb === \"function\") {\n                if (typeof optionsOrCb !== \"object\")\n                    throw new Error(`Expected http options but got ${typeof optionsOrCb}`);\n                this.send(command, optionsOrCb || {}, cb);\n            }\n            else {\n                return this.send(command, optionsOrCb);\n            }\n        };\n        const methodName = (command[0].toLowerCase() + command.slice(1)).replace(/Command$/, \"\");\n        Client.prototype[methodName] = methodImpl;\n    }\n};\n", "export class ServiceException extends Error {\n    constructor(options) {\n        super(options.message);\n        Object.setPrototypeOf(this, Object.getPrototypeOf(this).constructor.prototype);\n        this.name = options.name;\n        this.$fault = options.$fault;\n        this.$metadata = options.$metadata;\n    }\n    static isInstance(value) {\n        if (!value)\n            return false;\n        const candidate = value;\n        return (ServiceException.prototype.isPrototypeOf(candidate) ||\n            (<PERSON><PERSON><PERSON>(candidate.$fault) &&\n                <PERSON><PERSON><PERSON>(candidate.$metadata) &&\n                (candidate.$fault === \"client\" || candidate.$fault === \"server\")));\n    }\n    static [Symbol.hasInstance](instance) {\n        if (!instance)\n            return false;\n        const candidate = instance;\n        if (this === ServiceException) {\n            return ServiceException.isInstance(instance);\n        }\n        if (ServiceException.isInstance(instance)) {\n            if (candidate.name && this.name) {\n                return this.prototype.isPrototypeOf(instance) || candidate.name === this.name;\n            }\n            return this.prototype.isPrototypeOf(instance);\n        }\n        return false;\n    }\n}\nexport const decorateServiceException = (exception, additions = {}) => {\n    Object.entries(additions)\n        .filter(([, v]) => v !== undefined)\n        .forEach(([k, v]) => {\n        if (exception[k] == undefined || exception[k] === \"\") {\n            exception[k] = v;\n        }\n    });\n    const message = exception.message || exception.Message || \"UnknownError\";\n    exception.message = message;\n    delete exception.Message;\n    return exception;\n};\n", "export { collectBody } from \"@smithy/core/protocols\";\n", "export const parseBoolean = (value) => {\n    switch (value) {\n        case \"true\":\n            return true;\n        case \"false\":\n            return false;\n        default:\n            throw new Error(`Unable to parse boolean value \"${value}\"`);\n    }\n};\nexport const expectBoolean = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"number\") {\n        if (value === 0 || value === 1) {\n            logger.warn(stackTraceWarning(`Expected boolean, got ${typeof value}: ${value}`));\n        }\n        if (value === 0) {\n            return false;\n        }\n        if (value === 1) {\n            return true;\n        }\n    }\n    if (typeof value === \"string\") {\n        const lower = value.toLowerCase();\n        if (lower === \"false\" || lower === \"true\") {\n            logger.warn(stackTraceWarning(`Expected boolean, got ${typeof value}: ${value}`));\n        }\n        if (lower === \"false\") {\n            return false;\n        }\n        if (lower === \"true\") {\n            return true;\n        }\n    }\n    if (typeof value === \"boolean\") {\n        return value;\n    }\n    throw new TypeError(`Expected boolean, got ${typeof value}: ${value}`);\n};\nexport const expectNumber = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"string\") {\n        const parsed = parseFloat(value);\n        if (!Number.isNaN(parsed)) {\n            if (String(parsed) !== String(value)) {\n                logger.warn(stackTraceWarning(`Expected number but observed string: ${value}`));\n            }\n            return parsed;\n        }\n    }\n    if (typeof value === \"number\") {\n        return value;\n    }\n    throw new TypeError(`Expected number, got ${typeof value}: ${value}`);\n};\nconst MAX_FLOAT = Math.ceil(2 ** 127 * (2 - 2 ** -23));\nexport const expectFloat32 = (value) => {\n    const expected = expectNumber(value);\n    if (expected !== undefined && !Number.isNaN(expected) && expected !== Infinity && expected !== -Infinity) {\n        if (Math.abs(expected) > MAX_FLOAT) {\n            throw new TypeError(`Expected 32-bit float, got ${value}`);\n        }\n    }\n    return expected;\n};\nexport const expectLong = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (Number.isInteger(value) && !Number.isNaN(value)) {\n        return value;\n    }\n    throw new TypeError(`Expected integer, got ${typeof value}: ${value}`);\n};\nexport const expectInt = expectLong;\nexport const expectInt32 = (value) => expectSizedInt(value, 32);\nexport const expectShort = (value) => expectSizedInt(value, 16);\nexport const expectByte = (value) => expectSizedInt(value, 8);\nconst expectSizedInt = (value, size) => {\n    const expected = expectLong(value);\n    if (expected !== undefined && castInt(expected, size) !== expected) {\n        throw new TypeError(`Expected ${size}-bit integer, got ${value}`);\n    }\n    return expected;\n};\nconst castInt = (value, size) => {\n    switch (size) {\n        case 32:\n            return Int32Array.of(value)[0];\n        case 16:\n            return Int16Array.of(value)[0];\n        case 8:\n            return Int8Array.of(value)[0];\n    }\n};\nexport const expectNonNull = (value, location) => {\n    if (value === null || value === undefined) {\n        if (location) {\n            throw new TypeError(`Expected a non-null value for ${location}`);\n        }\n        throw new TypeError(\"Expected a non-null value\");\n    }\n    return value;\n};\nexport const expectObject = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"object\" && !Array.isArray(value)) {\n        return value;\n    }\n    const receivedType = Array.isArray(value) ? \"array\" : typeof value;\n    throw new TypeError(`Expected object, got ${receivedType}: ${value}`);\n};\nexport const expectString = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"string\") {\n        return value;\n    }\n    if ([\"boolean\", \"number\", \"bigint\"].includes(typeof value)) {\n        logger.warn(stackTraceWarning(`Expected string, got ${typeof value}: ${value}`));\n        return String(value);\n    }\n    throw new TypeError(`Expected string, got ${typeof value}: ${value}`);\n};\nexport const expectUnion = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    const asObject = expectObject(value);\n    const setKeys = Object.entries(asObject)\n        .filter(([, v]) => v != null)\n        .map(([k]) => k);\n    if (setKeys.length === 0) {\n        throw new TypeError(`Unions must have exactly one non-null member. None were found.`);\n    }\n    if (setKeys.length > 1) {\n        throw new TypeError(`Unions must have exactly one non-null member. Keys ${setKeys} were not null.`);\n    }\n    return asObject;\n};\nexport const strictParseDouble = (value) => {\n    if (typeof value == \"string\") {\n        return expectNumber(parseNumber(value));\n    }\n    return expectNumber(value);\n};\nexport const strictParseFloat = strictParseDouble;\nexport const strictParseFloat32 = (value) => {\n    if (typeof value == \"string\") {\n        return expectFloat32(parseNumber(value));\n    }\n    return expectFloat32(value);\n};\nconst NUMBER_REGEX = /(-?(?:0|[1-9]\\d*)(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)|(-?Infinity)|(NaN)/g;\nconst parseNumber = (value) => {\n    const matches = value.match(NUMBER_REGEX);\n    if (matches === null || matches[0].length !== value.length) {\n        throw new TypeError(`Expected real number, got implicit NaN`);\n    }\n    return parseFloat(value);\n};\nexport const limitedParseDouble = (value) => {\n    if (typeof value == \"string\") {\n        return parseFloatString(value);\n    }\n    return expectNumber(value);\n};\nexport const handleFloat = limitedParseDouble;\nexport const limitedParseFloat = limitedParseDouble;\nexport const limitedParseFloat32 = (value) => {\n    if (typeof value == \"string\") {\n        return parseFloatString(value);\n    }\n    return expectFloat32(value);\n};\nconst parseFloatString = (value) => {\n    switch (value) {\n        case \"NaN\":\n            return NaN;\n        case \"Infinity\":\n            return Infinity;\n        case \"-Infinity\":\n            return -Infinity;\n        default:\n            throw new Error(`Unable to parse float value: ${value}`);\n    }\n};\nexport const strictParseLong = (value) => {\n    if (typeof value === \"string\") {\n        return expectLong(parseNumber(value));\n    }\n    return expectLong(value);\n};\nexport const strictParseInt = strictParseLong;\nexport const strictParseInt32 = (value) => {\n    if (typeof value === \"string\") {\n        return expectInt32(parseNumber(value));\n    }\n    return expectInt32(value);\n};\nexport const strictParseShort = (value) => {\n    if (typeof value === \"string\") {\n        return expectShort(parseNumber(value));\n    }\n    return expectShort(value);\n};\nexport const strictParseByte = (value) => {\n    if (typeof value === \"string\") {\n        return expectByte(parseNumber(value));\n    }\n    return expectByte(value);\n};\nconst stackTraceWarning = (message) => {\n    return String(new TypeError(message).stack || message)\n        .split(\"\\n\")\n        .slice(0, 5)\n        .filter((s) => !s.includes(\"stackTraceWarning\"))\n        .join(\"\\n\");\n};\nexport const logger = {\n    warn: console.warn,\n};\n", "import { strictParseByte, strictParseDouble, strictParseFloat32, strictParseShort } from \"./parse-utils\";\nconst DAYS = [\"<PERSON>\", \"Mon\", \"<PERSON><PERSON>\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"];\nconst MONTHS = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\nexport function dateToUtcString(date) {\n    const year = date.getUTCFullYear();\n    const month = date.getUTCMonth();\n    const dayOfWeek = date.getUTCDay();\n    const dayOfMonthInt = date.getUTCDate();\n    const hoursInt = date.getUTCHours();\n    const minutesInt = date.getUTCMinutes();\n    const secondsInt = date.getUTCSeconds();\n    const dayOfMonthString = dayOfMonthInt < 10 ? `0${dayOfMonthInt}` : `${dayOfMonthInt}`;\n    const hoursString = hoursInt < 10 ? `0${hoursInt}` : `${hoursInt}`;\n    const minutesString = minutesInt < 10 ? `0${minutesInt}` : `${minutesInt}`;\n    const secondsString = secondsInt < 10 ? `0${secondsInt}` : `${secondsInt}`;\n    return `${DAYS[dayOfWeek]}, ${dayOfMonthString} ${MONTHS[month]} ${year} ${hoursString}:${minutesString}:${secondsString} GMT`;\n}\nconst RFC3339 = new RegExp(/^(\\d{4})-(\\d{2})-(\\d{2})[tT](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))?[zZ]$/);\nexport const parseRfc3339DateTime = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value !== \"string\") {\n        throw new TypeError(\"RFC-3339 date-times must be expressed as strings\");\n    }\n    const match = RFC3339.exec(value);\n    if (!match) {\n        throw new TypeError(\"Invalid RFC-3339 date-time value\");\n    }\n    const [_, yearStr, monthStr, dayStr, hours, minutes, seconds, fractionalMilliseconds] = match;\n    const year = strictParseShort(stripLeadingZeroes(yearStr));\n    const month = parseDateValue(monthStr, \"month\", 1, 12);\n    const day = parseDateValue(dayStr, \"day\", 1, 31);\n    return buildDate(year, month, day, { hours, minutes, seconds, fractionalMilliseconds });\n};\nconst RFC3339_WITH_OFFSET = new RegExp(/^(\\d{4})-(\\d{2})-(\\d{2})[tT](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))?(([-+]\\d{2}\\:\\d{2})|[zZ])$/);\nexport const parseRfc3339DateTimeWithOffset = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value !== \"string\") {\n        throw new TypeError(\"RFC-3339 date-times must be expressed as strings\");\n    }\n    const match = RFC3339_WITH_OFFSET.exec(value);\n    if (!match) {\n        throw new TypeError(\"Invalid RFC-3339 date-time value\");\n    }\n    const [_, yearStr, monthStr, dayStr, hours, minutes, seconds, fractionalMilliseconds, offsetStr] = match;\n    const year = strictParseShort(stripLeadingZeroes(yearStr));\n    const month = parseDateValue(monthStr, \"month\", 1, 12);\n    const day = parseDateValue(dayStr, \"day\", 1, 31);\n    const date = buildDate(year, month, day, { hours, minutes, seconds, fractionalMilliseconds });\n    if (offsetStr.toUpperCase() != \"Z\") {\n        date.setTime(date.getTime() - parseOffsetToMilliseconds(offsetStr));\n    }\n    return date;\n};\nconst IMF_FIXDATE = new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d{1,2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))? GMT$/);\nconst RFC_850_DATE = new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d{2}) (\\d{1,2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))? GMT$/);\nconst ASC_TIME = new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\\d{2}) (\\d{1,2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))? (\\d{4})$/);\nexport const parseRfc7231DateTime = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value !== \"string\") {\n        throw new TypeError(\"RFC-7231 date-times must be expressed as strings\");\n    }\n    let match = IMF_FIXDATE.exec(value);\n    if (match) {\n        const [_, dayStr, monthStr, yearStr, hours, minutes, seconds, fractionalMilliseconds] = match;\n        return buildDate(strictParseShort(stripLeadingZeroes(yearStr)), parseMonthByShortName(monthStr), parseDateValue(dayStr, \"day\", 1, 31), { hours, minutes, seconds, fractionalMilliseconds });\n    }\n    match = RFC_850_DATE.exec(value);\n    if (match) {\n        const [_, dayStr, monthStr, yearStr, hours, minutes, seconds, fractionalMilliseconds] = match;\n        return adjustRfc850Year(buildDate(parseTwoDigitYear(yearStr), parseMonthByShortName(monthStr), parseDateValue(dayStr, \"day\", 1, 31), {\n            hours,\n            minutes,\n            seconds,\n            fractionalMilliseconds,\n        }));\n    }\n    match = ASC_TIME.exec(value);\n    if (match) {\n        const [_, monthStr, dayStr, hours, minutes, seconds, fractionalMilliseconds, yearStr] = match;\n        return buildDate(strictParseShort(stripLeadingZeroes(yearStr)), parseMonthByShortName(monthStr), parseDateValue(dayStr.trimLeft(), \"day\", 1, 31), { hours, minutes, seconds, fractionalMilliseconds });\n    }\n    throw new TypeError(\"Invalid RFC-7231 date-time value\");\n};\nexport const parseEpochTimestamp = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    let valueAsDouble;\n    if (typeof value === \"number\") {\n        valueAsDouble = value;\n    }\n    else if (typeof value === \"string\") {\n        valueAsDouble = strictParseDouble(value);\n    }\n    else if (typeof value === \"object\" && value.tag === 1) {\n        valueAsDouble = value.value;\n    }\n    else {\n        throw new TypeError(\"Epoch timestamps must be expressed as floating point numbers or their string representation\");\n    }\n    if (Number.isNaN(valueAsDouble) || valueAsDouble === Infinity || valueAsDouble === -Infinity) {\n        throw new TypeError(\"Epoch timestamps must be valid, non-Infinite, non-NaN numerics\");\n    }\n    return new Date(Math.round(valueAsDouble * 1000));\n};\nconst buildDate = (year, month, day, time) => {\n    const adjustedMonth = month - 1;\n    validateDayOfMonth(year, adjustedMonth, day);\n    return new Date(Date.UTC(year, adjustedMonth, day, parseDateValue(time.hours, \"hour\", 0, 23), parseDateValue(time.minutes, \"minute\", 0, 59), parseDateValue(time.seconds, \"seconds\", 0, 60), parseMilliseconds(time.fractionalMilliseconds)));\n};\nconst parseTwoDigitYear = (value) => {\n    const thisYear = new Date().getUTCFullYear();\n    const valueInThisCentury = Math.floor(thisYear / 100) * 100 + strictParseShort(stripLeadingZeroes(value));\n    if (valueInThisCentury < thisYear) {\n        return valueInThisCentury + 100;\n    }\n    return valueInThisCentury;\n};\nconst FIFTY_YEARS_IN_MILLIS = 50 * 365 * 24 * 60 * 60 * 1000;\nconst adjustRfc850Year = (input) => {\n    if (input.getTime() - new Date().getTime() > FIFTY_YEARS_IN_MILLIS) {\n        return new Date(Date.UTC(input.getUTCFullYear() - 100, input.getUTCMonth(), input.getUTCDate(), input.getUTCHours(), input.getUTCMinutes(), input.getUTCSeconds(), input.getUTCMilliseconds()));\n    }\n    return input;\n};\nconst parseMonthByShortName = (value) => {\n    const monthIdx = MONTHS.indexOf(value);\n    if (monthIdx < 0) {\n        throw new TypeError(`Invalid month: ${value}`);\n    }\n    return monthIdx + 1;\n};\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst validateDayOfMonth = (year, month, day) => {\n    let maxDays = DAYS_IN_MONTH[month];\n    if (month === 1 && isLeapYear(year)) {\n        maxDays = 29;\n    }\n    if (day > maxDays) {\n        throw new TypeError(`Invalid day for ${MONTHS[month]} in ${year}: ${day}`);\n    }\n};\nconst isLeapYear = (year) => {\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n};\nconst parseDateValue = (value, type, lower, upper) => {\n    const dateVal = strictParseByte(stripLeadingZeroes(value));\n    if (dateVal < lower || dateVal > upper) {\n        throw new TypeError(`${type} must be between ${lower} and ${upper}, inclusive`);\n    }\n    return dateVal;\n};\nconst parseMilliseconds = (value) => {\n    if (value === null || value === undefined) {\n        return 0;\n    }\n    return strictParseFloat32(\"0.\" + value) * 1000;\n};\nconst parseOffsetToMilliseconds = (value) => {\n    const directionStr = value[0];\n    let direction = 1;\n    if (directionStr == \"+\") {\n        direction = 1;\n    }\n    else if (directionStr == \"-\") {\n        direction = -1;\n    }\n    else {\n        throw new TypeError(`Offset direction, ${directionStr}, must be \"+\" or \"-\"`);\n    }\n    const hour = Number(value.substring(1, 3));\n    const minute = Number(value.substring(4, 6));\n    return direction * (hour * 60 + minute) * 60 * 1000;\n};\nconst stripLeadingZeroes = (value) => {\n    let idx = 0;\n    while (idx < value.length - 1 && value.charAt(idx) === \"0\") {\n        idx++;\n    }\n    if (idx === 0) {\n        return value;\n    }\n    return value.slice(idx);\n};\n", "import { decorateServiceException } from \"./exceptions\";\nexport const throwDefaultError = ({ output, parsedBody, exceptionCtor, errorCode }) => {\n    const $metadata = deserializeMetadata(output);\n    const statusCode = $metadata.httpStatusCode ? $metadata.httpStatusCode + \"\" : undefined;\n    const response = new exceptionCtor({\n        name: parsedBody?.code || parsedBody?.Code || errorCode || statusCode || \"UnknownError\",\n        $fault: \"client\",\n        $metadata,\n    });\n    throw decorateServiceException(response, parsedBody);\n};\nexport const withBaseException = (ExceptionCtor) => {\n    return ({ output, parsedBody, errorCode }) => {\n        throwDefaultError({ output, parsedBody, exceptionCtor: ExceptionCtor, errorCode });\n    };\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\n", "export const loadConfigsForDefaultMode = (mode) => {\n    switch (mode) {\n        case \"standard\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 3100,\n            };\n        case \"in-region\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 1100,\n            };\n        case \"cross-region\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 3100,\n            };\n        case \"mobile\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 30000,\n            };\n        default:\n            return {};\n    }\n};\n", "let warningEmitted = false;\nexport const emitWarningIfUnsupportedVersion = (version) => {\n    if (version && !warningEmitted && parseInt(version.substring(1, version.indexOf(\".\"))) < 16) {\n        warningEmitted = true;\n    }\n};\n", "export { extendedEncodeURIComponent } from \"@smithy/core/protocols\";\n", "import { AlgorithmId } from \"@smithy/types\";\nexport { AlgorithmId };\nexport const getChecksumConfiguration = (runtimeConfig) => {\n    const checksumAlgorithms = [];\n    for (const id in AlgorithmId) {\n        const algorithmId = AlgorithmId[id];\n        if (runtimeConfig[algorithmId] === undefined) {\n            continue;\n        }\n        checksumAlgorithms.push({\n            algorithmId: () => algorithmId,\n            checksumConstructor: () => runtimeConfig[algorithmId],\n        });\n    }\n    return {\n        addChecksumAlgorithm(algo) {\n            checksumAlgorithms.push(algo);\n        },\n        checksumAlgorithms() {\n            return checksumAlgorithms;\n        },\n    };\n};\nexport const resolveChecksumRuntimeConfig = (clientConfig) => {\n    const runtimeConfig = {};\n    clientConfig.checksumAlgorithms().forEach((checksumAlgorithm) => {\n        runtimeConfig[checksumAlgorithm.algorithmId()] = checksumAlgorithm.checksumConstructor();\n    });\n    return runtimeConfig;\n};\n", "export const getRetryConfiguration = (runtimeConfig) => {\n    return {\n        setRetryStrategy(retryStrategy) {\n            runtimeConfig.retryStrategy = retryStrategy;\n        },\n        retryStrategy() {\n            return runtimeConfig.retryStrategy;\n        },\n    };\n};\nexport const resolveRetryRuntimeConfig = (retryStrategyConfiguration) => {\n    const runtimeConfig = {};\n    runtimeConfig.retryStrategy = retryStrategyConfiguration.retryStrategy();\n    return runtimeConfig;\n};\n", "import { getChecksumConfiguration, resolveChecksumRuntimeConfig } from \"./checksum\";\nimport { getRetryConfiguration, resolveRetryRuntimeConfig } from \"./retry\";\nexport const getDefaultExtensionConfiguration = (runtimeConfig) => {\n    return Object.assign(getChecksumConfiguration(runtimeConfig), getRetryConfiguration(runtimeConfig));\n};\nexport const getDefaultClientConfiguration = getDefaultExtensionConfiguration;\nexport const resolveDefaultRuntimeConfig = (config) => {\n    return Object.assign(resolveChecksumRuntimeConfig(config), resolveRetryRuntimeConfig(config));\n};\n", "export * from \"./defaultExtensionConfiguration\";\n", "export const getArrayIfSingleItem = (mayBeArray) => Array.isArray(mayBeArray) ? mayBeArray : [mayBeArray];\n", "export const getValueFromTextNode = (obj) => {\n    const textNodeName = \"#text\";\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && obj[key][textNodeName] !== undefined) {\n            obj[key] = obj[key][textNodeName];\n        }\n        else if (typeof obj[key] === \"object\" && obj[key] !== null) {\n            obj[key] = getValueFromTextNode(obj[key]);\n        }\n    }\n    return obj;\n};\n", "export const isSerializableHeaderValue = (value) => {\n    return value != null;\n};\n", "export const LazyJsonString = function LazyJsonString(val) {\n    const str = Object.assign(new String(val), {\n        deserializeJSON() {\n            return JSON.parse(String(val));\n        },\n        toString() {\n            return String(val);\n        },\n        toJSON() {\n            return String(val);\n        },\n    });\n    return str;\n};\nLazyJsonString.from = (object) => {\n    if (object && typeof object === \"object\" && (object instanceof LazyJsonString || \"deserializeJSON\" in object)) {\n        return object;\n    }\n    else if (typeof object === \"string\" || Object.getPrototypeOf(object) === String.prototype) {\n        return LazyJsonString(String(object));\n    }\n    return LazyJsonString(JSON.stringify(object));\n};\nLazyJsonString.fromObject = LazyJsonString.from;\n", "export class NoOpLogger {\n    trace() { }\n    debug() { }\n    info() { }\n    warn() { }\n    error() { }\n}\n", "export function map(arg0, arg1, arg2) {\n    let target;\n    let filter;\n    let instructions;\n    if (typeof arg1 === \"undefined\" && typeof arg2 === \"undefined\") {\n        target = {};\n        instructions = arg0;\n    }\n    else {\n        target = arg0;\n        if (typeof arg1 === \"function\") {\n            filter = arg1;\n            instructions = arg2;\n            return mapWithFilter(target, filter, instructions);\n        }\n        else {\n            instructions = arg1;\n        }\n    }\n    for (const key of Object.keys(instructions)) {\n        if (!Array.isArray(instructions[key])) {\n            target[key] = instructions[key];\n            continue;\n        }\n        applyInstruction(target, null, instructions, key);\n    }\n    return target;\n}\nexport const convertMap = (target) => {\n    const output = {};\n    for (const [k, v] of Object.entries(target || {})) {\n        output[k] = [, v];\n    }\n    return output;\n};\nexport const take = (source, instructions) => {\n    const out = {};\n    for (const key in instructions) {\n        applyInstruction(out, source, instructions, key);\n    }\n    return out;\n};\nconst mapWithFilter = (target, filter, instructions) => {\n    return map(target, Object.entries(instructions).reduce((_instructions, [key, value]) => {\n        if (Array.isArray(value)) {\n            _instructions[key] = value;\n        }\n        else {\n            if (typeof value === \"function\") {\n                _instructions[key] = [filter, value()];\n            }\n            else {\n                _instructions[key] = [filter, value];\n            }\n        }\n        return _instructions;\n    }, {}));\n};\nconst applyInstruction = (target, source, instructions, targetKey) => {\n    if (source !== null) {\n        let instruction = instructions[targetKey];\n        if (typeof instruction === \"function\") {\n            instruction = [, instruction];\n        }\n        const [filter = nonNullish, valueFn = pass, sourceKey = targetKey] = instruction;\n        if ((typeof filter === \"function\" && filter(source[sourceKey])) || (typeof filter !== \"function\" && !!filter)) {\n            target[targetKey] = valueFn(source[sourceKey]);\n        }\n        return;\n    }\n    let [filter, value] = instructions[targetKey];\n    if (typeof value === \"function\") {\n        let _value;\n        const defaultFilterPassed = filter === undefined && (_value = value()) != null;\n        const customFilterPassed = (typeof filter === \"function\" && !!filter(void 0)) || (typeof filter !== \"function\" && !!filter);\n        if (defaultFilterPassed) {\n            target[targetKey] = _value;\n        }\n        else if (customFilterPassed) {\n            target[targetKey] = value();\n        }\n    }\n    else {\n        const defaultFilterPassed = filter === undefined && value != null;\n        const customFilterPassed = (typeof filter === \"function\" && !!filter(value)) || (typeof filter !== \"function\" && !!filter);\n        if (defaultFilterPassed || customFilterPassed) {\n            target[targetKey] = value;\n        }\n    }\n};\nconst nonNullish = (_) => _ != null;\nconst pass = (_) => _;\n", "export function quoteHeader(part) {\n    if (part.includes(\",\") || part.includes('\"')) {\n        part = `\"${part.replace(/\"/g, '\\\\\"')}\"`;\n    }\n    return part;\n}\n", "export { resolvedPath } from \"@smithy/core/protocols\";\n", "export const serializeFloat = (value) => {\n    if (value !== value) {\n        return \"NaN\";\n    }\n    switch (value) {\n        case Infinity:\n            return \"Infinity\";\n        case -Infinity:\n            return \"-Infinity\";\n        default:\n            return value;\n    }\n};\nexport const serializeDateTime = (date) => date.toISOString().replace(\".000Z\", \"Z\");\n", "export const _json = (obj) => {\n    if (obj == null) {\n        return {};\n    }\n    if (Array.isArray(obj)) {\n        return obj.filter((_) => _ != null).map(_json);\n    }\n    if (typeof obj === \"object\") {\n        const target = {};\n        for (const key of Object.keys(obj)) {\n            if (obj[key] == null) {\n                continue;\n            }\n            target[key] = _json(obj[key]);\n        }\n        return target;\n    }\n    return obj;\n};\n", "export function splitEvery(value, delimiter, numDelimiters) {\n    if (numDelimiters <= 0 || !Number.isInteger(numDelimiters)) {\n        throw new Error(\"Invalid number of delimiters (\" + numDelimiters + \") for splitEvery.\");\n    }\n    const segments = value.split(delimiter);\n    if (numDelimiters === 1) {\n        return segments;\n    }\n    const compoundSegments = [];\n    let currentSegment = \"\";\n    for (let i = 0; i < segments.length; i++) {\n        if (currentSegment === \"\") {\n            currentSegment = segments[i];\n        }\n        else {\n            currentSegment += delimiter + segments[i];\n        }\n        if ((i + 1) % numDelimiters === 0) {\n            compoundSegments.push(currentSegment);\n            currentSegment = \"\";\n        }\n    }\n    if (currentSegment !== \"\") {\n        compoundSegments.push(currentSegment);\n    }\n    return compoundSegments;\n}\n", "export const splitHeader = (value) => {\n    const z = value.length;\n    const values = [];\n    let withinQuotes = false;\n    let prevChar = undefined;\n    let anchor = 0;\n    for (let i = 0; i < z; ++i) {\n        const char = value[i];\n        switch (char) {\n            case `\"`:\n                if (prevChar !== \"\\\\\") {\n                    withinQuotes = !withinQuotes;\n                }\n                break;\n            case \",\":\n                if (!withinQuotes) {\n                    values.push(value.slice(anchor, i));\n                    anchor = i + 1;\n                }\n                break;\n            default:\n        }\n        prevChar = char;\n    }\n    values.push(value.slice(anchor));\n    return values.map((v) => {\n        v = v.trim();\n        const z = v.length;\n        if (z < 2) {\n            return v;\n        }\n        if (v[0] === `\"` && v[z - 1] === `\"`) {\n            v = v.slice(1, z - 1);\n        }\n        return v.replace(/\\\\\"/g, '\"');\n    });\n};\n", "export * from \"./client\";\nexport * from \"./collect-stream-body\";\nexport * from \"./command\";\nexport * from \"./constants\";\nexport * from \"./create-aggregated-client\";\nexport * from \"./date-utils\";\nexport * from \"./default-error-handler\";\nexport * from \"./defaults-mode\";\nexport * from \"./emitWarningIfUnsupportedVersion\";\nexport * from \"./exceptions\";\nexport * from \"./extended-encode-uri-component\";\nexport * from \"./extensions\";\nexport * from \"./get-array-if-single-item\";\nexport * from \"./get-value-from-text-node\";\nexport * from \"./is-serializable-header-value\";\nexport * from \"./lazy-json\";\nexport * from \"./NoOpLogger\";\nexport * from \"./object-mapping\";\nexport * from \"./parse-utils\";\nexport * from \"./quote-header\";\nexport * from \"./resolve-path\";\nexport * from \"./ser-utils\";\nexport * from \"./serde-json\";\nexport * from \"./split-every\";\nexport * from \"./split-header\";\n", "/**\n * @internal\n */\nexport const BLOCK_SIZE: number = 64;\n\n/**\n * @internal\n */\nexport const DIGEST_LENGTH: number = 32;\n\n/**\n * @internal\n */\nexport const KEY = new Uint32Array([\n  0x428a2f98,\n  0x71374491,\n  0xb5c0fbcf,\n  0xe9b5dba5,\n  0x3956c25b,\n  0x59f111f1,\n  0x923f82a4,\n  0xab1c5ed5,\n  0xd807aa98,\n  0x12835b01,\n  0x243185be,\n  0x550c7dc3,\n  0x72be5d74,\n  0x80deb1fe,\n  0x9bdc06a7,\n  0xc19bf174,\n  0xe49b69c1,\n  0xefbe4786,\n  0x0fc19dc6,\n  0x240ca1cc,\n  0x2de92c6f,\n  0x4a7484aa,\n  0x5cb0a9dc,\n  0x76f988da,\n  0x983e5152,\n  0xa831c66d,\n  0xb00327c8,\n  0xbf597fc7,\n  0xc6e00bf3,\n  0xd5a79147,\n  0x06ca6351,\n  0x14292967,\n  0x27b70a85,\n  0x2e1b2138,\n  0x4d2c6dfc,\n  0x53380d13,\n  0x650a7354,\n  0x766a0abb,\n  0x81c2c92e,\n  0x92722c85,\n  0xa2bfe8a1,\n  0xa81a664b,\n  0xc24b8b70,\n  0xc76c51a3,\n  0xd192e819,\n  0xd6990624,\n  0xf40e3585,\n  0x106aa070,\n  0x19a4c116,\n  0x1e376c08,\n  0x2748774c,\n  0x34b0bcb5,\n  0x391c0cb3,\n  0x4ed8aa4a,\n  0x5b9cca4f,\n  0x682e6ff3,\n  0x748f82ee,\n  0x78a5636f,\n  0x84c87814,\n  0x8cc70208,\n  0x90befffa,\n  0xa4506ceb,\n  0xbef9a3f7,\n  0xc67178f2\n]);\n\n/**\n * @internal\n */\nexport const INIT = [\n  0x6a09e667,\n  0xbb67ae85,\n  0x3c6ef372,\n  0xa54ff53a,\n  0x510e527f,\n  0x9b05688c,\n  0x1f83d9ab,\n  0x5be0cd19\n];\n\n/**\n * @internal\n */\nexport const MAX_HASHABLE_LENGTH = 2 ** 53 - 1;\n", "import {\n  BLOCK_SIZE,\n  DIGEST_LENGTH,\n  INIT,\n  KEY,\n  MAX_HASHABLE_LENGTH\n} from \"./constants\";\n\n/**\n * @internal\n */\nexport class RawSha256 {\n  private state: Int32Array = Int32Array.from(INIT);\n  private temp: Int32Array = new Int32Array(64);\n  private buffer: Uint8Array = new Uint8Array(64);\n  private bufferLength: number = 0;\n  private bytesHashed: number = 0;\n\n  /**\n   * @internal\n   */\n  finished: boolean = false;\n\n  update(data: Uint8Array): void {\n    if (this.finished) {\n      throw new Error(\"Attempted to update an already finished hash.\");\n    }\n\n    let position = 0;\n    let { byteLength } = data;\n    this.bytesHashed += byteLength;\n\n    if (this.bytesHashed * 8 > MAX_HASHABLE_LENGTH) {\n      throw new Error(\"Cannot hash more than 2^53 - 1 bits\");\n    }\n\n    while (byteLength > 0) {\n      this.buffer[this.bufferLength++] = data[position++];\n      byteLength--;\n\n      if (this.bufferLength === BLOCK_SIZE) {\n        this.hashBuffer();\n        this.bufferLength = 0;\n      }\n    }\n  }\n\n  digest(): Uint8Array {\n    if (!this.finished) {\n      const bitsHashed = this.bytesHashed * 8;\n      const bufferView = new DataView(\n        this.buffer.buffer,\n        this.buffer.byteOffset,\n        this.buffer.byteLength\n      );\n\n      const undecoratedLength = this.bufferLength;\n      bufferView.setUint8(this.bufferLength++, 0x80);\n\n      // Ensure the final block has enough room for the hashed length\n      if (undecoratedLength % BLOCK_SIZE >= BLOCK_SIZE - 8) {\n        for (let i = this.bufferLength; i < BLOCK_SIZE; i++) {\n          bufferView.setUint8(i, 0);\n        }\n        this.hashBuffer();\n        this.bufferLength = 0;\n      }\n\n      for (let i = this.bufferLength; i < BLOCK_SIZE - 8; i++) {\n        bufferView.setUint8(i, 0);\n      }\n      bufferView.setUint32(\n        BLOCK_SIZE - 8,\n        Math.floor(bitsHashed / 0x100000000),\n        true\n      );\n      bufferView.setUint32(BLOCK_SIZE - 4, bitsHashed);\n\n      this.hashBuffer();\n\n      this.finished = true;\n    }\n\n    // The value in state is little-endian rather than big-endian, so flip\n    // each word into a new Uint8Array\n    const out = new Uint8Array(DIGEST_LENGTH);\n    for (let i = 0; i < 8; i++) {\n      out[i * 4] = (this.state[i] >>> 24) & 0xff;\n      out[i * 4 + 1] = (this.state[i] >>> 16) & 0xff;\n      out[i * 4 + 2] = (this.state[i] >>> 8) & 0xff;\n      out[i * 4 + 3] = (this.state[i] >>> 0) & 0xff;\n    }\n\n    return out;\n  }\n\n  private hashBuffer(): void {\n    const { buffer, state } = this;\n\n    let state0 = state[0],\n      state1 = state[1],\n      state2 = state[2],\n      state3 = state[3],\n      state4 = state[4],\n      state5 = state[5],\n      state6 = state[6],\n      state7 = state[7];\n\n    for (let i = 0; i < BLOCK_SIZE; i++) {\n      if (i < 16) {\n        this.temp[i] =\n          ((buffer[i * 4] & 0xff) << 24) |\n          ((buffer[i * 4 + 1] & 0xff) << 16) |\n          ((buffer[i * 4 + 2] & 0xff) << 8) |\n          (buffer[i * 4 + 3] & 0xff);\n      } else {\n        let u = this.temp[i - 2];\n        const t1 =\n          ((u >>> 17) | (u << 15)) ^ ((u >>> 19) | (u << 13)) ^ (u >>> 10);\n\n        u = this.temp[i - 15];\n        const t2 =\n          ((u >>> 7) | (u << 25)) ^ ((u >>> 18) | (u << 14)) ^ (u >>> 3);\n\n        this.temp[i] =\n          ((t1 + this.temp[i - 7]) | 0) + ((t2 + this.temp[i - 16]) | 0);\n      }\n\n      const t1 =\n        ((((((state4 >>> 6) | (state4 << 26)) ^\n          ((state4 >>> 11) | (state4 << 21)) ^\n          ((state4 >>> 25) | (state4 << 7))) +\n          ((state4 & state5) ^ (~state4 & state6))) |\n          0) +\n          ((state7 + ((KEY[i] + this.temp[i]) | 0)) | 0)) |\n        0;\n\n      const t2 =\n        ((((state0 >>> 2) | (state0 << 30)) ^\n          ((state0 >>> 13) | (state0 << 19)) ^\n          ((state0 >>> 22) | (state0 << 10))) +\n          ((state0 & state1) ^ (state0 & state2) ^ (state1 & state2))) |\n        0;\n\n      state7 = state6;\n      state6 = state5;\n      state5 = state4;\n      state4 = (state3 + t1) | 0;\n      state3 = state2;\n      state2 = state1;\n      state1 = state0;\n      state0 = (t1 + t2) | 0;\n    }\n\n    state[0] += state0;\n    state[1] += state1;\n    state[2] += state2;\n    state[3] += state3;\n    state[4] += state4;\n    state[5] += state5;\n    state[6] += state6;\n    state[7] += state7;\n  }\n}\n", "export const fromUtf8 = (input) => new TextEncoder().encode(input);\n", "import { fromUtf8 } from \"./fromUtf8\";\nexport const toUint8Array = (data) => {\n    if (typeof data === \"string\") {\n        return fromUtf8(data);\n    }\n    if (ArrayBuffer.isView(data)) {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n    }\n    return new Uint8Array(data);\n};\n", "export const toUtf8 = (input) => {\n    if (typeof input === \"string\") {\n        return input;\n    }\n    if (typeof input !== \"object\" || typeof input.byteOffset !== \"number\" || typeof input.byteLength !== \"number\") {\n        throw new Error(\"@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.\");\n    }\n    return new TextDecoder(\"utf-8\").decode(input);\n};\n", "export * from \"./fromUtf8\";\nexport * from \"./toUint8Array\";\nexport * from \"./toUtf8\";\n", "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { SourceData } from \"@aws-sdk/types\";\nimport { fromUtf8 as fromUtf8Browser } from \"@smithy/util-utf8\";\n\n// Quick polyfill\nconst fromUtf8 =\n  typeof Buffer !== \"undefined\" && Buffer.from\n    ? (input: string) => Buffer.from(input, \"utf8\")\n    : fromUtf8Browser;\n\nexport function convertToBuffer(data: SourceData): Uint8Array {\n  // Already a Uint8, do nothing\n  if (data instanceof Uint8Array) return data;\n\n  if (typeof data === \"string\") {\n    return fromUtf8(data);\n  }\n\n  if (ArrayBuffer.isView(data)) {\n    return new Uint8Array(\n      data.buffer,\n      data.byteOffset,\n      data.byteLength / Uint8Array.BYTES_PER_ELEMENT\n    );\n  }\n\n  return new Uint8Array(data);\n}\n", "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { SourceData } from \"@aws-sdk/types\";\n\nexport function isEmptyData(data: SourceData): boolean {\n  if (typeof data === \"string\") {\n    return data.length === 0;\n  }\n\n  return data.byteLength === 0;\n}\n", "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nexport function numToUint8(num: number) {\n  return new Uint8Array([\n    (num & 0xff000000) >> 24,\n    (num & 0x00ff0000) >> 16,\n    (num & 0x0000ff00) >> 8,\n    num & 0x000000ff,\n  ]);\n}\n", "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\n// IE 11 does not support Array.from, so we do it manually\nexport function uint32ArrayFrom(a_lookUpTable: Array<number>): Uint32Array {\n  if (!Uint32Array.from) {\n    const return_array = new Uint32Array(a_lookUpTable.length)\n    let a_index = 0\n    while (a_index < a_lookUpTable.length) {\n      return_array[a_index] = a_lookUpTable[a_index]\n      a_index += 1\n    }\n    return return_array\n  }\n  return Uint32Array.from(a_lookUpTable)\n}\n", "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nexport { convertToBuffer } from \"./convertToBuffer\";\nexport { isEmptyData } from \"./isEmptyData\";\nexport { numToUint8 } from \"./numToUint8\";\nexport {uint32ArrayFrom} from './uint32ArrayFrom';\n", "import { BLOCK_SIZE } from \"./constants\";\nimport { RawSha256 } from \"./RawSha256\";\nimport { Checksum, SourceData } from \"@aws-sdk/types\";\nimport { isEmptyData, convertToBuffer } from \"@aws-crypto/util\";\n\nexport class Sha256 implements Checksum {\n  private readonly secret?: SourceData;\n  private hash: RawSha256;\n  private outer?: RawSha256;\n  private error: any;\n\n  constructor(secret?: SourceData) {\n    this.secret = secret;\n    this.hash = new RawSha256();\n    this.reset();\n  }\n\n  update(toHash: SourceData): void {\n    if (isEmptyData(toHash) || this.error) {\n      return;\n    }\n\n    try {\n      this.hash.update(convertToBuffer(toHash));\n    } catch (e) {\n      this.error = e;\n    }\n  }\n\n  /* This synchronous method keeps compatibility\n   * with the v2 aws-sdk.\n   */\n  digestSync(): Uint8Array {\n    if (this.error) {\n      throw this.error;\n    }\n\n    if (this.outer) {\n      if (!this.outer.finished) {\n        this.outer.update(this.hash.digest());\n      }\n\n      return this.outer.digest();\n    }\n\n    return this.hash.digest();\n  }\n\n  /* The underlying digest method here is synchronous.\n   * To keep the same interface with the other hash functions\n   * the default is to expose this as an async method.\n   * However, it can sometimes be useful to have a sync method.\n   */\n  async digest(): Promise<Uint8Array> {\n    return this.digestSync();\n  }\n\n  reset(): void {\n    this.hash = new RawSha256();\n    if (this.secret) {\n      this.outer = new RawSha256();\n      const inner = bufferFromSecret(this.secret);\n      const outer = new Uint8Array(BLOCK_SIZE);\n      outer.set(inner);\n\n      for (let i = 0; i < BLOCK_SIZE; i++) {\n        inner[i] ^= 0x36;\n        outer[i] ^= 0x5c;\n      }\n\n      this.hash.update(inner);\n      this.outer.update(outer);\n\n      // overwrite the copied key in memory\n      for (let i = 0; i < inner.byteLength; i++) {\n        inner[i] = 0;\n      }\n    }\n  }\n}\n\nfunction bufferFromSecret(secret: SourceData): Uint8Array {\n  let input = convertToBuffer(secret);\n\n  if (input.byteLength > BLOCK_SIZE) {\n    const bufferHash = new RawSha256();\n    bufferHash.update(input);\n    input = bufferHash.digest();\n  }\n\n  const buffer = new Uint8Array(BLOCK_SIZE);\n  buffer.set(input);\n  return buffer;\n}\n", "export * from \"./jsSha256\";\n", "export const memoize = (provider, isExpired, requiresRefresh) => {\n    let resolved;\n    let pending;\n    let hasResult;\n    let isConstant = false;\n    const coalesceProvider = async () => {\n        if (!pending) {\n            pending = provider();\n        }\n        try {\n            resolved = await pending;\n            hasResult = true;\n            isConstant = false;\n        }\n        finally {\n            pending = undefined;\n        }\n        return resolved;\n    };\n    if (isExpired === undefined) {\n        return async (options) => {\n            if (!hasResult || options?.forceRefresh) {\n                resolved = await coalesceProvider();\n            }\n            return resolved;\n        };\n    }\n    return async (options) => {\n        if (!hasResult || options?.forceRefresh) {\n            resolved = await coalesceProvider();\n        }\n        if (isConstant) {\n            return resolved;\n        }\n        if (requiresRefresh && !requiresRefresh(resolved)) {\n            isConstant = true;\n            return resolved;\n        }\n        if (isExpired(resolved)) {\n            await coalesceProvider();\n            return resolved;\n        }\n        return resolved;\n    };\n};\n", "export class ProviderError extends Error {\n    constructor(message, options = true) {\n        let logger;\n        let tryNextLink = true;\n        if (typeof options === \"boolean\") {\n            logger = undefined;\n            tryNextLink = options;\n        }\n        else if (options != null && typeof options === \"object\") {\n            logger = options.logger;\n            tryNextLink = options.tryNextLink ?? true;\n        }\n        super(message);\n        this.name = \"ProviderError\";\n        this.tryNextLink = tryNextLink;\n        Object.setPrototypeOf(this, ProviderError.prototype);\n        logger?.debug?.(`@smithy/property-provider ${tryNextLink ? \"->\" : \"(!)\"} ${message}`);\n    }\n    static from(error, options = true) {\n        return Object.assign(new this(error.message, options), error);\n    }\n}\n", "import { ProviderError } from \"./ProviderError\";\nexport class CredentialsProviderError extends ProviderError {\n    constructor(message, options = true) {\n        super(message, options);\n        this.name = \"CredentialsProviderError\";\n        Object.setPrototypeOf(this, CredentialsProviderError.prototype);\n    }\n}\n", "import { ProviderError } from \"./ProviderError\";\nexport class TokenProviderError extends ProviderError {\n    constructor(message, options = true) {\n        super(message, options);\n        this.name = \"TokenProviderError\";\n        Object.setPrototypeOf(this, TokenProviderError.prototype);\n    }\n}\n", "import { ProviderError } from \"./ProviderError\";\nexport const chain = (...providers) => async () => {\n    if (providers.length === 0) {\n        throw new ProviderError(\"No providers in chain\");\n    }\n    let lastProviderError;\n    for (const provider of providers) {\n        try {\n            const credentials = await provider();\n            return credentials;\n        }\n        catch (err) {\n            lastProviderError = err;\n            if (err?.tryNextLink) {\n                continue;\n            }\n            throw err;\n        }\n    }\n    throw lastProviderError;\n};\n", "export const fromStatic = (staticValue) => () => Promise.resolve(staticValue);\n", "export * from \"./CredentialsProviderError\";\nexport * from \"./ProviderError\";\nexport * from \"./TokenProviderError\";\nexport * from \"./chain\";\nexport * from \"./fromStatic\";\nexport * from \"./memoize\";\n"], "mappings": ";;;;;;;;;;;AAAA,IAAM,eAYA,8BAGO,gBA8PP,aAOA;AApRN;AAAA;AAAA,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACrC,YAAM,WAAW,CAAC;AAClB,UAAI,MAAM;AACN,iBAAS,KAAK,IAAI;AAAA,MACtB;AACA,UAAI,SAAS;AACT,mBAAW,SAAS,SAAS;AACzB,mBAAS,KAAK,KAAK;AAAA,QACvB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,+BAA+B,CAAC,MAAM,YAAY;AACpD,aAAO,GAAG,QAAQ,WAAW,GAAG,WAAW,QAAQ,SAAS,IAAI,YAAY,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE;AAAA,IACzG;AACO,IAAM,iBAAiB,MAAM;AAChC,UAAI,kBAAkB,CAAC;AACvB,UAAI,kBAAkB,CAAC;AACvB,UAAI,oBAAoB;AACxB,YAAM,iBAAiB,oBAAI,IAAI;AAC/B,YAAM,OAAO,CAAC,YAAY,QAAQ,KAAK,CAAC,GAAG,MAAM,YAAY,EAAE,IAAI,IAAI,YAAY,EAAE,IAAI,KACrF,gBAAgB,EAAE,YAAY,QAAQ,IAAI,gBAAgB,EAAE,YAAY,QAAQ,CAAC;AACrF,YAAM,eAAe,CAAC,aAAa;AAC/B,YAAI,YAAY;AAChB,cAAM,WAAW,CAAC,UAAU;AACxB,gBAAM,UAAU,cAAc,MAAM,MAAM,MAAM,OAAO;AACvD,cAAI,QAAQ,SAAS,QAAQ,GAAG;AAC5B,wBAAY;AACZ,uBAAW,SAAS,SAAS;AACzB,6BAAe,OAAO,KAAK;AAAA,YAC/B;AACA,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AACA,0BAAkB,gBAAgB,OAAO,QAAQ;AACjD,0BAAkB,gBAAgB,OAAO,QAAQ;AACjD,eAAO;AAAA,MACX;AACA,YAAM,oBAAoB,CAAC,aAAa;AACpC,YAAI,YAAY;AAChB,cAAM,WAAW,CAAC,UAAU;AACxB,cAAI,MAAM,eAAe,UAAU;AAC/B,wBAAY;AACZ,uBAAW,SAAS,cAAc,MAAM,MAAM,MAAM,OAAO,GAAG;AAC1D,6BAAe,OAAO,KAAK;AAAA,YAC/B;AACA,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AACA,0BAAkB,gBAAgB,OAAO,QAAQ;AACjD,0BAAkB,gBAAgB,OAAO,QAAQ;AACjD,eAAO;AAAA,MACX;AACA,YAAM,UAAU,CAAC,YAAY;AAvDjC;AAwDQ,wBAAgB,QAAQ,CAAC,UAAU;AAC/B,kBAAQ,IAAI,MAAM,YAAY,EAAE,GAAG,MAAM,CAAC;AAAA,QAC9C,CAAC;AACD,wBAAgB,QAAQ,CAAC,UAAU;AAC/B,kBAAQ,cAAc,MAAM,YAAY,EAAE,GAAG,MAAM,CAAC;AAAA,QACxD,CAAC;AACD,sBAAQ,sBAAR,iCAA4B,MAAM,kBAAkB;AACpD,eAAO;AAAA,MACX;AACA,YAAM,+BAA+B,CAAC,SAAS;AAC3C,cAAM,yBAAyB,CAAC;AAChC,aAAK,OAAO,QAAQ,CAAC,UAAU;AAC3B,cAAI,MAAM,OAAO,WAAW,KAAK,MAAM,MAAM,WAAW,GAAG;AACvD,mCAAuB,KAAK,KAAK;AAAA,UACrC,OACK;AACD,mCAAuB,KAAK,GAAG,6BAA6B,KAAK,CAAC;AAAA,UACtE;AAAA,QACJ,CAAC;AACD,+BAAuB,KAAK,IAAI;AAChC,aAAK,MAAM,QAAQ,EAAE,QAAQ,CAAC,UAAU;AACpC,cAAI,MAAM,OAAO,WAAW,KAAK,MAAM,MAAM,WAAW,GAAG;AACvD,mCAAuB,KAAK,KAAK;AAAA,UACrC,OACK;AACD,mCAAuB,KAAK,GAAG,6BAA6B,KAAK,CAAC;AAAA,UACtE;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AACA,YAAM,oBAAoB,CAAC,QAAQ,UAAU;AACzC,cAAM,4BAA4B,CAAC;AACnC,cAAM,4BAA4B,CAAC;AACnC,cAAM,2BAA2B,CAAC;AAClC,wBAAgB,QAAQ,CAAC,UAAU;AAC/B,gBAAM,kBAAkB;AAAA,YACpB,GAAG;AAAA,YACH,QAAQ,CAAC;AAAA,YACT,OAAO,CAAC;AAAA,UACZ;AACA,qBAAW,SAAS,cAAc,gBAAgB,MAAM,gBAAgB,OAAO,GAAG;AAC9E,qCAAyB,KAAK,IAAI;AAAA,UACtC;AACA,oCAA0B,KAAK,eAAe;AAAA,QAClD,CAAC;AACD,wBAAgB,QAAQ,CAAC,UAAU;AAC/B,gBAAM,kBAAkB;AAAA,YACpB,GAAG;AAAA,YACH,QAAQ,CAAC;AAAA,YACT,OAAO,CAAC;AAAA,UACZ;AACA,qBAAW,SAAS,cAAc,gBAAgB,MAAM,gBAAgB,OAAO,GAAG;AAC9E,qCAAyB,KAAK,IAAI;AAAA,UACtC;AACA,oCAA0B,KAAK,eAAe;AAAA,QAClD,CAAC;AACD,kCAA0B,QAAQ,CAAC,UAAU;AACzC,cAAI,MAAM,cAAc;AACpB,kBAAM,eAAe,yBAAyB,MAAM,YAAY;AAChE,gBAAI,iBAAiB,QAAW;AAC5B,kBAAI,OAAO;AACP;AAAA,cACJ;AACA,oBAAM,IAAI,MAAM,GAAG,MAAM,YAAY,6BAC9B,6BAA6B,MAAM,MAAM,MAAM,OAAO,CAAC,eAC5C,MAAM,QAAQ,IAAI,MAAM,YAAY,EAAE;AAAA,YAC5D;AACA,gBAAI,MAAM,aAAa,SAAS;AAC5B,2BAAa,MAAM,KAAK,KAAK;AAAA,YACjC;AACA,gBAAI,MAAM,aAAa,UAAU;AAC7B,2BAAa,OAAO,KAAK,KAAK;AAAA,YAClC;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,cAAM,YAAY,KAAK,yBAAyB,EAC3C,IAAI,4BAA4B,EAChC,OAAO,CAAC,WAAW,2BAA2B;AAC/C,oBAAU,KAAK,GAAG,sBAAsB;AACxC,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AACL,eAAO;AAAA,MACX;AACA,YAAM,QAAQ;AAAA,QACV,KAAK,CAAC,YAAY,UAAU,CAAC,MAAM;AAC/B,gBAAM,EAAE,MAAM,UAAU,SAAS,SAAS,IAAI;AAC9C,gBAAM,QAAQ;AAAA,YACV,MAAM;AAAA,YACN,UAAU;AAAA,YACV;AAAA,YACA,GAAG;AAAA,UACP;AACA,gBAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAI,QAAQ,SAAS,GAAG;AACpB,gBAAI,QAAQ,KAAK,CAAC,UAAU,eAAe,IAAI,KAAK,CAAC,GAAG;AACpD,kBAAI,CAAC;AACD,sBAAM,IAAI,MAAM,8BAA8B,6BAA6B,MAAM,QAAQ,CAAC,GAAG;AACjG,yBAAW,SAAS,SAAS;AACzB,sBAAM,kBAAkB,gBAAgB,UAAU,CAACA,WAAO;AA1JlF;AA0JqF,yBAAAA,OAAM,SAAS,WAAS,KAAAA,OAAM,YAAN,mBAAe,KAAK,CAAC,MAAM,MAAM;AAAA,iBAAM;AAC5H,oBAAI,oBAAoB,IAAI;AACxB;AAAA,gBACJ;AACA,sBAAM,aAAa,gBAAgB,eAAe;AAClD,oBAAI,WAAW,SAAS,MAAM,QAAQ,MAAM,aAAa,WAAW,UAAU;AAC1E,wBAAM,IAAI,MAAM,IAAI,6BAA6B,WAAW,MAAM,WAAW,OAAO,CAAC,qBAC9E,WAAW,QAAQ,gBAAgB,WAAW,IAAI,kCAChC,6BAA6B,MAAM,QAAQ,CAAC,qBAC9D,MAAM,QAAQ,gBAAgB,MAAM,IAAI,QAAQ;AAAA,gBAC3D;AACA,gCAAgB,OAAO,iBAAiB,CAAC;AAAA,cAC7C;AAAA,YACJ;AACA,uBAAW,SAAS,SAAS;AACzB,6BAAe,IAAI,KAAK;AAAA,YAC5B;AAAA,UACJ;AACA,0BAAgB,KAAK,KAAK;AAAA,QAC9B;AAAA,QACA,eAAe,CAAC,YAAY,YAAY;AACpC,gBAAM,EAAE,MAAM,UAAU,SAAS,SAAS,IAAI;AAC9C,gBAAM,QAAQ;AAAA,YACV;AAAA,YACA,GAAG;AAAA,UACP;AACA,gBAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAI,QAAQ,SAAS,GAAG;AACpB,gBAAI,QAAQ,KAAK,CAAC,UAAU,eAAe,IAAI,KAAK,CAAC,GAAG;AACpD,kBAAI,CAAC;AACD,sBAAM,IAAI,MAAM,8BAA8B,6BAA6B,MAAM,QAAQ,CAAC,GAAG;AACjG,yBAAW,SAAS,SAAS;AACzB,sBAAM,kBAAkB,gBAAgB,UAAU,CAACA,WAAO;AA1LlF;AA0LqF,yBAAAA,OAAM,SAAS,WAAS,KAAAA,OAAM,YAAN,mBAAe,KAAK,CAAC,MAAM,MAAM;AAAA,iBAAM;AAC5H,oBAAI,oBAAoB,IAAI;AACxB;AAAA,gBACJ;AACA,sBAAM,aAAa,gBAAgB,eAAe;AAClD,oBAAI,WAAW,iBAAiB,MAAM,gBAAgB,WAAW,aAAa,MAAM,UAAU;AAC1F,wBAAM,IAAI,MAAM,IAAI,6BAA6B,WAAW,MAAM,WAAW,OAAO,CAAC,gBAC9E,WAAW,QAAQ,KAAK,WAAW,YAAY,yCAC3C,6BAA6B,MAAM,QAAQ,CAAC,gBAAgB,MAAM,QAAQ,KAC7E,MAAM,YAAY,eAAe;AAAA,gBAC7C;AACA,gCAAgB,OAAO,iBAAiB,CAAC;AAAA,cAC7C;AAAA,YACJ;AACA,uBAAW,SAAS,SAAS;AACzB,6BAAe,IAAI,KAAK;AAAA,YAC5B;AAAA,UACJ;AACA,0BAAgB,KAAK,KAAK;AAAA,QAC9B;AAAA,QACA,OAAO,MAAM,QAAQ,eAAe,CAAC;AAAA,QACrC,KAAK,CAAC,WAAW;AACb,iBAAO,aAAa,KAAK;AAAA,QAC7B;AAAA,QACA,QAAQ,CAAC,aAAa;AAClB,cAAI,OAAO,aAAa;AACpB,mBAAO,aAAa,QAAQ;AAAA;AAE5B,mBAAO,kBAAkB,QAAQ;AAAA,QACzC;AAAA,QACA,aAAa,CAAC,aAAa;AACvB,cAAI,YAAY;AAChB,gBAAM,WAAW,CAAC,UAAU;AACxB,kBAAM,EAAE,MAAM,MAAM,SAAS,SAAS,IAAI;AAC1C,gBAAI,QAAQ,KAAK,SAAS,QAAQ,GAAG;AACjC,oBAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,yBAAW,SAAS,SAAS;AACzB,+BAAe,OAAO,KAAK;AAAA,cAC/B;AACA,0BAAY;AACZ,qBAAO;AAAA,YACX;AACA,mBAAO;AAAA,UACX;AACA,4BAAkB,gBAAgB,OAAO,QAAQ;AACjD,4BAAkB,gBAAgB,OAAO,QAAQ;AACjD,iBAAO;AAAA,QACX;AAAA,QACA,QAAQ,CAAC,SAAS;AA1O1B;AA2OY,gBAAM,SAAS,QAAQ,eAAe,CAAC;AACvC,iBAAO,IAAI,IAAI;AACf,iBAAO,kBAAkB,qBAAqB,OAAO,kBAAkB,QAAM,UAAK,sBAAL,kCAA8B,MAAM;AACjH,iBAAO;AAAA,QACX;AAAA,QACA,cAAc;AAAA,QACd,UAAU,MAAM;AACZ,iBAAO,kBAAkB,IAAI,EAAE,IAAI,CAAC,OAAO;AACvC,kBAAM,OAAO,GAAG,QACZ,GAAG,WACC,MACA,GAAG;AACX,mBAAO,6BAA6B,GAAG,MAAM,GAAG,OAAO,IAAI,QAAQ;AAAA,UACvE,CAAC;AAAA,QACL;AAAA,QACA,kBAAkB,QAAQ;AACtB,cAAI,OAAO,WAAW;AAClB,gCAAoB;AACxB,iBAAO;AAAA,QACX;AAAA,QACA,SAAS,CAAC,SAAS,YAAY;AAC3B,qBAAW,cAAc,kBAAkB,EACtC,IAAI,CAAC,UAAU,MAAM,UAAU,EAC/B,QAAQ,GAAG;AACZ,sBAAU,WAAW,SAAS,OAAO;AAAA,UACzC;AACA,cAAI,mBAAmB;AACnB,oBAAQ,IAAI,MAAM,SAAS,CAAC;AAAA,UAChC;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,cAAc;AAAA,MAChB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACjB;AACA,IAAM,kBAAkB;AAAA,MACpB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,KAAK;AAAA,IACT;AAAA;AAAA;;;ACxRA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IACa;AADb;AAAA;AAAA;AACO,IAAM,SAAN,MAAa;AAAA,MAChB,YAAY,QAAQ;AAChB,aAAK,SAAS;AACd,aAAK,kBAAkB,eAAe;AAAA,MAC1C;AAAA,MACA,KAAK,SAAS,aAAa,IAAI;AAC3B,cAAM,UAAU,OAAO,gBAAgB,aAAa,cAAc;AAClE,cAAM,WAAW,OAAO,gBAAgB,aAAa,cAAc;AACnE,cAAM,kBAAkB,YAAY,UAAa,KAAK,OAAO,oBAAoB;AACjF,YAAI;AACJ,YAAI,iBAAiB;AACjB,cAAI,CAAC,KAAK,UAAU;AAChB,iBAAK,WAAW,oBAAI,QAAQ;AAAA,UAChC;AACA,gBAAM,WAAW,KAAK;AACtB,cAAI,SAAS,IAAI,QAAQ,WAAW,GAAG;AACnC,sBAAU,SAAS,IAAI,QAAQ,WAAW;AAAA,UAC9C,OACK;AACD,sBAAU,QAAQ,kBAAkB,KAAK,iBAAiB,KAAK,QAAQ,OAAO;AAC9E,qBAAS,IAAI,QAAQ,aAAa,OAAO;AAAA,UAC7C;AAAA,QACJ,OACK;AACD,iBAAO,KAAK;AACZ,oBAAU,QAAQ,kBAAkB,KAAK,iBAAiB,KAAK,QAAQ,OAAO;AAAA,QAClF;AACA,YAAI,UAAU;AACV,kBAAQ,OAAO,EACV,KAAK,CAAC,WAAW,SAAS,MAAM,OAAO,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,CAAC,EACtE,MAAM,MAAM;AAAA,UAAE,CAAC;AAAA,QACxB,OACK;AACD,iBAAO,QAAQ,OAAO,EAAE,KAAK,CAAC,WAAW,OAAO,MAAM;AAAA,QAC1D;AAAA,MACJ;AAAA,MACA,UAAU;AArCd;AAsCQ,+BAAK,WAAL,mBAAa,mBAAb,mBAA6B,YAA7B;AACA,eAAO,KAAK;AAAA,MAChB;AAAA,IACJ;AAAA;AAAA;;;ACzCA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,mBAAkB;AACzB,MAAAA,kBAAiB,QAAQ,IAAI;AAC7B,MAAAA,kBAAiB,OAAO,IAAI;AAAA,IAChC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAAA;AAAA;;;ACJ9C,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,yBAAwB;AAC/B,MAAAA,wBAAuB,QAAQ,IAAI;AACnC,MAAAA,wBAAuB,OAAO,IAAI;AAAA,IACtC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAAA;AAAA;;;ACJ1D;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,aAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACLA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,eAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,oBAAmB;AAC1B,MAAAA,mBAAkB,MAAM,IAAI;AAC5B,MAAAA,mBAAkB,OAAO,IAAI;AAAA,IACjC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAAA;AAAA;;;ACJhD;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACJA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX,IAAAC,iBAAA;AAAA;AACA,KAAC,SAAUC,cAAa;AACpB,MAAAA,aAAY,KAAK,IAAI;AACrB,MAAAA,aAAY,OAAO,IAAI;AACvB,MAAAA,aAAY,QAAQ,IAAI;AACxB,MAAAA,aAAY,MAAM,IAAI;AACtB,MAAAA,aAAY,QAAQ,IAAI;AAAA,IAC5B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAAA;AAAA;;;ACPpC;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA,IAAAC;AAAA;AAAA;;;ACFA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,gBAAe;AACtB,MAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,MAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAAA,IAClD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAAA;AAAA;;;ACJxC;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACHA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,qBAAqB;AAAA;AAAA;;;ACAlC;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,iBAAgB;AACvB,MAAAA,gBAAe,SAAS,IAAI;AAC5B,MAAAA,gBAAe,aAAa,IAAI;AAChC,MAAAA,gBAAe,UAAU,IAAI;AAAA,IACjC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAAA;AAAA;;;ACL1C;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX;AAAA;AACA,KAAC,SAAUC,yBAAwB;AAC/B,MAAAA,wBAAuB,UAAU,IAAI;AACrC,MAAAA,wBAAuB,UAAU,IAAI;AACrC,MAAAA,wBAAuB,SAAS,IAAI;AAAA,IACxC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAAA;AAAA;;;ACL1D;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,gBAAA;AAAA;AAAA;AACA,IAAAC;AACA;AACA;AACA,IAAAC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAAC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACpCA,IAEa,SA6BP;AA/BN,IAAAC,gBAAA;AAAA;AAAA;AACA,IAAAC;AACO,IAAM,UAAN,MAAc;AAAA,MACjB,cAAc;AACV,aAAK,kBAAkB,eAAe;AAAA,MAC1C;AAAA,MACA,OAAO,eAAe;AAClB,eAAO,IAAI,aAAa;AAAA,MAC5B;AAAA,MACA,6BAA6B,aAAa,eAAe,SAAS,EAAE,cAAc,YAAY,aAAa,yBAAyB,0BAA0B,eAAe,mBAAmB,YAAa,GAAG;AAC5M,mBAAW,MAAM,aAAa,KAAK,IAAI,EAAE,aAAa,aAAa,eAAe,OAAO,GAAG;AACxF,eAAK,gBAAgB,IAAI,EAAE;AAAA,QAC/B;AACA,cAAM,QAAQ,YAAY,OAAO,KAAK,eAAe;AACrD,cAAM,EAAE,QAAAC,QAAO,IAAI;AACnB,cAAM,0BAA0B;AAAA,UAC5B,QAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,CAAC,kBAAkB,GAAG;AAAA,YAClB,iBAAiB;AAAA,YACjB,GAAG;AAAA,UACP;AAAA,UACA,GAAG;AAAA,QACP;AACA,cAAM,EAAE,eAAe,IAAI;AAC3B,eAAO,MAAM,QAAQ,CAAC,YAAY,eAAe,OAAO,QAAQ,SAAS,WAAW,CAAC,CAAC,GAAG,uBAAuB;AAAA,MACpH;AAAA,IACJ;AACA,IAAM,eAAN,MAAmB;AAAA,MACf,cAAc;AACV,aAAK,QAAQ,MAAM;AAAA,QAAE;AACrB,aAAK,MAAM,CAAC;AACZ,aAAK,gBAAgB,MAAM,CAAC;AAC5B,aAAK,eAAe;AACpB,aAAK,cAAc;AACnB,aAAK,qBAAqB,CAAC;AAC3B,aAAK,iBAAiB,CAAC;AACvB,aAAK,2BAA2B,CAAC,MAAM;AACvC,aAAK,4BAA4B,CAAC,MAAM;AACxC,aAAK,cAAc;AACnB,aAAK,gBAAgB;AAAA,MACzB;AAAA,MACA,KAAK,IAAI;AACL,aAAK,QAAQ;AAAA,MACjB;AAAA,MACA,GAAG,+BAA+B;AAC9B,aAAK,MAAM;AACX,eAAO;AAAA,MACX;AAAA,MACA,EAAE,oBAAoB;AAClB,aAAK,gBAAgB;AACrB,eAAO;AAAA,MACX;AAAA,MACA,EAAE,SAAS,WAAW,gBAAgB,CAAC,GAAG;AACtC,aAAK,iBAAiB;AAAA,UAClB;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACP;AACA,eAAO;AAAA,MACX;AAAA,MACA,EAAE,oBAAoB,CAAC,GAAG;AACtB,aAAK,qBAAqB;AAC1B,eAAO;AAAA,MACX;AAAA,MACA,EAAE,YAAY,aAAa;AACvB,aAAK,cAAc;AACnB,aAAK,eAAe;AACpB,eAAO;AAAA,MACX;AAAA,MACA,EAAE,cAAc,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,GAAG;AAC/C,aAAK,2BAA2B;AAChC,aAAK,4BAA4B;AACjC,eAAO;AAAA,MACX;AAAA,MACA,IAAI,YAAY;AACZ,aAAK,cAAc;AACnB,eAAO;AAAA,MACX;AAAA,MACA,GAAG,cAAc;AACb,aAAK,gBAAgB;AACrB,eAAO;AAAA,MACX;AAAA,MACA,QAAQ;AACJ,cAAM,UAAU;AAChB,YAAI;AACJ,eAAQ,aAAa,cAAc,QAAQ;AAAA,UACvC,OAAO,mCAAmC;AACtC,mBAAO,QAAQ;AAAA,UACnB;AAAA,UACA,eAAe,CAAC,KAAK,GAAG;AACpB,kBAAM;AACN,iBAAK,YAAY,QAAQ;AACzB,iBAAK,cAAc,QAAQ;AAC3B,iBAAK,QAAQ,SAAS,CAAC;AACvB,oBAAQ,MAAM,IAAI;AAAA,UACtB;AAAA,UACA,kBAAkB,OAAO,eAAe,SAAS;AAC7C,mBAAO,KAAK,6BAA6B,OAAO,eAAe,SAAS;AAAA,cACpE,aAAa;AAAA,cACb,cAAc,QAAQ;AAAA,cACtB,YAAY,QAAQ;AAAA,cACpB,aAAa,QAAQ;AAAA,cACrB,yBAAyB,QAAQ;AAAA,cACjC,0BAA0B,QAAQ;AAAA,cAClC,eAAe,QAAQ;AAAA,cACvB,mBAAmB,QAAQ;AAAA,YAC/B,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AClHA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,mBAAmB,CAAC,YAAY,QAAQ,kBAAkB,MAAM,QAAQ,kBAAkB,IAAI,CAAC;AAAA;AAAA;;;ACD5G,IAAa;AAAb;AAAA;AAAO,IAAM,oBAAoB,CAAC,UAAU;AACxC,UAAI,OAAO,UAAU;AACjB,eAAO;AACX,YAAM,cAAc,QAAQ,QAAQ,KAAK;AACzC,aAAO,MAAM;AAAA,IACjB;AAAA;AAAA;;;ACLA,IAAAC,gBAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAAa;AAAb;AAAA;AAAO,IAAM,qBAAqB,CAAC,sBAAsB,yBAAyB;AAC9E,UAAI,CAAC,wBAAwB,qBAAqB,WAAW,GAAG;AAC5D,eAAO;AAAA,MACX;AACA,YAAM,uBAAuB,CAAC;AAC9B,iBAAW,uBAAuB,sBAAsB;AACpD,mBAAW,uBAAuB,sBAAsB;AACpD,gBAAM,0BAA0B,oBAAoB,SAAS,MAAM,GAAG,EAAE,CAAC;AACzE,cAAI,4BAA4B,qBAAqB;AACjD,iCAAqB,KAAK,mBAAmB;AAAA,UACjD;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW,uBAAuB,sBAAsB;AACpD,YAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE,SAAS,MAAM,aAAa,oBAAoB,QAAQ,GAAG;AACzF,+BAAqB,KAAK,mBAAmB;AAAA,QACjD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AChBA,SAAS,4BAA4B,iBAAiB;AAClD,QAAM,MAAM,oBAAI,IAAI;AACpB,aAAW,UAAU,iBAAiB;AAClC,QAAI,IAAI,OAAO,UAAU,MAAM;AAAA,EACnC;AACA,SAAO;AACX;AATA,IAUa;AAVb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AAQO,IAAM,2BAA2B,CAAC,QAAQ,cAAc,CAAC,MAAM,YAAY,OAAO,SAAS;AAVlG;AAWI,YAAM,UAAU,OAAO,uBAAuB,MAAM,UAAU,iCAAiC,QAAQ,SAAS,KAAK,KAAK,CAAC;AAC3H,YAAM,uBAAuB,OAAO,uBAAuB,MAAM,OAAO,qBAAqB,IAAI,CAAC;AAClG,YAAM,kBAAkB,mBAAmB,SAAS,oBAAoB;AACxE,YAAM,cAAc,4BAA4B,OAAO,eAAe;AACtE,YAAM,gBAAgB,iBAAiB,OAAO;AAC9C,YAAM,iBAAiB,CAAC;AACxB,iBAAW,UAAU,iBAAiB;AAClC,cAAM,SAAS,YAAY,IAAI,OAAO,QAAQ;AAC9C,YAAI,CAAC,QAAQ;AACT,yBAAe,KAAK,oBAAoB,OAAO,QAAQ,sCAAsC;AAC7F;AAAA,QACJ;AACA,cAAM,mBAAmB,OAAO,iBAAiB,MAAM,UAAU,+BAA+B,MAAM,CAAC;AACvG,YAAI,CAAC,kBAAkB;AACnB,yBAAe,KAAK,oBAAoB,OAAO,QAAQ,iDAAiD;AACxG;AAAA,QACJ;AACA,cAAM,EAAE,qBAAqB,CAAC,GAAG,oBAAoB,CAAC,EAAE,MAAI,YAAO,wBAAP,gCAA6B,QAAQ,aAAY,CAAC;AAC9G,eAAO,qBAAqB,OAAO,OAAO,OAAO,sBAAsB,CAAC,GAAG,kBAAkB;AAC7F,eAAO,oBAAoB,OAAO,OAAO,OAAO,qBAAqB,CAAC,GAAG,iBAAiB;AAC1F,sBAAc,yBAAyB;AAAA,UACnC,gBAAgB;AAAA,UAChB,UAAU,MAAM,iBAAiB,OAAO,kBAAkB;AAAA,UAC1D,QAAQ,OAAO;AAAA,QACnB;AACA;AAAA,MACJ;AACA,UAAI,CAAC,cAAc,wBAAwB;AACvC,cAAM,IAAI,MAAM,eAAe,KAAK,IAAI,CAAC;AAAA,MAC7C;AACA,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA;AAAA;;;AC1CA,IACa,gDAQA;AATb;AAAA;AAAA;AACO,IAAM,iDAAiD;AAAA,MAC1D,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,MACV,cAAc;AAAA,IAClB;AACO,IAAM,yCAAyC,CAAC,QAAQ,EAAE,kCAAkC,+BAAgC,OAAO;AAAA,MACtI,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,cAAc,yBAAyB,QAAQ;AAAA,UACvD;AAAA,UACA;AAAA,QACJ,CAAC,GAAG,8CAA8C;AAAA,MACtD;AAAA,IACJ;AAAA;AAAA;;;AChBA,IAAa;AAAb;AAAA;AAAO,IAAM,yBAAyB,CAAC,SAAS,iBAAiB,CAAC,MAAM,YAAY,OAAO,SAAS;AAApG;AACI,YAAM,EAAE,SAAS,IAAI,MAAM,KAAK,IAAI;AACpC,UAAI;AACA,cAAM,SAAS,MAAM,aAAa,UAAU,OAAO;AACnD,eAAO;AAAA,UACH;AAAA,UACA,QAAQ;AAAA,QACZ;AAAA,MACJ,SACO,OAAO;AACV,eAAO,eAAe,OAAO,aAAa;AAAA,UACtC,OAAO;AAAA,QACX,CAAC;AACD,YAAI,EAAE,eAAe,QAAQ;AACzB,gBAAM,OAAO;AACb,cAAI;AACA,kBAAM,WAAW,SAAS;AAAA,UAC9B,SACO,GAAG;AACN,gBAAI,CAAC,QAAQ,YAAU,mBAAQ,WAAR,mBAAgB,gBAAhB,mBAA6B,UAAS,cAAc;AACvE,sBAAQ,KAAK,IAAI;AAAA,YACrB,OACK;AACD,kCAAQ,WAAR,mBAAgB,SAAhB,4BAAuB;AAAA,YAC3B;AAAA,UACJ;AACA,cAAI,OAAO,MAAM,sBAAsB,aAAa;AAChD,gBAAI,MAAM,WAAW;AACjB,oBAAM,UAAU,OAAO,MAAM;AAAA,YACjC;AAAA,UACJ;AAAA,QACJ;AACA,cAAM;AAAA,MACV;AAAA,IACJ;AAAA;AAAA;;;AClCA,IAAa;AAAb;AAAA;AAAO,IAAM,uBAAuB,CAAC,SAAS,eAAe,CAAC,MAAM,YAAY,OAAO,SAAS;AAAhG;AACI,YAAM,aAAW,aAAQ,eAAR,mBAAoB,QAAO,QAAQ,YAC9C,YAAY,QAAQ,UAAU,QAAQ,WAAW,GAAG,IACpD,QAAQ;AACd,UAAI,CAAC,UAAU;AACX,cAAM,IAAI,MAAM,uCAAuC;AAAA,MAC3D;AACA,YAAM,UAAU,MAAM,WAAW,KAAK,OAAO,EAAE,GAAG,SAAS,SAAS,CAAC;AACrE,aAAO,KAAK;AAAA,QACR,GAAG;AAAA,QACH;AAAA,MACJ,CAAC;AAAA,IACL;AAAA;AAAA;;;ACEO,SAAS,eAAe,QAAQ,YAAY,cAAc;AAC7D,SAAO;AAAA,IACH,cAAc,CAAC,iBAAiB;AAC5B,mBAAa,IAAI,uBAAuB,QAAQ,YAAY,GAAG,4BAA4B;AAC3F,mBAAa,IAAI,qBAAqB,QAAQ,UAAU,GAAG,0BAA0B;AAAA,IACzF;AAAA,EACJ;AACJ;AArBA,IAEa,8BAMA;AARb;AAAA;AAAA;AACA;AACO,IAAM,+BAA+B;AAAA,MACxC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,MACrB,UAAU;AAAA,IACd;AACO,IAAM,6BAA6B;AAAA,MACtC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,MACnB,UAAU;AAAA,IACd;AAAA;AAAA;;;ACbA,IAAAC,gBAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA,IAAa,sCAgBA;AAhBb;AAAA;AAAO,IAAM,uCAAuC,CAAC,kBAAkB;AACnE,aAAO;AAAA,QACH,eAAe,SAAS;AACpB,wBAAc,cAAc;AAAA,QAChC;AAAA,QACA,cAAc;AACV,iBAAO,cAAc;AAAA,QACzB;AAAA,QACA,uBAAuB,KAAK,OAAO;AAR3C;AASY,8BAAc,gBAAd,mBAA2B,uBAAuB,KAAK;AAAA,QAC3D;AAAA,QACA,qBAAqB;AACjB,iBAAO,cAAc,YAAY,mBAAmB;AAAA,QACxD;AAAA,MACJ;AAAA,IACJ;AACO,IAAM,kCAAkC,CAAC,sCAAsC;AAClF,aAAO;AAAA,QACH,aAAa,kCAAkC,YAAY;AAAA,MAC/D;AAAA,IACJ;AAAA;AAAA;;;ACpBA,IAAAC,mBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;AC4CA,SAAS,WAAW,OAAO;AACvB,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,OAAO,cAAc;AACnD,UAAM,QAAQ,MAAM,SAAS;AAC7B,WAAO;AAAA,MACH,GAAG;AAAA,MACH,CAAC,SAAS,GAAG,MAAM,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI;AAAA,IACrD;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AApDA,IAAa;AAAb;AAAA;AAAO,IAAM,cAAN,MAAM,aAAY;AAAA,MACrB,YAAY,SAAS;AACjB,aAAK,SAAS,QAAQ,UAAU;AAChC,aAAK,WAAW,QAAQ,YAAY;AACpC,aAAK,OAAO,QAAQ;AACpB,aAAK,QAAQ,QAAQ,SAAS,CAAC;AAC/B,aAAK,UAAU,QAAQ,WAAW,CAAC;AACnC,aAAK,OAAO,QAAQ;AACpB,aAAK,WAAW,QAAQ,WAClB,QAAQ,SAAS,MAAM,EAAE,MAAM,MAC3B,GAAG,QAAQ,QAAQ,MACnB,QAAQ,WACZ;AACN,aAAK,OAAO,QAAQ,OAAQ,QAAQ,KAAK,OAAO,CAAC,MAAM,MAAM,IAAI,QAAQ,IAAI,KAAK,QAAQ,OAAQ;AAClG,aAAK,WAAW,QAAQ;AACxB,aAAK,WAAW,QAAQ;AACxB,aAAK,WAAW,QAAQ;AAAA,MAC5B;AAAA,MACA,OAAO,MAAM,SAAS;AAClB,cAAM,SAAS,IAAI,aAAY;AAAA,UAC3B,GAAG;AAAA,UACH,SAAS,EAAE,GAAG,QAAQ,QAAQ;AAAA,QAClC,CAAC;AACD,YAAI,OAAO,OAAO;AACd,iBAAO,QAAQ,WAAW,OAAO,KAAK;AAAA,QAC1C;AACA,eAAO;AAAA,MACX;AAAA,MACA,OAAO,WAAW,SAAS;AACvB,YAAI,CAAC,SAAS;AACV,iBAAO;AAAA,QACX;AACA,cAAM,MAAM;AACZ,eAAQ,YAAY,OAChB,cAAc,OACd,cAAc,OACd,UAAU,OACV,OAAO,IAAI,OAAO,MAAM,YACxB,OAAO,IAAI,SAAS,MAAM;AAAA,MAClC;AAAA,MACA,QAAQ;AACJ,eAAO,aAAY,MAAM,IAAI;AAAA,MACjC;AAAA,IACJ;AAAA;AAAA;;;AC3CA,IAAa;AAAb;AAAA;AAAO,IAAM,eAAN,MAAmB;AAAA,MACtB,YAAY,SAAS;AACjB,aAAK,aAAa,QAAQ;AAC1B,aAAK,SAAS,QAAQ;AACtB,aAAK,UAAU,QAAQ,WAAW,CAAC;AACnC,aAAK,OAAO,QAAQ;AAAA,MACxB;AAAA,MACA,OAAO,WAAW,UAAU;AACxB,YAAI,CAAC;AACD,iBAAO;AACX,cAAM,OAAO;AACb,eAAO,OAAO,KAAK,eAAe,YAAY,OAAO,KAAK,YAAY;AAAA,MAC1E;AAAA,IACJ;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,gBAAA;AAAA;AAAA,IAAAC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACPA,IAGM,qBAGA,uBACO;AAPb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAM,sBAAsB,CAAC,sBAAsB,CAAC,UAAU;AAC1D,YAAM;AAAA,IACV;AACA,IAAM,wBAAwB,CAAC,cAAc,sBAAsB;AAAA,IAAE;AAC9D,IAAM,wBAAwB,CAAC,WAAW,CAAC,MAAM,YAAY,OAAO,SAAS;AAChF,UAAI,CAAC,YAAY,WAAW,KAAK,OAAO,GAAG;AACvC,eAAO,KAAK,IAAI;AAAA,MACpB;AACA,YAAM,gBAAgB,iBAAiB,OAAO;AAC9C,YAAM,SAAS,cAAc;AAC7B,UAAI,CAAC,QAAQ;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC5E;AACA,YAAM,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,EAAE,GAAG,UAAU,OAAQ,IAAI;AAC1E,YAAM,SAAS,MAAM,KAAK;AAAA,QACtB,GAAG;AAAA,QACH,SAAS,MAAM,OAAO,KAAK,KAAK,SAAS,UAAU,iBAAiB;AAAA,MACxE,CAAC,EAAE,OAAO,OAAO,gBAAgB,qBAAqB,iBAAiB,CAAC;AACxE,OAAC,OAAO,kBAAkB,uBAAuB,OAAO,UAAU,iBAAiB;AACnF,aAAO;AAAA,IACX;AAAA;AAAA;;;ACvBA,IACa,8BASA;AAVb;AAAA;AAAA;AACO,IAAM,+BAA+B;AAAA,MACxC,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,MACrB,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB,mBAAmB,mBAAmB;AAAA,MACpE,UAAU;AAAA,MACV,UAAU;AAAA,MACV,cAAc;AAAA,IAClB;AACO,IAAM,uBAAuB,CAAC,YAAY;AAAA,MAC7C,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,cAAc,sBAAsB,MAAM,GAAG,4BAA4B;AAAA,MACzF;AAAA,IACJ;AAAA;AAAA;;;ACdA,IAAa;AAAb;AAAA;AAAO,IAAM,gCAAN,MAAoC;AAAA,MACvC,YAAY,QAAQ;AAChB,aAAK,cAAc,oBAAI,IAAI;AAC3B,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC/C,cAAI,UAAU,QAAW;AACrB,iBAAK,YAAY,IAAI,KAAK,KAAK;AAAA,UACnC;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,oBAAoB,UAAU;AAC1B,eAAO,KAAK,YAAY,IAAI,QAAQ;AAAA,MACxC;AAAA,IACJ;AAAA;AAAA;;;ACZA,IAAAC,yBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAEa;AAFb;AAAA;AAAA,IAAAC;AACA;AACO,IAAM,kCAAkC;AAAA,MAC3C,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,MACV,cAAc,2BAA2B;AAAA,IAC7C;AAAA;AAAA;;;ACTA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAAaC;AAAb,IAAAC,0BAAA;AAAA;AAAO,IAAMD,qBAAoB,CAAC,UAAU;AACxC,UAAI,OAAO,UAAU;AACjB,eAAO;AACX,YAAM,cAAc,QAAQ,QAAQ,KAAK;AACzC,aAAO,MAAM;AAAA,IACjB;AAAA;AAAA;;;ACAO,SAAS,gBAAgB,YAAY,aAAa,gBAAgB,iBAAiB,mBAAmB;AACzG,SAAO,gBAAgB,kBAAkB,QAAQ,UAAU,qBAAqB;AAC5E,UAAM,SAAS;AACf,QAAI,QAAQ,OAAO,iBAAiB,OAAO,cAAc;AACzD,QAAI,UAAU;AACd,QAAI;AACJ,WAAO,SAAS;AACZ,aAAO,cAAc,IAAI;AACzB,UAAI,mBAAmB;AACnB,eAAO,iBAAiB,IAAI,OAAO,iBAAiB,KAAK,OAAO;AAAA,MACpE;AACA,UAAI,OAAO,kBAAkB,YAAY;AACrC,eAAO,MAAM,uBAAuB,aAAa,OAAO,QAAQ,OAAO,OAAO,aAAa,GAAG,mBAAmB;AAAA,MACrH,OACK;AACD,cAAM,IAAI,MAAM,wCAAwC,WAAW,IAAI,EAAE;AAAA,MAC7E;AACA,YAAM;AACN,YAAM,YAAY;AAClB,cAAQ,IAAI,MAAM,eAAe;AACjC,gBAAU,CAAC,EAAE,UAAU,CAAC,OAAO,mBAAmB,UAAU;AAAA,IAChE;AACA,WAAO;AAAA,EACX;AACJ;AA7BA,IAAM,wBA8BA;AA9BN;AAAA;AAAA,IAAM,yBAAyB,OAAO,aAAa,QAAQ,OAAO,cAAc,CAAC,MAAM,MAAM,SAAS;AAClG,UAAI,UAAU,IAAI,YAAY,KAAK;AACnC,gBAAU,YAAY,OAAO,KAAK;AAClC,aAAO,MAAM,OAAO,KAAK,SAAS,GAAG,IAAI;AAAA,IAC7C;AA0BA,IAAM,MAAM,CAAC,YAAY,SAAS;AAC9B,UAAI,SAAS;AACb,YAAM,iBAAiB,KAAK,MAAM,GAAG;AACrC,iBAAW,QAAQ,gBAAgB;AAC/B,YAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACvC,iBAAO;AAAA,QACX;AACA,iBAAS,OAAO,IAAI;AAAA,MACxB;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACxCA,IAAM,oBACA,iBAuBA,eACA,aACA;AA1BN;AAAA;AAAA,IAAM,qBAAqB,CAAC;AAC5B,IAAM,kBAAkB,IAAI,MAAM,EAAE;AACpC,aAAS,IAAI,GAAG,QAAQ,IAAI,WAAW,CAAC,GAAG,QAAQ,IAAI,WAAW,CAAC,GAAG,IAAI,SAAS,OAAO,KAAK;AAC3F,YAAM,OAAO,OAAO,aAAa,IAAI,KAAK;AAC1C,yBAAmB,IAAI,IAAI;AAC3B,sBAAgB,CAAC,IAAI;AAAA,IACzB;AACA,aAAS,IAAI,GAAG,QAAQ,IAAI,WAAW,CAAC,GAAG,QAAQ,IAAI,WAAW,CAAC,GAAG,IAAI,SAAS,OAAO,KAAK;AAC3F,YAAM,OAAO,OAAO,aAAa,IAAI,KAAK;AAC1C,YAAM,QAAQ,IAAI;AAClB,yBAAmB,IAAI,IAAI;AAC3B,sBAAgB,KAAK,IAAI;AAAA,IAC7B;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,yBAAmB,EAAE,SAAS,EAAE,CAAC,IAAI,IAAI;AACzC,YAAM,OAAO,EAAE,SAAS,EAAE;AAC1B,YAAM,QAAQ,IAAI;AAClB,yBAAmB,IAAI,IAAI;AAC3B,sBAAgB,KAAK,IAAI;AAAA,IAC7B;AACA,uBAAmB,GAAG,IAAI;AAC1B,oBAAgB,EAAE,IAAI;AACtB,uBAAmB,GAAG,IAAI;AAC1B,oBAAgB,EAAE,IAAI;AACtB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,iBAAiB;AAAA;AAAA;;;AC1BvB,IACa;AADb;AAAA;AAAA;AACO,IAAM,aAAa,CAAC,UAAU;AACjC,UAAI,kBAAmB,MAAM,SAAS,IAAK;AAC3C,UAAI,MAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,2BAAmB;AAAA,MACvB,WACS,MAAM,MAAM,EAAE,MAAM,KAAK;AAC9B;AAAA,MACJ;AACA,YAAM,MAAM,IAAI,YAAY,eAAe;AAC3C,YAAM,WAAW,IAAI,SAAS,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACtC,YAAI,OAAO;AACX,YAAI,YAAY;AAChB,iBAAS,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK,OAAO,KAAK;AAC5C,cAAI,MAAM,CAAC,MAAM,KAAK;AAClB,gBAAI,EAAE,MAAM,CAAC,KAAK,qBAAqB;AACnC,oBAAM,IAAI,UAAU,qBAAqB,MAAM,CAAC,CAAC,oBAAoB;AAAA,YACzE;AACA,oBAAQ,mBAAmB,MAAM,CAAC,CAAC,MAAO,QAAQ,KAAK;AACvD,yBAAa;AAAA,UACjB,OACK;AACD,qBAAS;AAAA,UACb;AAAA,QACJ;AACA,cAAM,cAAe,IAAI,IAAK;AAC9B,iBAAS,YAAY;AACrB,cAAM,aAAa,KAAK,MAAM,YAAY,WAAW;AACrD,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,gBAAM,UAAU,aAAa,IAAI,KAAK;AACtC,mBAAS,SAAS,cAAc,IAAI,OAAQ,OAAO,WAAY,MAAM;AAAA,QACzE;AAAA,MACJ;AACA,aAAO,IAAI,WAAW,GAAG;AAAA,IAC7B;AAAA;AAAA;;;ACnCA,IAAa;AAAb;AAAA;AAAO,IAAM,WAAW,CAAC,UAAU,IAAI,YAAY,EAAE,OAAO,KAAK;AAAA;AAAA;;;ACAjE,IACa;AADb;AAAA;AAAA;AACO,IAAM,eAAe,CAAC,SAAS;AAClC,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,SAAS,IAAI;AAAA,MACxB;AACA,UAAI,YAAY,OAAO,IAAI,GAAG;AAC1B,eAAO,IAAI,WAAW,KAAK,QAAQ,KAAK,YAAY,KAAK,aAAa,WAAW,iBAAiB;AAAA,MACtG;AACA,aAAO,IAAI,WAAW,IAAI;AAAA,IAC9B;AAAA;AAAA;;;ACTA,IAAa;AAAb;AAAA;AAAO,IAAM,SAAS,CAAC,UAAU;AAC7B,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,YAAY,OAAO,MAAM,eAAe,YAAY,OAAO,MAAM,eAAe,UAAU;AAC3G,cAAM,IAAI,MAAM,8EAA8E;AAAA,MAClG;AACA,aAAO,IAAI,YAAY,OAAO,EAAE,OAAO,KAAK;AAAA,IAChD;AAAA;AAAA;;;ACRA,IAAAE,gBAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACAO,SAAS,SAAS,QAAQ;AAC7B,MAAI;AACJ,MAAI,OAAO,WAAW,UAAU;AAC5B,YAAQ,SAAS,MAAM;AAAA,EAC3B,OACK;AACD,YAAQ;AAAA,EACZ;AACA,QAAM,cAAc,OAAO,UAAU,YAAY,OAAO,MAAM,WAAW;AACzE,QAAM,eAAe,OAAO,UAAU,YAClC,OAAO,MAAM,eAAe,YAC5B,OAAO,MAAM,eAAe;AAChC,MAAI,CAAC,eAAe,CAAC,cAAc;AAC/B,UAAM,IAAI,MAAM,kFAAkF;AAAA,EACtG;AACA,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACtC,QAAI,OAAO;AACX,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,QAAQ,KAAK,IAAI,IAAI,GAAG,MAAM,MAAM,GAAG,IAAI,OAAO,KAAK;AACnE,cAAQ,MAAM,CAAC,MAAO,QAAQ,IAAI,KAAK;AACvC,mBAAa;AAAA,IACjB;AACA,UAAM,kBAAkB,KAAK,KAAK,YAAY,aAAa;AAC3D,aAAS,kBAAkB,gBAAgB;AAC3C,aAAS,IAAI,GAAG,KAAK,iBAAiB,KAAK;AACvC,YAAM,UAAU,kBAAkB,KAAK;AACvC,aAAO,iBAAiB,OAAQ,kBAAkB,WAAY,MAAM;AAAA,IACxE;AACA,WAAO,KAAK,MAAM,GAAG,IAAI,eAAe;AAAA,EAC5C;AACA,SAAO;AACX;AAlCA;AAAA;AAAA,IAAAC;AACA;AAAA;AAAA;;;ACDA,IAAAC,gBAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACEO,SAAS,kBAAkB,SAAS,WAAW,SAAS;AAC3D,MAAI,aAAa,UAAU;AACvB,WAAO,SAAS,OAAO;AAAA,EAC3B;AACA,SAAO,OAAO,OAAO;AACzB;AACO,SAAS,oBAAoB,KAAK,UAAU;AAC/C,MAAI,aAAa,UAAU;AACvB,WAAO,sBAAsB,OAAO,WAAW,GAAG,CAAC;AAAA,EACvD;AACA,SAAO,sBAAsB,OAAO,SAAS,GAAG,CAAC;AACrD;AAdA;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AAAA;AAAA;;;ACFA,IACa;AADb;AAAA;AAAA;AACO,IAAM,wBAAN,MAAM,+BAA8B,WAAW;AAAA,MAClD,OAAO,WAAW,QAAQ,WAAW,SAAS;AAC1C,gBAAQ,OAAO,QAAQ;AAAA,UACnB,KAAK;AACD,mBAAO,oBAAoB,QAAQ,QAAQ;AAAA,UAC/C;AACI,kBAAM,IAAI,MAAM,+BAA+B,OAAO,MAAM,4BAA4B;AAAA,QAChG;AAAA,MACJ;AAAA,MACA,OAAO,OAAO,QAAQ;AAClB,eAAO,eAAe,QAAQ,uBAAsB,SAAS;AAC7D,eAAO;AAAA,MACX;AAAA,MACA,kBAAkB,WAAW,SAAS;AAClC,eAAO,kBAAkB,MAAM,QAAQ;AAAA,MAC3C;AAAA,IACJ;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,mBAAmB,CAAC,WAAQ;AAAzC;AAA4C,oBAAO,mBAAmB,iBACjE,sCAAQ,gBAAR,mBAAqB,UAAS,eAAe,QAAQ,kBAAkB;AAAA;AAAA;AAAA;;;ACD5E;AAAA;AAAA,IAAAC;AACA;AACA;AAAA;AAAA;;;ACFA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAa,WACP;AADN;AAAA;AAAO,IAAM,YAAY,CAAC,QAAQ,mBAAmB,GAAG,EAAE,QAAQ,YAAY,SAAS;AACvF,IAAM,YAAY,CAAC,MAAM,IAAI,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC;AAAA;AAAA;;;ACDvE;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,gBAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACAO,SAAS,iBAAiB,OAAO;AACpC,QAAM,QAAQ,CAAC;AACf,WAAS,OAAO,OAAO,KAAK,KAAK,EAAE,KAAK,GAAG;AACvC,UAAM,QAAQ,MAAM,GAAG;AACvB,UAAM,UAAU,GAAG;AACnB,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,eAAS,IAAI,GAAG,OAAO,MAAM,QAAQ,IAAI,MAAM,KAAK;AAChD,cAAM,KAAK,GAAG,GAAG,IAAI,UAAU,MAAM,CAAC,CAAC,CAAC,EAAE;AAAA,MAC9C;AAAA,IACJ,OACK;AACD,UAAI,UAAU;AACd,UAAI,SAAS,OAAO,UAAU,UAAU;AACpC,mBAAW,IAAI,UAAU,KAAK,CAAC;AAAA,MACnC;AACA,YAAM,KAAK,OAAO;AAAA,IACtB;AAAA,EACJ;AACA,SAAO,MAAM,KAAK,GAAG;AACzB;AApBA,IAAAC,gBAAA;AAAA;AAAA,IAAAA;AAAA;AAAA;;;ACAO,SAAS,cAAc,KAAK,gBAAgB;AAC/C,SAAO,IAAI,QAAQ,KAAK,cAAc;AAC1C;AAFA;AAAA;AAAA;AAAA;;;ACAO,SAAS,eAAe,cAAc,GAAG;AAC5C,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,QAAI,aAAa;AACb,iBAAW,MAAM;AACb,cAAM,eAAe,IAAI,MAAM,mCAAmC,WAAW,KAAK;AAClF,qBAAa,OAAO;AACpB,eAAO,YAAY;AAAA,MACvB,GAAG,WAAW;AAAA,IAClB;AAAA,EACJ,CAAC;AACL;AAVA;AAAA;AAAA;AAAA;;;ACAA,IAIa,kBAGA;AAPb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACA;AACO,IAAM,mBAAmB;AAAA,MAC5B,WAAW;AAAA,IACf;AACO,IAAM,mBAAN,MAAM,kBAAiB;AAAA,MAC1B,OAAO,OAAO,mBAAmB;AAC7B,YAAI,QAAO,uDAAmB,YAAW,YAAY;AACjD,iBAAO;AAAA,QACX;AACA,eAAO,IAAI,kBAAiB,iBAAiB;AAAA,MACjD;AAAA,MACA,YAAY,SAAS;AACjB,YAAI,OAAO,YAAY,YAAY;AAC/B,eAAK,iBAAiB,QAAQ,EAAE,KAAK,CAAC,SAAS,QAAQ,CAAC,CAAC;AAAA,QAC7D,OACK;AACD,eAAK,SAAS,WAAW,CAAC;AAC1B,eAAK,iBAAiB,QAAQ,QAAQ,KAAK,MAAM;AAAA,QACrD;AACA,YAAI,iBAAiB,cAAc,QAAW;AAC1C,2BAAiB,YAAY,QAAQ,OAAO,YAAY,eAAe,eAAe,cAAc,eAAe,CAAC;AAAA,QACxH;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,MAAM,OAAO,SAAS,EAAE,YAAY,IAAI,CAAC,GAAG;AA5BhD;AA6BQ,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,SAAS,MAAM,KAAK;AAAA,QAC7B;AACA,cAAM,qBAAqB,KAAK,OAAO;AACvC,cAAM,YAAY,KAAK,OAAO,cAAc;AAC5C,cAAM,cAAc,KAAK,OAAO;AAChC,YAAI,2CAAa,SAAS;AACtB,gBAAM,aAAa,IAAI,MAAM,iBAAiB;AAC9C,qBAAW,OAAO;AAClB,iBAAO,QAAQ,OAAO,UAAU;AAAA,QACpC;AACA,YAAI,OAAO,QAAQ;AACnB,cAAM,cAAc,iBAAiB,QAAQ,SAAS,CAAC,CAAC;AACxD,YAAI,aAAa;AACb,kBAAQ,IAAI,WAAW;AAAA,QAC3B;AACA,YAAI,QAAQ,UAAU;AAClB,kBAAQ,IAAI,QAAQ,QAAQ;AAAA,QAChC;AACA,YAAI,OAAO;AACX,YAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY,MAAM;AACtD,gBAAM,WAAW,QAAQ,YAAY;AACrC,gBAAM,WAAW,QAAQ,YAAY;AACrC,iBAAO,GAAG,QAAQ,IAAI,QAAQ;AAAA,QAClC;AACA,cAAM,EAAE,MAAM,OAAO,IAAI;AACzB,cAAM,MAAM,GAAG,QAAQ,QAAQ,KAAK,IAAI,GAAG,QAAQ,QAAQ,GAAG,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG,IAAI;AAC3F,cAAM,OAAO,WAAW,SAAS,WAAW,SAAS,SAAY,QAAQ;AACzE,cAAM,iBAAiB;AAAA,UACnB;AAAA,UACA,SAAS,IAAI,QAAQ,QAAQ,OAAO;AAAA,UACpC;AAAA,UACA;AAAA,QACJ;AACA,aAAI,UAAK,WAAL,mBAAa,OAAO;AACpB,yBAAe,QAAQ,KAAK,OAAO;AAAA,QACvC;AACA,YAAI,MAAM;AACN,yBAAe,SAAS;AAAA,QAC5B;AACA,YAAI,OAAO,oBAAoB,aAAa;AACxC,yBAAe,SAAS;AAAA,QAC5B;AACA,YAAI,iBAAiB,WAAW;AAC5B,yBAAe,YAAY;AAAA,QAC/B;AACA,YAAI,OAAO,KAAK,OAAO,gBAAgB,YAAY;AAC/C,iBAAO,OAAO,gBAAgB,KAAK,OAAO,YAAY,OAAO,CAAC;AAAA,QAClE;AACA,YAAI,4BAA4B,MAAM;AAAA,QAAE;AACxC,cAAM,eAAe,cAAc,KAAK,cAAc;AACtD,cAAM,iBAAiB;AAAA,UACnB,MAAM,YAAY,EAAE,KAAK,CAAC,aAAa;AACnC,kBAAM,eAAe,SAAS;AAC9B,kBAAM,qBAAqB,CAAC;AAC5B,uBAAW,QAAQ,aAAa,QAAQ,GAAG;AACvC,iCAAmB,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AAAA,YACxC;AACA,kBAAM,oBAAoB,SAAS,QAAQ;AAC3C,gBAAI,CAAC,mBAAmB;AACpB,qBAAO,SAAS,KAAK,EAAE,KAAK,CAACC,WAAU;AAAA,gBACnC,UAAU,IAAI,aAAa;AAAA,kBACvB,SAAS;AAAA,kBACT,QAAQ,SAAS;AAAA,kBACjB,YAAY,SAAS;AAAA,kBACrB,MAAAA;AAAA,gBACJ,CAAC;AAAA,cACL,EAAE;AAAA,YACN;AACA,mBAAO;AAAA,cACH,UAAU,IAAI,aAAa;AAAA,gBACvB,SAAS;AAAA,gBACT,QAAQ,SAAS;AAAA,gBACjB,YAAY,SAAS;AAAA,gBACrB,MAAM,SAAS;AAAA,cACnB,CAAC;AAAA,YACL;AAAA,UACJ,CAAC;AAAA,UACD,eAAe,kBAAkB;AAAA,QACrC;AACA,YAAI,aAAa;AACb,yBAAe,KAAK,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjD,kBAAM,UAAU,MAAM;AAClB,oBAAM,aAAa,IAAI,MAAM,iBAAiB;AAC9C,yBAAW,OAAO;AAClB,qBAAO,UAAU;AAAA,YACrB;AACA,gBAAI,OAAO,YAAY,qBAAqB,YAAY;AACpD,oBAAM,SAAS;AACf,qBAAO,iBAAiB,SAAS,SAAS,EAAE,MAAM,KAAK,CAAC;AACxD,0CAA4B,MAAM,OAAO,oBAAoB,SAAS,OAAO;AAAA,YACjF,OACK;AACD,0BAAY,UAAU;AAAA,YAC1B;AAAA,UACJ,CAAC,CAAC;AAAA,QACN;AACA,eAAO,QAAQ,KAAK,cAAc,EAAE,QAAQ,yBAAyB;AAAA,MACzE;AAAA,MACA,uBAAuB,KAAK,OAAO;AAC/B,aAAK,SAAS;AACd,aAAK,iBAAiB,KAAK,eAAe,KAAK,CAAC,WAAW;AACvD,iBAAO,GAAG,IAAI;AACd,iBAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA,MACA,qBAAqB;AACjB,eAAO,KAAK,UAAU,CAAC;AAAA,MAC3B;AAAA,IACJ;AAAA;AAAA;;;AChIA,eAAe,YAAY,MAAM;AAC7B,QAAM,SAAS,MAAM,aAAa,IAAI;AACtC,QAAM,cAAc,WAAW,MAAM;AACrC,SAAO,IAAI,WAAW,WAAW;AACrC;AACA,eAAe,cAAc,QAAQ;AACjC,QAAM,SAAS,CAAC;AAChB,QAAM,SAAS,OAAO,UAAU;AAChC,MAAI,SAAS;AACb,MAAI,SAAS;AACb,SAAO,CAAC,QAAQ;AACZ,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,OAAO;AACP,aAAO,KAAK,KAAK;AACjB,gBAAU,MAAM;AAAA,IACpB;AACA,aAAS;AAAA,EACb;AACA,QAAM,YAAY,IAAI,WAAW,MAAM;AACvC,MAAI,SAAS;AACb,aAAW,SAAS,QAAQ;AACxB,cAAU,IAAI,OAAO,MAAM;AAC3B,cAAU,MAAM;AAAA,EACpB;AACA,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,YAAY,MAAM;AACrB,UAAI,OAAO,eAAe,GAAG;AACzB,eAAO,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,MACvD;AACA,YAAM,SAAU,OAAO,UAAU;AACjC,YAAM,aAAa,OAAO,QAAQ,GAAG;AACrC,YAAM,aAAa,aAAa,KAAK,aAAa,IAAI,OAAO;AAC7D,cAAQ,OAAO,UAAU,UAAU,CAAC;AAAA,IACxC;AACA,WAAO,UAAU,MAAM,OAAO,IAAI,MAAM,cAAc,CAAC;AACvD,WAAO,UAAU,MAAM,OAAO,OAAO,KAAK;AAC1C,WAAO,cAAc,IAAI;AAAA,EAC7B,CAAC;AACL;AApDA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,kBAAkB,OAAO,WAAW;AADjD;AAEI,UAAK,OAAO,SAAS,cAAc,kBAAkB,UAAS,YAAO,gBAAP,mBAAoB,UAAS,QAAQ;AAC/F,YAAI,KAAK,UAAU,gBAAgB,QAAW;AAC1C,iBAAO,IAAI,WAAW,MAAM,OAAO,YAAY,CAAC;AAAA,QACpD;AACA,eAAO,YAAY,MAAM;AAAA,MAC7B;AACA,aAAO,cAAc,MAAM;AAAA,IAC/B;AAAA;AAAA;;;ACTA,IAAAC,iBAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACSO,SAAS,QAAQ,SAAS;AAC7B,MAAI,QAAQ,SAAS,MAAM,GAAG;AAC1B,UAAM,IAAI,MAAM,qDAAqD;AAAA,EACzE;AACA,QAAM,MAAM,IAAI,WAAW,QAAQ,SAAS,CAAC;AAC7C,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACxC,UAAM,cAAc,QAAQ,MAAM,GAAG,IAAI,CAAC,EAAE,YAAY;AACxD,QAAI,eAAe,cAAc;AAC7B,UAAI,IAAI,CAAC,IAAI,aAAa,WAAW;AAAA,IACzC,OACK;AACD,YAAM,IAAI,MAAM,uCAAuC,WAAW,iBAAiB;AAAA,IACvF;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,MAAM,OAAO;AACzB,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,YAAY,KAAK;AACvC,WAAO,aAAa,MAAM,CAAC,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AAhCA,IAAM,cACA;AADN,IAAAC,iBAAA;AAAA;AAAA,IAAM,eAAe,CAAC;AACtB,IAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAI,cAAc,EAAE,SAAS,EAAE,EAAE,YAAY;AAC7C,UAAI,YAAY,WAAW,GAAG;AAC1B,sBAAc,IAAI,WAAW;AAAA,MACjC;AACA,mBAAa,CAAC,IAAI;AAClB,mBAAa,WAAW,IAAI;AAAA,IAChC;AAAA;AAAA;;;ACTA,IAKM,qCACO,gBAyDP;AA/DN;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA,IAAM,sCAAsC;AACrC,IAAM,iBAAiB,CAAC,WAAW;AAN1C;AAOI,UAAI,CAAC,eAAe,MAAM,KAAK,CAAC,iBAAiB,MAAM,GAAG;AACtD,cAAM,SAAO,4CAAQ,cAAR,mBAAmB,gBAAnB,mBAAgC,SAAQ;AACrD,cAAM,IAAI,MAAM,wEAAwE,IAAI,EAAE;AAAA,MAClG;AACA,UAAI,cAAc;AAClB,YAAM,uBAAuB,YAAY;AACrC,YAAI,aAAa;AACb,gBAAM,IAAI,MAAM,mCAAmC;AAAA,QACvD;AACA,sBAAc;AACd,eAAO,MAAM,gBAAgB,MAAM;AAAA,MACvC;AACA,YAAM,kBAAkB,CAAC,SAAS;AAC9B,YAAI,OAAO,KAAK,WAAW,YAAY;AACnC,gBAAM,IAAI,MAAM,0OAC8H;AAAA,QAClJ;AACA,eAAO,KAAK,OAAO;AAAA,MACvB;AACA,aAAO,OAAO,OAAO,QAAQ;AAAA,QACzB;AAAA,QACA,mBAAmB,OAAO,aAAa;AACnC,gBAAM,MAAM,MAAM,qBAAqB;AACvC,cAAI,aAAa,UAAU;AACvB,mBAAO,SAAS,GAAG;AAAA,UACvB,WACS,aAAa,OAAO;AACzB,mBAAO,MAAM,GAAG;AAAA,UACpB,WACS,aAAa,UAAa,aAAa,UAAU,aAAa,SAAS;AAC5E,mBAAO,OAAO,GAAG;AAAA,UACrB,WACS,OAAO,gBAAgB,YAAY;AACxC,mBAAO,IAAI,YAAY,QAAQ,EAAE,OAAO,GAAG;AAAA,UAC/C,OACK;AACD,kBAAM,IAAI,MAAM,sEAAsE;AAAA,UAC1F;AAAA,QACJ;AAAA,QACA,sBAAsB,MAAM;AACxB,cAAI,aAAa;AACb,kBAAM,IAAI,MAAM,mCAAmC;AAAA,UACvD;AACA,wBAAc;AACd,cAAI,eAAe,MAAM,GAAG;AACxB,mBAAO,gBAAgB,MAAM;AAAA,UACjC,WACS,iBAAiB,MAAM,GAAG;AAC/B,mBAAO;AAAA,UACX,OACK;AACD,kBAAM,IAAI,MAAM,+CAA+C,MAAM,EAAE;AAAA,UAC3E;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAM,iBAAiB,CAAC,WAAW,OAAO,SAAS,cAAc,kBAAkB;AAAA;AAAA;;;AC/DnF;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACRA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,cAAc,OAAO,aAAa,IAAI,WAAW,GAAG,YAAY;AACzE,UAAI,sBAAsB,YAAY;AAClC,eAAO,sBAAsB,OAAO,UAAU;AAAA,MAClD;AACA,UAAI,CAAC,YAAY;AACb,eAAO,sBAAsB,OAAO,IAAI,WAAW,CAAC;AAAA,MACxD;AACA,YAAM,cAAc,QAAQ,gBAAgB,UAAU;AACtD,aAAO,sBAAsB,OAAO,MAAM,WAAW;AAAA,IACzD;AAAA;AAAA;;;ACVO,SAAS,2BAA2B,KAAK;AAC5C,SAAO,mBAAmB,GAAG,EAAE,QAAQ,YAAY,SAAU,GAAG;AAC5D,WAAO,MAAM,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,EAC1D,CAAC;AACL;AAJA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACHA,IAAAC,uBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,SAAS,WAAW,SAAS,SAAS,OAAO;AAChD,MAAI,CAAC,QAAQ,kBAAkB;AAC3B,YAAQ,mBAAmB;AAAA,MACvB,UAAU,CAAC;AAAA,IACf;AAAA,EACJ,WACS,CAAC,QAAQ,iBAAiB,UAAU;AACzC,YAAQ,iBAAiB,WAAW,CAAC;AAAA,EACzC;AACA,UAAQ,iBAAiB,SAAS,OAAO,IAAI;AACjD;AAVA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AACA,IAAAA;AAAA;AAAA;;;ACDA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,eAAN,MAAmB;AAAA,MACtB,MAAM,KAAK,aAAa,UAAU,mBAAmB;AACjD,eAAO;AAAA,MACX;AAAA,IACJ;AAAA;AAAA;;;ACJA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA,IAAa,iCACA,eACA,mBACA,4BACA;AAJb;AAAA;AAAO,IAAM,kCAAkC,CAAC,iBAAiB,CAAC,aAAa,2BAA2B,QAAQ,KAAK,SAAS,WAAW,QAAQ,IAAI,KAAK,IAAI,IAAI;AAC7J,IAAM,gBAAgB;AACtB,IAAM,oBAAoB,gCAAgC,aAAa;AACvE,IAAM,6BAA6B,CAAC,aAAa,SAAS,eAAe;AACzE,IAAM,0BAA0B,CAAC,UAAU,WAAW,oBAAoB;AAC7E,UAAI,aAAa,QAAW;AACxB,eAAO;AAAA,MACX;AACA,YAAM,qBAAqB,OAAO,aAAa,aAAa,YAAY,QAAQ,QAAQ,QAAQ,IAAI;AACpG,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa;AACjB,YAAM,mBAAmB,OAAO,YAAY;AACxC,YAAI,CAAC,SAAS;AACV,oBAAU,mBAAmB,OAAO;AAAA,QACxC;AACA,YAAI;AACA,qBAAW,MAAM;AACjB,sBAAY;AACZ,uBAAa;AAAA,QACjB,UACA;AACI,oBAAU;AAAA,QACd;AACA,eAAO;AAAA,MACX;AACA,UAAI,cAAc,QAAW;AACzB,eAAO,OAAO,YAAY;AACtB,cAAI,CAAC,cAAa,mCAAS,eAAc;AACrC,uBAAW,MAAM,iBAAiB,OAAO;AAAA,UAC7C;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,OAAO,YAAY;AACtB,YAAI,CAAC,cAAa,mCAAS,eAAc;AACrC,qBAAW,MAAM,iBAAiB,OAAO;AAAA,QAC7C;AACA,YAAI,YAAY;AACZ,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,gBAAgB,QAAQ,GAAG;AAC5B,uBAAa;AACb,iBAAO;AAAA,QACX;AACA,YAAI,UAAU,QAAQ,GAAG;AACrB,gBAAM,iBAAiB,OAAO;AAC9B,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA;AAAA;;;ACpDA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA,IAAAC,iBAAA;AAAA;AAAA,IAAAC;AACA;AACA;AACA,IAAAC;AACA;AACA,IAAAC;AACA;AACA;AAAA;AAAA;;;ACPA,IAAa;AAAb;AAAA;AAAO,IAAM,mBAAmB;AAAA;AAAA;;;ACAhC,IAAa;AAAb;AAAA;AAAO,IAAM,yBAAyB,CAAC,UAAUC,YAAW;AACxD,iBAAW,WAAW,OAAO,KAAK,QAAQ,GAAG;AACzC,cAAM,cAAc,SAAS,OAAO;AACpC,cAAM,aAAa,eAAgB,MAAM,aAAa,IAAI;AACtD,gBAAMC,WAAU,IAAI,YAAY,IAAI;AACpC,cAAI,OAAO,gBAAgB,YAAY;AACnC,iBAAK,KAAKA,UAAS,WAAW;AAAA,UAClC,WACS,OAAO,OAAO,YAAY;AAC/B,gBAAI,OAAO,gBAAgB;AACvB,oBAAM,IAAI,MAAM,iCAAiC,OAAO,WAAW,EAAE;AACzE,iBAAK,KAAKA,UAAS,eAAe,CAAC,GAAG,EAAE;AAAA,UAC5C,OACK;AACD,mBAAO,KAAK,KAAKA,UAAS,WAAW;AAAA,UACzC;AAAA,QACJ;AACA,cAAM,cAAc,QAAQ,CAAC,EAAE,YAAY,IAAI,QAAQ,MAAM,CAAC,GAAG,QAAQ,YAAY,EAAE;AACvF,QAAAD,QAAO,UAAU,UAAU,IAAI;AAAA,MACnC;AAAA,IACJ;AAAA;AAAA;;;ACpBA,IAAa,kBAiCA;AAjCb;AAAA;AAAO,IAAM,mBAAN,MAAM,0BAAyB,MAAM;AAAA,MACxC,YAAY,SAAS;AACjB,cAAM,QAAQ,OAAO;AACrB,eAAO,eAAe,MAAM,OAAO,eAAe,IAAI,EAAE,YAAY,SAAS;AAC7E,aAAK,OAAO,QAAQ;AACpB,aAAK,SAAS,QAAQ;AACtB,aAAK,YAAY,QAAQ;AAAA,MAC7B;AAAA,MACA,OAAO,WAAW,OAAO;AACrB,YAAI,CAAC;AACD,iBAAO;AACX,cAAM,YAAY;AAClB,eAAQ,kBAAiB,UAAU,cAAc,SAAS,KACrD,QAAQ,UAAU,MAAM,KACrB,QAAQ,UAAU,SAAS,MAC1B,UAAU,WAAW,YAAY,UAAU,WAAW;AAAA,MACnE;AAAA,MACA,QAAQ,OAAO,WAAW,EAAE,UAAU;AAClC,YAAI,CAAC;AACD,iBAAO;AACX,cAAM,YAAY;AAClB,YAAI,SAAS,mBAAkB;AAC3B,iBAAO,kBAAiB,WAAW,QAAQ;AAAA,QAC/C;AACA,YAAI,kBAAiB,WAAW,QAAQ,GAAG;AACvC,cAAI,UAAU,QAAQ,KAAK,MAAM;AAC7B,mBAAO,KAAK,UAAU,cAAc,QAAQ,KAAK,UAAU,SAAS,KAAK;AAAA,UAC7E;AACA,iBAAO,KAAK,UAAU,cAAc,QAAQ;AAAA,QAChD;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACO,IAAM,2BAA2B,CAAC,WAAW,YAAY,CAAC,MAAM;AACnE,aAAO,QAAQ,SAAS,EACnB,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,MAAM,MAAS,EACjC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACrB,YAAI,UAAU,CAAC,KAAK,UAAa,UAAU,CAAC,MAAM,IAAI;AAClD,oBAAU,CAAC,IAAI;AAAA,QACnB;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,UAAU,WAAW,UAAU,WAAW;AAC1D,gBAAU,UAAU;AACpB,aAAO,UAAU;AACjB,aAAO;AAAA,IACX;AAAA;AAAA;;;AC7CA,IAAAE,4BAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IA0Ca,cAkBP,WACO,eASA,YAUA,aACA,aACA,YACP,gBAOA,SAUO,eAmBA,cA6BA,mBAOA,oBAMP,cACA,aAwCO,kBAMA,kBAMA,iBAMP,mBAOO;AAnOb;AAAA;AA0CO,IAAM,eAAe,CAAC,UAAU;AACnC,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,cAAM,SAAS,WAAW,KAAK;AAC/B,YAAI,CAAC,OAAO,MAAM,MAAM,GAAG;AACvB,cAAI,OAAO,MAAM,MAAM,OAAO,KAAK,GAAG;AAClC,mBAAO,KAAK,kBAAkB,wCAAwC,KAAK,EAAE,CAAC;AAAA,UAClF;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO;AAAA,MACX;AACA,YAAM,IAAI,UAAU,wBAAwB,OAAO,KAAK,KAAK,KAAK,EAAE;AAAA,IACxE;AACA,IAAM,YAAY,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI;AAC9C,IAAM,gBAAgB,CAAC,UAAU;AACpC,YAAM,WAAW,aAAa,KAAK;AACnC,UAAI,aAAa,UAAa,CAAC,OAAO,MAAM,QAAQ,KAAK,aAAa,YAAY,aAAa,WAAW;AACtG,YAAI,KAAK,IAAI,QAAQ,IAAI,WAAW;AAChC,gBAAM,IAAI,UAAU,8BAA8B,KAAK,EAAE;AAAA,QAC7D;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACO,IAAM,aAAa,CAAC,UAAU;AACjC,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,KAAK,KAAK,CAAC,OAAO,MAAM,KAAK,GAAG;AACjD,eAAO;AAAA,MACX;AACA,YAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK,KAAK,KAAK,EAAE;AAAA,IACzE;AAEO,IAAM,cAAc,CAAC,UAAU,eAAe,OAAO,EAAE;AACvD,IAAM,cAAc,CAAC,UAAU,eAAe,OAAO,EAAE;AACvD,IAAM,aAAa,CAAC,UAAU,eAAe,OAAO,CAAC;AAC5D,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACpC,YAAM,WAAW,WAAW,KAAK;AACjC,UAAI,aAAa,UAAa,QAAQ,UAAU,IAAI,MAAM,UAAU;AAChE,cAAM,IAAI,UAAU,YAAY,IAAI,qBAAqB,KAAK,EAAE;AAAA,MACpE;AACA,aAAO;AAAA,IACX;AACA,IAAM,UAAU,CAAC,OAAO,SAAS;AAC7B,cAAQ,MAAM;AAAA,QACV,KAAK;AACD,iBAAO,WAAW,GAAG,KAAK,EAAE,CAAC;AAAA,QACjC,KAAK;AACD,iBAAO,WAAW,GAAG,KAAK,EAAE,CAAC;AAAA,QACjC,KAAK;AACD,iBAAO,UAAU,GAAG,KAAK,EAAE,CAAC;AAAA,MACpC;AAAA,IACJ;AACO,IAAM,gBAAgB,CAAC,OAAO,aAAa;AAC9C,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,YAAI,UAAU;AACV,gBAAM,IAAI,UAAU,iCAAiC,QAAQ,EAAE;AAAA,QACnE;AACA,cAAM,IAAI,UAAU,2BAA2B;AAAA,MACnD;AACA,aAAO;AAAA,IACX;AAWO,IAAM,eAAe,CAAC,UAAU;AACnC,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO;AAAA,MACX;AACA,UAAI,CAAC,WAAW,UAAU,QAAQ,EAAE,SAAS,OAAO,KAAK,GAAG;AACxD,eAAO,KAAK,kBAAkB,wBAAwB,OAAO,KAAK,KAAK,KAAK,EAAE,CAAC;AAC/E,eAAO,OAAO,KAAK;AAAA,MACvB;AACA,YAAM,IAAI,UAAU,wBAAwB,OAAO,KAAK,KAAK,KAAK,EAAE;AAAA,IACxE;AAiBO,IAAM,oBAAoB,CAAC,UAAU;AACxC,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,aAAa,YAAY,KAAK,CAAC;AAAA,MAC1C;AACA,aAAO,aAAa,KAAK;AAAA,IAC7B;AAEO,IAAM,qBAAqB,CAAC,UAAU;AACzC,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,cAAc,YAAY,KAAK,CAAC;AAAA,MAC3C;AACA,aAAO,cAAc,KAAK;AAAA,IAC9B;AACA,IAAM,eAAe;AACrB,IAAM,cAAc,CAAC,UAAU;AAC3B,YAAM,UAAU,MAAM,MAAM,YAAY;AACxC,UAAI,YAAY,QAAQ,QAAQ,CAAC,EAAE,WAAW,MAAM,QAAQ;AACxD,cAAM,IAAI,UAAU,wCAAwC;AAAA,MAChE;AACA,aAAO,WAAW,KAAK;AAAA,IAC3B;AAkCO,IAAM,mBAAmB,CAAC,UAAU;AACvC,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO,YAAY,YAAY,KAAK,CAAC;AAAA,MACzC;AACA,aAAO,YAAY,KAAK;AAAA,IAC5B;AACO,IAAM,mBAAmB,CAAC,UAAU;AACvC,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO,YAAY,YAAY,KAAK,CAAC;AAAA,MACzC;AACA,aAAO,YAAY,KAAK;AAAA,IAC5B;AACO,IAAM,kBAAkB,CAAC,UAAU;AACtC,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO,WAAW,YAAY,KAAK,CAAC;AAAA,MACxC;AACA,aAAO,WAAW,KAAK;AAAA,IAC3B;AACA,IAAM,oBAAoB,CAAC,YAAY;AACnC,aAAO,OAAO,IAAI,UAAU,OAAO,EAAE,SAAS,OAAO,EAChD,MAAM,IAAI,EACV,MAAM,GAAG,CAAC,EACV,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,mBAAmB,CAAC,EAC9C,KAAK,IAAI;AAAA,IAClB;AACO,IAAM,SAAS;AAAA,MAClB,MAAM,QAAQ;AAAA,IAClB;AAAA;AAAA;;;ACrOA,IAEM,QAeA,SACO,sBAiBP,qBACO,gCAqBP,aACA,cACA,UA8BO,qBAsBP,WAaA,uBAcA,eACA,oBASA,YAGA,gBAOA,mBAMA,2BAgBA;AApLN;AAAA;AAAA;AAEA,IAAM,SAAS,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAelG,IAAM,UAAU,IAAI,OAAO,sEAAsE;AAC1F,IAAM,uBAAuB,CAAC,UAAU;AAC3C,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E;AACA,YAAM,QAAQ,QAAQ,KAAK,KAAK;AAChC,UAAI,CAAC,OAAO;AACR,cAAM,IAAI,UAAU,kCAAkC;AAAA,MAC1D;AACA,YAAM,CAAC,GAAG,SAAS,UAAU,QAAQ,OAAO,SAAS,SAAS,sBAAsB,IAAI;AACxF,YAAM,OAAO,iBAAiB,mBAAmB,OAAO,CAAC;AACzD,YAAM,QAAQ,eAAe,UAAU,SAAS,GAAG,EAAE;AACrD,YAAM,MAAM,eAAe,QAAQ,OAAO,GAAG,EAAE;AAC/C,aAAO,UAAU,MAAM,OAAO,KAAK,EAAE,OAAO,SAAS,SAAS,uBAAuB,CAAC;AAAA,IAC1F;AACA,IAAM,sBAAsB,IAAI,OAAO,2FAA2F;AAC3H,IAAM,iCAAiC,CAAC,UAAU;AACrD,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,UAAI,OAAO,UAAU,UAAU;AAC3B,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E;AACA,YAAM,QAAQ,oBAAoB,KAAK,KAAK;AAC5C,UAAI,CAAC,OAAO;AACR,cAAM,IAAI,UAAU,kCAAkC;AAAA,MAC1D;AACA,YAAM,CAAC,GAAG,SAAS,UAAU,QAAQ,OAAO,SAAS,SAAS,wBAAwB,SAAS,IAAI;AACnG,YAAM,OAAO,iBAAiB,mBAAmB,OAAO,CAAC;AACzD,YAAM,QAAQ,eAAe,UAAU,SAAS,GAAG,EAAE;AACrD,YAAM,MAAM,eAAe,QAAQ,OAAO,GAAG,EAAE;AAC/C,YAAM,OAAO,UAAU,MAAM,OAAO,KAAK,EAAE,OAAO,SAAS,SAAS,uBAAuB,CAAC;AAC5F,UAAI,UAAU,YAAY,KAAK,KAAK;AAChC,aAAK,QAAQ,KAAK,QAAQ,IAAI,0BAA0B,SAAS,CAAC;AAAA,MACtE;AACA,aAAO;AAAA,IACX;AACA,IAAM,cAAc,IAAI,OAAO,gJAAgJ;AAC/K,IAAM,eAAe,IAAI,OAAO,6KAA6K;AAC7M,IAAM,WAAW,IAAI,OAAO,kJAAkJ;AA8BvK,IAAM,sBAAsB,CAAC,UAAU;AAC1C,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,UAAI;AACJ,UAAI,OAAO,UAAU,UAAU;AAC3B,wBAAgB;AAAA,MACpB,WACS,OAAO,UAAU,UAAU;AAChC,wBAAgB,kBAAkB,KAAK;AAAA,MAC3C,WACS,OAAO,UAAU,YAAY,MAAM,QAAQ,GAAG;AACnD,wBAAgB,MAAM;AAAA,MAC1B,OACK;AACD,cAAM,IAAI,UAAU,6FAA6F;AAAA,MACrH;AACA,UAAI,OAAO,MAAM,aAAa,KAAK,kBAAkB,YAAY,kBAAkB,WAAW;AAC1F,cAAM,IAAI,UAAU,gEAAgE;AAAA,MACxF;AACA,aAAO,IAAI,KAAK,KAAK,MAAM,gBAAgB,GAAI,CAAC;AAAA,IACpD;AACA,IAAM,YAAY,CAAC,MAAM,OAAO,KAAK,SAAS;AAC1C,YAAM,gBAAgB,QAAQ;AAC9B,yBAAmB,MAAM,eAAe,GAAG;AAC3C,aAAO,IAAI,KAAK,KAAK,IAAI,MAAM,eAAe,KAAK,eAAe,KAAK,OAAO,QAAQ,GAAG,EAAE,GAAG,eAAe,KAAK,SAAS,UAAU,GAAG,EAAE,GAAG,eAAe,KAAK,SAAS,WAAW,GAAG,EAAE,GAAG,kBAAkB,KAAK,sBAAsB,CAAC,CAAC;AAAA,IAChP;AASA,IAAM,wBAAwB,KAAK,MAAM,KAAK,KAAK,KAAK;AAcxD,IAAM,gBAAgB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACrE,IAAM,qBAAqB,CAAC,MAAM,OAAO,QAAQ;AAC7C,UAAI,UAAU,cAAc,KAAK;AACjC,UAAI,UAAU,KAAK,WAAW,IAAI,GAAG;AACjC,kBAAU;AAAA,MACd;AACA,UAAI,MAAM,SAAS;AACf,cAAM,IAAI,UAAU,mBAAmB,OAAO,KAAK,CAAC,OAAO,IAAI,KAAK,GAAG,EAAE;AAAA,MAC7E;AAAA,IACJ;AACA,IAAM,aAAa,CAAC,SAAS;AACzB,aAAO,OAAO,MAAM,MAAM,OAAO,QAAQ,KAAK,OAAO,QAAQ;AAAA,IACjE;AACA,IAAM,iBAAiB,CAAC,OAAO,MAAM,OAAO,UAAU;AAClD,YAAM,UAAU,gBAAgB,mBAAmB,KAAK,CAAC;AACzD,UAAI,UAAU,SAAS,UAAU,OAAO;AACpC,cAAM,IAAI,UAAU,GAAG,IAAI,oBAAoB,KAAK,QAAQ,KAAK,aAAa;AAAA,MAClF;AACA,aAAO;AAAA,IACX;AACA,IAAM,oBAAoB,CAAC,UAAU;AACjC,UAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,eAAO;AAAA,MACX;AACA,aAAO,mBAAmB,OAAO,KAAK,IAAI;AAAA,IAC9C;AACA,IAAM,4BAA4B,CAAC,UAAU;AACzC,YAAM,eAAe,MAAM,CAAC;AAC5B,UAAI,YAAY;AAChB,UAAI,gBAAgB,KAAK;AACrB,oBAAY;AAAA,MAChB,WACS,gBAAgB,KAAK;AAC1B,oBAAY;AAAA,MAChB,OACK;AACD,cAAM,IAAI,UAAU,qBAAqB,YAAY,sBAAsB;AAAA,MAC/E;AACA,YAAM,OAAO,OAAO,MAAM,UAAU,GAAG,CAAC,CAAC;AACzC,YAAM,SAAS,OAAO,MAAM,UAAU,GAAG,CAAC,CAAC;AAC3C,aAAO,aAAa,OAAO,KAAK,UAAU,KAAK;AAAA,IACnD;AACA,IAAM,qBAAqB,CAAC,UAAU;AAClC,UAAI,MAAM;AACV,aAAO,MAAM,MAAM,SAAS,KAAK,MAAM,OAAO,GAAG,MAAM,KAAK;AACxD;AAAA,MACJ;AACA,UAAI,QAAQ,GAAG;AACX,eAAO;AAAA,MACX;AACA,aAAO,MAAM,MAAM,GAAG;AAAA,IAC1B;AAAA;AAAA;;;AC7LA,IACa,mBAUA,mBAKP;AAhBN;AAAA;AAAA;AACO,IAAM,oBAAoB,CAAC,EAAE,QAAQ,YAAY,eAAe,UAAU,MAAM;AACnF,YAAM,YAAY,oBAAoB,MAAM;AAC5C,YAAM,aAAa,UAAU,iBAAiB,UAAU,iBAAiB,KAAK;AAC9E,YAAM,WAAW,IAAI,cAAc;AAAA,QAC/B,OAAM,yCAAY,UAAQ,yCAAY,SAAQ,aAAa,cAAc;AAAA,QACzE,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,YAAM,yBAAyB,UAAU,UAAU;AAAA,IACvD;AACO,IAAM,oBAAoB,CAAC,kBAAkB;AAChD,aAAO,CAAC,EAAE,QAAQ,YAAY,UAAU,MAAM;AAC1C,0BAAkB,EAAE,QAAQ,YAAY,eAAe,eAAe,UAAU,CAAC;AAAA,MACrF;AAAA,IACJ;AACA,IAAM,sBAAsB,CAAC,YAAY;AAAA,MACrC,gBAAgB,OAAO;AAAA,MACvB,WAAW,OAAO,QAAQ,kBAAkB,KAAK,OAAO,QAAQ,mBAAmB,KAAK,OAAO,QAAQ,kBAAkB;AAAA,MACzH,mBAAmB,OAAO,QAAQ,YAAY;AAAA,MAC9C,MAAM,OAAO,QAAQ,aAAa;AAAA,IACtC;AAAA;AAAA;;;ACrBA,IAAa;AAAb;AAAA;AAAO,IAAM,4BAA4B,CAAC,SAAS;AAC/C,cAAQ,MAAM;AAAA,QACV,KAAK;AACD,iBAAO;AAAA,YACH,WAAW;AAAA,YACX,mBAAmB;AAAA,UACvB;AAAA,QACJ,KAAK;AACD,iBAAO;AAAA,YACH,WAAW;AAAA,YACX,mBAAmB;AAAA,UACvB;AAAA,QACJ,KAAK;AACD,iBAAO;AAAA,YACH,WAAW;AAAA,YACX,mBAAmB;AAAA,UACvB;AAAA,QACJ,KAAK;AACD,iBAAO;AAAA,YACH,WAAW;AAAA,YACX,mBAAmB;AAAA,UACvB;AAAA,QACJ;AACI,iBAAO,CAAC;AAAA,MAChB;AAAA,IACJ;AAAA;AAAA;;;ACzBA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,sCAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAEaC,2BAqBAC;AAvBb,IAAAC,iBAAA;AAAA;AAAA,IAAAC;AAEO,IAAMH,4BAA2B,CAAC,kBAAkB;AACvD,YAAM,qBAAqB,CAAC;AAC5B,iBAAW,MAAM,aAAa;AAC1B,cAAM,cAAc,YAAY,EAAE;AAClC,YAAI,cAAc,WAAW,MAAM,QAAW;AAC1C;AAAA,QACJ;AACA,2BAAmB,KAAK;AAAA,UACpB,aAAa,MAAM;AAAA,UACnB,qBAAqB,MAAM,cAAc,WAAW;AAAA,QACxD,CAAC;AAAA,MACL;AACA,aAAO;AAAA,QACH,qBAAqB,MAAM;AACvB,6BAAmB,KAAK,IAAI;AAAA,QAChC;AAAA,QACA,qBAAqB;AACjB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACO,IAAMC,gCAA+B,CAAC,iBAAiB;AAC1D,YAAM,gBAAgB,CAAC;AACvB,mBAAa,mBAAmB,EAAE,QAAQ,CAAC,sBAAsB;AAC7D,sBAAc,kBAAkB,YAAY,CAAC,IAAI,kBAAkB,oBAAoB;AAAA,MAC3F,CAAC;AACD,aAAO;AAAA,IACX;AAAA;AAAA;;;AC7BA,IAAa,uBAUA;AAVb,IAAAG,cAAA;AAAA;AAAO,IAAM,wBAAwB,CAAC,kBAAkB;AACpD,aAAO;AAAA,QACH,iBAAiB,eAAe;AAC5B,wBAAc,gBAAgB;AAAA,QAClC;AAAA,QACA,gBAAgB;AACZ,iBAAO,cAAc;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACO,IAAM,4BAA4B,CAAC,+BAA+B;AACrE,YAAM,gBAAgB,CAAC;AACvB,oBAAc,gBAAgB,2BAA2B,cAAc;AACvE,aAAO;AAAA,IACX;AAAA;AAAA;;;ACdA,IAEa,kCAIA;AANb,IAAAC,sCAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AACO,IAAM,mCAAmC,CAAC,kBAAkB;AAC/D,aAAO,OAAO,OAAOC,0BAAyB,aAAa,GAAG,sBAAsB,aAAa,CAAC;AAAA,IACtG;AAEO,IAAM,8BAA8B,CAAC,WAAW;AACnD,aAAO,OAAO,OAAOC,8BAA6B,MAAM,GAAG,0BAA0B,MAAM,CAAC;AAAA,IAChG;AAAA;AAAA;;;ACRA,IAAAC,mBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,uBAAuB,CAAC,QAAQ;AACzC,YAAM,eAAe;AACrB,iBAAW,OAAO,KAAK;AACnB,YAAI,IAAI,eAAe,GAAG,KAAK,IAAI,GAAG,EAAE,YAAY,MAAM,QAAW;AACjE,cAAI,GAAG,IAAI,IAAI,GAAG,EAAE,YAAY;AAAA,QACpC,WACS,OAAO,IAAI,GAAG,MAAM,YAAY,IAAI,GAAG,MAAM,MAAM;AACxD,cAAI,GAAG,IAAI,qBAAqB,IAAI,GAAG,CAAC;AAAA,QAC5C;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACXA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,iBAAiB,SAASC,gBAAe,KAAK;AACvD,YAAM,MAAM,OAAO,OAAO,IAAI,OAAO,GAAG,GAAG;AAAA,QACvC,kBAAkB;AACd,iBAAO,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,QACjC;AAAA,QACA,WAAW;AACP,iBAAO,OAAO,GAAG;AAAA,QACrB;AAAA,QACA,SAAS;AACL,iBAAO,OAAO,GAAG;AAAA,QACrB;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,mBAAe,OAAO,CAAC,WAAW;AAC9B,UAAI,UAAU,OAAO,WAAW,aAAa,kBAAkB,kBAAkB,qBAAqB,SAAS;AAC3G,eAAO;AAAA,MACX,WACS,OAAO,WAAW,YAAY,OAAO,eAAe,MAAM,MAAM,OAAO,WAAW;AACvF,eAAO,eAAe,OAAO,MAAM,CAAC;AAAA,MACxC;AACA,aAAO,eAAe,KAAK,UAAU,MAAM,CAAC;AAAA,IAChD;AACA,mBAAe,aAAa,eAAe;AAAA;AAAA;;;ACvB3C,IAAa;AAAb;AAAA;AAAO,IAAM,aAAN,MAAiB;AAAA,MACpB,QAAQ;AAAA,MAAE;AAAA,MACV,QAAQ;AAAA,MAAE;AAAA,MACV,OAAO;AAAA,MAAE;AAAA,MACT,OAAO;AAAA,MAAE;AAAA,MACT,QAAQ;AAAA,MAAE;AAAA,IACd;AAAA;AAAA;;;ACNA,IAmCa,MAuBP,kBAgCA,YACA;AA3FN;AAAA;AAmCO,IAAM,OAAO,CAAC,QAAQ,iBAAiB;AAC1C,YAAM,MAAM,CAAC;AACb,iBAAW,OAAO,cAAc;AAC5B,yBAAiB,KAAK,QAAQ,cAAc,GAAG;AAAA,MACnD;AACA,aAAO;AAAA,IACX;AAiBA,IAAM,mBAAmB,CAAC,QAAQ,QAAQ,cAAc,cAAc;AAClE,UAAI,WAAW,MAAM;AACjB,YAAI,cAAc,aAAa,SAAS;AACxC,YAAI,OAAO,gBAAgB,YAAY;AACnC,wBAAc,CAAC,EAAE,WAAW;AAAA,QAChC;AACA,cAAM,CAACC,UAAS,YAAY,UAAU,MAAM,YAAY,SAAS,IAAI;AACrE,YAAK,OAAOA,YAAW,cAAcA,QAAO,OAAO,SAAS,CAAC,KAAO,OAAOA,YAAW,cAAc,CAAC,CAACA,SAAS;AAC3G,iBAAO,SAAS,IAAI,QAAQ,OAAO,SAAS,CAAC;AAAA,QACjD;AACA;AAAA,MACJ;AACA,UAAI,CAAC,QAAQ,KAAK,IAAI,aAAa,SAAS;AAC5C,UAAI,OAAO,UAAU,YAAY;AAC7B,YAAI;AACJ,cAAM,sBAAsB,WAAW,WAAc,SAAS,MAAM,MAAM;AAC1E,cAAM,qBAAsB,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,MAAM,KAAO,OAAO,WAAW,cAAc,CAAC,CAAC;AACpH,YAAI,qBAAqB;AACrB,iBAAO,SAAS,IAAI;AAAA,QACxB,WACS,oBAAoB;AACzB,iBAAO,SAAS,IAAI,MAAM;AAAA,QAC9B;AAAA,MACJ,OACK;AACD,cAAM,sBAAsB,WAAW,UAAa,SAAS;AAC7D,cAAM,qBAAsB,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,KAAK,KAAO,OAAO,WAAW,cAAc,CAAC,CAAC;AACnH,YAAI,uBAAuB,oBAAoB;AAC3C,iBAAO,SAAS,IAAI;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AACA,IAAM,aAAa,CAAC,MAAM,KAAK;AAC/B,IAAM,OAAO,CAAC,MAAM;AAAA;AAAA;;;AC3FpB;AAAA;AAAA;AAAA;;;ACAA,IAAAC,qBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,QAAQ,CAAC,QAAQ;AAC1B,UAAI,OAAO,MAAM;AACb,eAAO,CAAC;AAAA,MACZ;AACA,UAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAO,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK;AAAA,MACjD;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,cAAM,SAAS,CAAC;AAChB,mBAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AAChC,cAAI,IAAI,GAAG,KAAK,MAAM;AAClB;AAAA,UACJ;AACA,iBAAO,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,QAChC;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AClBA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA,IAAAC;AACA,IAAAC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAAC;AACA,IAAAC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAAC;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACxBA,IAGa,YAKA,eAKA,KAsEA,MAcA;AAjGb,IAAAC,kBAAA;;AAGO,IAAM,aAAqB;AAK3B,IAAM,gBAAwB;AAK9B,IAAM,MAAM,IAAI,YAAY;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;AAKM,IAAM,OAAO;MAClB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;AAMK,IAAM,sBAAsB,KAAA,IAAA,GAAK,EAAE,IAAG;;;;;ACjG7C,IAWA;AAXA;;IAAAC;AAWA,IAAA;IAAA,WAAA;AAAA,eAAAC,aAAA;AACU,aAAA,QAAoB,WAAW,KAAK,IAAI;AACxC,aAAA,OAAmB,IAAI,WAAW,EAAE;AACpC,aAAA,SAAqB,IAAI,WAAW,EAAE;AACtC,aAAA,eAAuB;AACvB,aAAA,cAAsB;AAK9B,aAAA,WAAoB;MA8ItB;AA5IE,MAAAA,WAAA,UAAA,SAAA,SAAO,MAAgB;AACrB,YAAI,KAAK,UAAU;AACjB,gBAAM,IAAI,MAAM,+CAA+C;;AAGjE,YAAI,WAAW;AACT,YAAA,aAAe,KAAI;AACzB,aAAK,eAAe;AAEpB,YAAI,KAAK,cAAc,IAAI,qBAAqB;AAC9C,gBAAM,IAAI,MAAM,qCAAqC;;AAGvD,eAAO,aAAa,GAAG;AACrB,eAAK,OAAO,KAAK,cAAc,IAAI,KAAK,UAAU;AAClD;AAEA,cAAI,KAAK,iBAAiB,YAAY;AACpC,iBAAK,WAAU;AACf,iBAAK,eAAe;;;MAG1B;AAEA,MAAAA,WAAA,UAAA,SAAA,WAAA;AACE,YAAI,CAAC,KAAK,UAAU;AAClB,cAAM,aAAa,KAAK,cAAc;AACtC,cAAM,aAAa,IAAI,SACrB,KAAK,OAAO,QACZ,KAAK,OAAO,YACZ,KAAK,OAAO,UAAU;AAGxB,cAAM,oBAAoB,KAAK;AAC/B,qBAAW,SAAS,KAAK,gBAAgB,GAAI;AAG7C,cAAI,oBAAoB,cAAc,aAAa,GAAG;AACpD,qBAAS,IAAI,KAAK,cAAc,IAAI,YAAY,KAAK;AACnD,yBAAW,SAAS,GAAG,CAAC;;AAE1B,iBAAK,WAAU;AACf,iBAAK,eAAe;;AAGtB,mBAAS,IAAI,KAAK,cAAc,IAAI,aAAa,GAAG,KAAK;AACvD,uBAAW,SAAS,GAAG,CAAC;;AAE1B,qBAAW,UACT,aAAa,GACb,KAAK,MAAM,aAAa,UAAW,GACnC,IAAI;AAEN,qBAAW,UAAU,aAAa,GAAG,UAAU;AAE/C,eAAK,WAAU;AAEf,eAAK,WAAW;;AAKlB,YAAM,MAAM,IAAI,WAAW,aAAa;AACxC,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,IAAI,CAAC,IAAK,KAAK,MAAM,CAAC,MAAM,KAAM;AACtC,cAAI,IAAI,IAAI,CAAC,IAAK,KAAK,MAAM,CAAC,MAAM,KAAM;AAC1C,cAAI,IAAI,IAAI,CAAC,IAAK,KAAK,MAAM,CAAC,MAAM,IAAK;AACzC,cAAI,IAAI,IAAI,CAAC,IAAK,KAAK,MAAM,CAAC,MAAM,IAAK;;AAG3C,eAAO;MACT;AAEQ,MAAAA,WAAA,UAAA,aAAR,WAAA;AACQ,YAAA,KAAoB,MAAlB,SAAM,GAAA,QAAE,QAAK,GAAA;AAErB,YAAI,SAAS,MAAM,CAAC,GAClB,SAAS,MAAM,CAAC,GAChB,SAAS,MAAM,CAAC,GAChB,SAAS,MAAM,CAAC,GAChB,SAAS,MAAM,CAAC,GAChB,SAAS,MAAM,CAAC,GAChB,SAAS,MAAM,CAAC,GAChB,SAAS,MAAM,CAAC;AAElB,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAI,IAAI,IAAI;AACV,iBAAK,KAAK,CAAC,KACP,OAAO,IAAI,CAAC,IAAI,QAAS,MACzB,OAAO,IAAI,IAAI,CAAC,IAAI,QAAS,MAC7B,OAAO,IAAI,IAAI,CAAC,IAAI,QAAS,IAC9B,OAAO,IAAI,IAAI,CAAC,IAAI;iBAClB;AACL,gBAAI,IAAI,KAAK,KAAK,IAAI,CAAC;AACvB,gBAAM,QACF,MAAM,KAAO,KAAK,OAAS,MAAM,KAAO,KAAK,MAAQ,MAAM;AAE/D,gBAAI,KAAK,KAAK,IAAI,EAAE;AACpB,gBAAM,QACF,MAAM,IAAM,KAAK,OAAS,MAAM,KAAO,KAAK,MAAQ,MAAM;AAE9D,iBAAK,KAAK,CAAC,KACP,OAAK,KAAK,KAAK,IAAI,CAAC,IAAK,MAAO,OAAK,KAAK,KAAK,IAAI,EAAE,IAAK;;AAGhE,cAAM,QACE,WAAW,IAAM,UAAU,OAC7B,WAAW,KAAO,UAAU,OAC5B,WAAW,KAAO,UAAU,OAC5B,SAAS,SAAW,CAAC,SAAS,UAChC,MACE,UAAW,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAK,KAAM,KAC9C;AAEF,cAAM,OACA,WAAW,IAAM,UAAU,OAC3B,WAAW,KAAO,UAAU,OAC5B,WAAW,KAAO,UAAU,QAC5B,SAAS,SAAW,SAAS,SAAW,SAAS,UACrD;AAEF,mBAAS;AACT,mBAAS;AACT,mBAAS;AACT,mBAAU,SAAS,KAAM;AACzB,mBAAS;AACT,mBAAS;AACT,mBAAS;AACT,mBAAU,KAAK,KAAM;;AAGvB,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;AACZ,cAAM,CAAC,KAAK;MACd;AACF,aAAAA;IAAA,EAxJA;;;;;ACXA,IAAaC;AAAb,IAAAC,yBAAA;AAAA;AAAO,IAAMD,YAAW,CAAC,UAAU,IAAI,YAAY,EAAE,OAAO,KAAK;AAAA;AAAA;;;ACAjE,IAAAE,qBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAAAC,uBAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACUM,SAAU,gBAAgB,MAAgB;AAE9C,MAAI,gBAAgB;AAAY,WAAO;AAEvC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAOC,UAAS,IAAI;;AAGtB,MAAI,YAAY,OAAO,IAAI,GAAG;AAC5B,WAAO,IAAI,WACT,KAAK,QACL,KAAK,YACL,KAAK,aAAa,WAAW,iBAAiB;;AAIlD,SAAO,IAAI,WAAW,IAAI;AAC5B;AA7BA,IAOMA;AAPN;;AAIA,IAAAC;AAGA,IAAMD,YACJ,OAAO,WAAW,eAAe,OAAO,OACpC,SAAC,OAAa;AAAK,aAAA,OAAO,KAAK,OAAO,MAAM;IAAzB,IACnBA;;;;;ACLA,SAAU,YAAY,MAAgB;AAC1C,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,WAAW;;AAGzB,SAAO,KAAK,eAAe;AAC7B;AAXA;;;;;;ACAA;;;;;;ACAA;;;;;;ACAA;;AAGA;AACA;AACA;AACA;;;;;AC2EA,SAAS,iBAAiB,QAAkB;AAC1C,MAAI,QAAQ,gBAAgB,MAAM;AAElC,MAAI,MAAM,aAAa,YAAY;AACjC,QAAM,aAAa,IAAI,UAAS;AAChC,eAAW,OAAO,KAAK;AACvB,YAAQ,WAAW,OAAM;;AAG3B,MAAM,SAAS,IAAI,WAAW,UAAU;AACxC,SAAO,IAAI,KAAK;AAChB,SAAO;AACT;IAxFA;;;;AALA,IAAAE;AACA;AAEA;AAEA,IAAA;IAAA,WAAA;AAME,eAAAC,QAAY,QAAmB;AAC7B,aAAK,SAAS;AACd,aAAK,OAAO,IAAI,UAAS;AACzB,aAAK,MAAK;MACZ;AAEA,MAAAA,QAAA,UAAA,SAAA,SAAO,QAAkB;AACvB,YAAI,YAAY,MAAM,KAAK,KAAK,OAAO;AACrC;;AAGF,YAAI;AACF,eAAK,KAAK,OAAO,gBAAgB,MAAM,CAAC;iBACjC,GAAG;AACV,eAAK,QAAQ;;MAEjB;AAKA,MAAAA,QAAA,UAAA,aAAA,WAAA;AACE,YAAI,KAAK,OAAO;AACd,gBAAM,KAAK;;AAGb,YAAI,KAAK,OAAO;AACd,cAAI,CAAC,KAAK,MAAM,UAAU;AACxB,iBAAK,MAAM,OAAO,KAAK,KAAK,OAAM,CAAE;;AAGtC,iBAAO,KAAK,MAAM,OAAM;;AAG1B,eAAO,KAAK,KAAK,OAAM;MACzB;AAOM,MAAAA,QAAA,UAAA,SAAN,WAAA;;;AACE,mBAAA,CAAA,GAAO,KAAK,WAAU,CAAE;;;;AAG1B,MAAAA,QAAA,UAAA,QAAA,WAAA;AACE,aAAK,OAAO,IAAI,UAAS;AACzB,YAAI,KAAK,QAAQ;AACf,eAAK,QAAQ,IAAI,UAAS;AAC1B,cAAM,QAAQ,iBAAiB,KAAK,MAAM;AAC1C,cAAM,QAAQ,IAAI,WAAW,UAAU;AACvC,gBAAM,IAAI,KAAK;AAEf,mBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,kBAAM,CAAC,KAAK;AACZ,kBAAM,CAAC,KAAK;;AAGd,eAAK,KAAK,OAAO,KAAK;AACtB,eAAK,MAAM,OAAO,KAAK;AAGvB,mBAAS,IAAI,GAAG,IAAI,MAAM,YAAY,KAAK;AACzC,kBAAM,CAAC,IAAI;;;MAGjB;AACF,aAAAA;IAAA,EA1EA;;;;;ACLA;;;;IAAAC,eAAA;;;;;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,UAAU,CAAC,UAAU,WAAW,oBAAoB;AAC7D,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,aAAa;AACjB,YAAM,mBAAmB,YAAY;AACjC,YAAI,CAAC,SAAS;AACV,oBAAU,SAAS;AAAA,QACvB;AACA,YAAI;AACA,qBAAW,MAAM;AACjB,sBAAY;AACZ,uBAAa;AAAA,QACjB,UACA;AACI,oBAAU;AAAA,QACd;AACA,eAAO;AAAA,MACX;AACA,UAAI,cAAc,QAAW;AACzB,eAAO,OAAO,YAAY;AACtB,cAAI,CAAC,cAAa,mCAAS,eAAc;AACrC,uBAAW,MAAM,iBAAiB;AAAA,UACtC;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,OAAO,YAAY;AACtB,YAAI,CAAC,cAAa,mCAAS,eAAc;AACrC,qBAAW,MAAM,iBAAiB;AAAA,QACtC;AACA,YAAI,YAAY;AACZ,iBAAO;AAAA,QACX;AACA,YAAI,mBAAmB,CAAC,gBAAgB,QAAQ,GAAG;AAC/C,uBAAa;AACb,iBAAO;AAAA,QACX;AACA,YAAI,UAAU,QAAQ,GAAG;AACrB,gBAAM,iBAAiB;AACvB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA;AAAA;;;AC5CA,IAAa;AAAb;AAAA;AAAO,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,MACrC,YAAY,SAAS,UAAU,MAAM;AADzC;AAEQ,YAAIC;AACJ,YAAI,cAAc;AAClB,YAAI,OAAO,YAAY,WAAW;AAC9B,UAAAA,UAAS;AACT,wBAAc;AAAA,QAClB,WACS,WAAW,QAAQ,OAAO,YAAY,UAAU;AACrD,UAAAA,UAAS,QAAQ;AACjB,wBAAc,QAAQ,eAAe;AAAA,QACzC;AACA,cAAM,OAAO;AACb,aAAK,OAAO;AACZ,aAAK,cAAc;AACnB,eAAO,eAAe,MAAM,eAAc,SAAS;AACnD,cAAAA,WAAA,gBAAAA,QAAQ,UAAR,wBAAAA,SAAgB,6BAA6B,cAAc,OAAO,KAAK,IAAI,OAAO;AAAA,MACtF;AAAA,MACA,OAAO,KAAK,OAAO,UAAU,MAAM;AAC/B,eAAO,OAAO,OAAO,IAAI,KAAK,MAAM,SAAS,OAAO,GAAG,KAAK;AAAA,MAChE;AAAA,IACJ;AAAA;AAAA;;;ACrBA,IACa;AADb;AAAA;AAAA;AACO,IAAM,2BAAN,MAAM,kCAAiC,cAAc;AAAA,MACxD,YAAY,SAAS,UAAU,MAAM;AACjC,cAAM,SAAS,OAAO;AACtB,aAAK,OAAO;AACZ,eAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,MAClE;AAAA,IACJ;AAAA;AAAA;;;ACPA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;", "names": ["entry", "HttpAuthLocation", "HttpApiKeyAuthLocation", "init_auth", "init_client", "EndpointURLScheme", "init_checksum", "AlgorithmId", "init_checksum", "init_checksum", "FieldPosition", "init_identity", "IniSectionType", "RequestHandlerProtocol", "init_dist_es", "init_auth", "init_client", "init_identity", "init_command", "init_dist_es", "logger", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_extensions", "init_dist_es", "init_dist_es", "init_extensions", "init_dist_es", "init_getSmithyContext", "init_dist_es", "init_dist_es", "normalizeProvider", "init_normalizeProvider", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "body", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_requestBuilder", "init_dist_es", "init_dist_es", "init_dist_es", "init_getSmithyContext", "init_normalizeProvider", "init_requestBuilder", "Client", "command", "init_collect_stream_body", "init_extended_encode_uri_component", "getChecksumConfiguration", "resolveChecksumRuntimeConfig", "init_checksum", "init_dist_es", "init_retry", "init_defaultExtensionConfiguration", "init_checksum", "init_retry", "getChecksumConfiguration", "resolveChecksumRuntimeConfig", "init_extensions", "init_defaultExtensionConfiguration", "LazyJsonString", "filter", "init_resolve_path", "init_dist_es", "init_collect_stream_body", "init_command", "init_extended_encode_uri_component", "init_extensions", "init_resolve_path", "init_constants", "init_constants", "RawSha256", "fromUtf8", "init_fromUtf8_browser", "init_toUint8Array", "init_fromUtf8_browser", "init_toUtf8_browser", "init_dist_es", "init_fromUtf8_browser", "init_toUint8Array", "init_toUtf8_browser", "fromUtf8", "init_dist_es", "init_constants", "Sha256", "init_module", "logger", "init_dist_es"]}