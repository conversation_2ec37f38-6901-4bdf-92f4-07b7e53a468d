{"version": 3, "sources": ["../../node_modules/@aws-sdk/middleware-host-header/dist-es/index.js", "../../node_modules/@aws-sdk/middleware-logger/dist-es/loggerMiddleware.js", "../../node_modules/@aws-sdk/middleware-logger/dist-es/index.js", "../../node_modules/@aws-sdk/middleware-recursion-detection/dist-es/index.js", "../../node_modules/@aws-sdk/middleware-user-agent/dist-es/configurations.js", "../../node_modules/@smithy/util-endpoints/dist-es/cache/EndpointCache.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/isIpAddress.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/isValidHostLabel.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js", "../../node_modules/@smithy/util-endpoints/dist-es/debug/debugId.js", "../../node_modules/@smithy/util-endpoints/dist-es/debug/toDebugString.js", "../../node_modules/@smithy/util-endpoints/dist-es/debug/index.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/EndpointError.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/EndpointFunctions.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/EndpointRuleObject.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/ErrorRuleObject.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/RuleSetObject.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/TreeRuleObject.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/shared.js", "../../node_modules/@smithy/util-endpoints/dist-es/types/index.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/booleanEquals.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/getAttrPathList.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/getAttr.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/isSet.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/not.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/parseURL.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/stringEquals.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/substring.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/uriEncode.js", "../../node_modules/@smithy/util-endpoints/dist-es/lib/index.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/endpointFunctions.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTemplate.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/getReferenceValue.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/callFunction.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateCondition.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointHeaders.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperty.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperties.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointUrl.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateEndpointRule.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateErrorRule.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTreeRule.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/evaluateRules.js", "../../node_modules/@smithy/util-endpoints/dist-es/utils/index.js", "../../node_modules/@smithy/util-endpoints/dist-es/resolveEndpoint.js", "../../node_modules/@smithy/util-endpoints/dist-es/index.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/lib/isIpAddress.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/isVirtualHostableS3Bucket.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/parseArn.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partitions.json", "../../node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partition.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/aws.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/resolveEndpoint.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointError.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointRuleObject.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/ErrorRuleObject.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/RuleSetObject.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/TreeRuleObject.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/shared.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/types/index.js", "../../node_modules/@aws-sdk/util-endpoints/dist-es/index.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/client/index.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getDateHeader.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getSkewCorrectedDate.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/isClockSkewed.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getUpdatedSystemClockOffset.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/index.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4ASigner.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getArrayForCommaSeparatedString.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4AConfig.js", "../../node_modules/@smithy/signature-v4/dist-es/constants.js", "../../node_modules/@smithy/signature-v4/dist-es/credentialDerivation.js", "../../node_modules/@smithy/signature-v4/dist-es/getCanonicalHeaders.js", "../../node_modules/@smithy/is-array-buffer/dist-es/index.js", "../../node_modules/@smithy/signature-v4/dist-es/getPayloadHash.js", "../../node_modules/@smithy/signature-v4/dist-es/HeaderFormatter.js", "../../node_modules/@smithy/signature-v4/dist-es/headerUtil.js", "../../node_modules/@smithy/signature-v4/dist-es/moveHeadersToQuery.js", "../../node_modules/@smithy/signature-v4/dist-es/prepareRequest.js", "../../node_modules/@smithy/signature-v4/dist-es/getCanonicalQuery.js", "../../node_modules/@smithy/signature-v4/dist-es/utilDate.js", "../../node_modules/@smithy/signature-v4/dist-es/SignatureV4Base.js", "../../node_modules/@smithy/signature-v4/dist-es/SignatureV4.js", "../../node_modules/@smithy/signature-v4/dist-es/signature-v4a-container.js", "../../node_modules/@smithy/signature-v4/dist-es/index.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/index.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/index.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/protocols/coercing-serializers.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/awsExpectUnion.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/protocols/common.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js", "../../node_modules/fast-xml-parser/src/util.js", "../../node_modules/fast-xml-parser/src/validator.js", "../../node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js", "../../node_modules/fast-xml-parser/src/xmlparser/xmlNode.js", "../../node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js", "../../node_modules/strnum/strnum.js", "../../node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js", "../../node_modules/fast-xml-parser/src/xmlparser/node2json.js", "../../node_modules/fast-xml-parser/src/xmlparser/XMLParser.js", "../../node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js", "../../node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js", "../../node_modules/fast-xml-parser/src/fxp.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/protocols/xml/parseXmlBody.js", "../../node_modules/@aws-sdk/core/dist-es/submodules/protocols/index.js", "../../node_modules/@aws-sdk/core/dist-es/index.js", "../../node_modules/@aws-sdk/middleware-user-agent/dist-es/check-features.js", "../../node_modules/@aws-sdk/middleware-user-agent/dist-es/constants.js", "../../node_modules/@aws-sdk/middleware-user-agent/dist-es/encode-features.js", "../../node_modules/@aws-sdk/middleware-user-agent/dist-es/user-agent-middleware.js", "../../node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js", "../../node_modules/@smithy/config-resolver/dist-es/regionConfig/isFipsRegion.js", "../../node_modules/@smithy/config-resolver/dist-es/regionConfig/getRealRegion.js", "../../node_modules/@smithy/config-resolver/dist-es/regionConfig/resolveRegionConfig.js", "../../node_modules/@smithy/util-config-provider/dist-es/booleanSelector.js", "../../node_modules/@smithy/util-config-provider/dist-es/numberSelector.js", "../../node_modules/@smithy/util-config-provider/dist-es/types.js", "../../node_modules/@smithy/util-config-provider/dist-es/index.js", "../../node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseDualstackEndpointConfigOptions.js", "../../node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseFipsEndpointConfigOptions.js", "../../node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveCustomEndpointsConfig.js", "../../node_modules/@smithy/config-resolver/dist-es/endpointsConfig/utils/getEndpointFromRegion.js", "../../node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveEndpointsConfig.js", "../../node_modules/@smithy/config-resolver/dist-es/endpointsConfig/index.js", "../../node_modules/@smithy/config-resolver/dist-es/regionConfig/config.js", "../../node_modules/@smithy/config-resolver/dist-es/regionConfig/index.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/PartitionHash.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/RegionHash.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/getHostnameFromVariants.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedHostname.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedPartition.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedSigningRegion.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/getRegionInfo.js", "../../node_modules/@smithy/config-resolver/dist-es/regionInfo/index.js", "../../node_modules/@smithy/config-resolver/dist-es/index.js", "../../node_modules/@smithy/middleware-content-length/dist-es/index.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/s3.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/index.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromConfig.browser.js", "../../node_modules/@smithy/querystring-parser/dist-es/index.js", "../../node_modules/@smithy/url-parser/dist-es/index.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/endpointMiddleware.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/getEndpointPlugin.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/resolveEndpointConfig.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/adaptors/index.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/types.js", "../../node_modules/@smithy/middleware-endpoint/dist-es/index.js", "../../node_modules/@smithy/util-retry/dist-es/config.js", "../../node_modules/@smithy/service-error-classification/dist-es/constants.js", "../../node_modules/@smithy/service-error-classification/dist-es/index.js", "../../node_modules/@smithy/util-retry/dist-es/DefaultRateLimiter.js", "../../node_modules/@smithy/util-retry/dist-es/constants.js", "../../node_modules/@smithy/util-retry/dist-es/defaultRetryBackoffStrategy.js", "../../node_modules/@smithy/util-retry/dist-es/defaultRetryToken.js", "../../node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js", "../../node_modules/@smithy/util-retry/dist-es/AdaptiveRetryStrategy.js", "../../node_modules/@smithy/util-retry/dist-es/ConfiguredRetryStrategy.js", "../../node_modules/@smithy/util-retry/dist-es/types.js", "../../node_modules/@smithy/util-retry/dist-es/index.js", "../../node_modules/@smithy/middleware-retry/dist-es/configurations.js", "../../node_modules/uuid/dist/esm-browser/rng.js", "../../node_modules/uuid/dist/esm-browser/regex.js", "../../node_modules/uuid/dist/esm-browser/validate.js", "../../node_modules/uuid/dist/esm-browser/stringify.js", "../../node_modules/uuid/dist/esm-browser/v1.js", "../../node_modules/uuid/dist/esm-browser/parse.js", "../../node_modules/uuid/dist/esm-browser/v35.js", "../../node_modules/uuid/dist/esm-browser/md5.js", "../../node_modules/uuid/dist/esm-browser/v3.js", "../../node_modules/uuid/dist/esm-browser/native.js", "../../node_modules/uuid/dist/esm-browser/v4.js", "../../node_modules/uuid/dist/esm-browser/sha1.js", "../../node_modules/uuid/dist/esm-browser/v5.js", "../../node_modules/uuid/dist/esm-browser/nil.js", "../../node_modules/uuid/dist/esm-browser/version.js", "../../node_modules/uuid/dist/esm-browser/index.js", "../../node_modules/@smithy/middleware-retry/dist-es/isStreamingPayload/isStreamingPayload.browser.js", "../../node_modules/@smithy/middleware-retry/dist-es/util.js", "../../node_modules/@smithy/middleware-retry/dist-es/retryMiddleware.js", "../../node_modules/@smithy/middleware-retry/dist-es/defaultRetryQuota.js", "../../node_modules/@smithy/middleware-retry/dist-es/delayDecider.js", "../../node_modules/@smithy/middleware-retry/dist-es/retryDecider.js", "../../node_modules/@smithy/middleware-retry/dist-es/StandardRetryStrategy.js", "../../node_modules/@smithy/middleware-retry/dist-es/AdaptiveRetryStrategy.js", "../../node_modules/@smithy/middleware-retry/dist-es/omitRetryHeadersMiddleware.js", "../../node_modules/@smithy/middleware-retry/dist-es/index.js", "../../node_modules/@aws-crypto/sha256-browser/src/constants.ts", "../../node_modules/@aws-sdk/util-locate-window/dist-es/index.js", "../../node_modules/@aws-crypto/sha256-browser/src/webCryptoSha256.ts", "../../node_modules/@aws-crypto/supports-web-crypto/src/supportsWebCrypto.ts", "../../node_modules/@aws-crypto/supports-web-crypto/src/index.ts", "../../node_modules/@aws-crypto/sha256-browser/src/crossPlatformSha256.ts", "../../node_modules/@aws-crypto/sha256-browser/src/index.ts", "../../node_modules/bowser/src/constants.js", "../../node_modules/bowser/src/utils.js", "../../node_modules/bowser/src/parser-browsers.js", "../../node_modules/bowser/src/parser-os.js", "../../node_modules/bowser/src/parser-platforms.js", "../../node_modules/bowser/src/parser-engines.js", "../../node_modules/bowser/src/parser.js", "../../node_modules/bowser/src/bowser.js", "../../node_modules/@aws-sdk/util-user-agent-browser/dist-es/index.js", "../../node_modules/@smithy/invalid-dependency/dist-es/invalidProvider.js", "../../node_modules/@smithy/invalid-dependency/dist-es/invalidFunction.js", "../../node_modules/@smithy/invalid-dependency/dist-es/index.js", "../../node_modules/@smithy/util-body-length-browser/dist-es/calculateBodyLength.js", "../../node_modules/@smithy/util-body-length-browser/dist-es/index.js", "../../node_modules/@smithy/util-defaults-mode-browser/dist-es/constants.js", "../../node_modules/@smithy/util-defaults-mode-browser/dist-es/resolveDefaultsModeConfig.js", "../../node_modules/@smithy/util-defaults-mode-browser/dist-es/index.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/extensions/index.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/config.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/isFipsRegion.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/getRealRegion.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/resolveRegionConfig.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/index.js", "../../node_modules/@aws-sdk/region-config-resolver/dist-es/index.js"], "sourcesContent": ["import { HttpRequest } from \"@smithy/protocol-http\";\nexport function resolveHostHeaderConfig(input) {\n    return input;\n}\nexport const hostHeaderMiddleware = (options) => (next) => async (args) => {\n    if (!HttpRequest.isInstance(args.request))\n        return next(args);\n    const { request } = args;\n    const { handlerProtocol = \"\" } = options.requestHandler.metadata || {};\n    if (handlerProtocol.indexOf(\"h2\") >= 0 && !request.headers[\":authority\"]) {\n        delete request.headers[\"host\"];\n        request.headers[\":authority\"] = request.hostname + (request.port ? \":\" + request.port : \"\");\n    }\n    else if (!request.headers[\"host\"]) {\n        let host = request.hostname;\n        if (request.port != null)\n            host += `:${request.port}`;\n        request.headers[\"host\"] = host;\n    }\n    return next(args);\n};\nexport const hostHeaderMiddlewareOptions = {\n    name: \"hostHeaderMiddleware\",\n    step: \"build\",\n    priority: \"low\",\n    tags: [\"HOST\"],\n    override: true,\n};\nexport const getHostHeaderPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(hostHeaderMiddleware(options), hostHeaderMiddlewareOptions);\n    },\n});\n", "export const loggerMiddleware = () => (next, context) => async (args) => {\n    try {\n        const response = await next(args);\n        const { clientName, commandName, logger, dynamoDbDocumentClientOptions = {} } = context;\n        const { overrideInputFilterSensitiveLog, overrideOutputFilterSensitiveLog } = dynamoDbDocumentClientOptions;\n        const inputFilterSensitiveLog = overrideInputFilterSensitiveLog ?? context.inputFilterSensitiveLog;\n        const outputFilterSensitiveLog = overrideOutputFilterSensitiveLog ?? context.outputFilterSensitiveLog;\n        const { $metadata, ...outputWithoutMetadata } = response.output;\n        logger?.info?.({\n            clientName,\n            commandName,\n            input: inputFilterSensitiveLog(args.input),\n            output: outputFilterSensitiveLog(outputWithoutMetadata),\n            metadata: $metadata,\n        });\n        return response;\n    }\n    catch (error) {\n        const { clientName, commandName, logger, dynamoDbDocumentClientOptions = {} } = context;\n        const { overrideInputFilterSensitiveLog } = dynamoDbDocumentClientOptions;\n        const inputFilterSensitiveLog = overrideInputFilterSensitiveLog ?? context.inputFilterSensitiveLog;\n        logger?.error?.({\n            clientName,\n            commandName,\n            input: inputFilterSensitiveLog(args.input),\n            error,\n            metadata: error.$metadata,\n        });\n        throw error;\n    }\n};\nexport const loggerMiddlewareOptions = {\n    name: \"loggerMiddleware\",\n    tags: [\"LOGGER\"],\n    step: \"initialize\",\n    override: true,\n};\nexport const getLoggerPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(loggerMiddleware(), loggerMiddlewareOptions);\n    },\n});\n", "export * from \"./loggerMiddleware\";\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nconst TRACE_ID_HEADER_NAME = \"X-Amzn-Trace-Id\";\nconst ENV_LAMBDA_FUNCTION_NAME = \"AWS_LAMBDA_FUNCTION_NAME\";\nconst ENV_TRACE_ID = \"_X_AMZN_TRACE_ID\";\nexport const recursionDetectionMiddleware = (options) => (next) => async (args) => {\n    const { request } = args;\n    if (!HttpRequest.isInstance(request) || options.runtime !== \"node\") {\n        return next(args);\n    }\n    const traceIdHeader = Object.keys(request.headers ?? {}).find((h) => h.toLowerCase() === TRACE_ID_HEADER_NAME.toLowerCase()) ??\n        TRACE_ID_HEADER_NAME;\n    if (request.headers.hasOwnProperty(traceIdHeader)) {\n        return next(args);\n    }\n    const functionName = process.env[ENV_LAMBDA_FUNCTION_NAME];\n    const traceId = process.env[ENV_TRACE_ID];\n    const nonEmptyString = (str) => typeof str === \"string\" && str.length > 0;\n    if (nonEmptyString(functionName) && nonEmptyString(traceId)) {\n        request.headers[TRACE_ID_HEADER_NAME] = traceId;\n    }\n    return next({\n        ...args,\n        request,\n    });\n};\nexport const addRecursionDetectionMiddlewareOptions = {\n    step: \"build\",\n    tags: [\"RECURSION_DETECTION\"],\n    name: \"recursionDetectionMiddleware\",\n    override: true,\n    priority: \"low\",\n};\nexport const getRecursionDetectionPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(recursionDetectionMiddleware(options), addRecursionDetectionMiddlewareOptions);\n    },\n});\n", "import { normalizeProvider } from \"@smithy/core\";\nexport const DEFAULT_UA_APP_ID = undefined;\nfunction isValidUserAgentAppId(appId) {\n    if (appId === undefined) {\n        return true;\n    }\n    return typeof appId === \"string\" && appId.length <= 50;\n}\nexport function resolveUserAgentConfig(input) {\n    const normalizedAppIdProvider = normalizeProvider(input.userAgentAppId ?? DEFAULT_UA_APP_ID);\n    const { customUserAgent } = input;\n    return Object.assign(input, {\n        customUserAgent: typeof customUserAgent === \"string\" ? [[customUserAgent]] : customUserAgent,\n        userAgentAppId: async () => {\n            const appId = await normalizedAppIdProvider();\n            if (!isValidUserAgentAppId(appId)) {\n                const logger = input.logger?.constructor?.name === \"NoOpLogger\" || !input.logger ? console : input.logger;\n                if (typeof appId !== \"string\") {\n                    logger?.warn(\"userAgentAppId must be a string or undefined.\");\n                }\n                else if (appId.length > 50) {\n                    logger?.warn(\"The provided userAgentAppId exceeds the maximum length of 50 characters.\");\n                }\n            }\n            return appId;\n        },\n    });\n}\n", "export class EndpointCache {\n    constructor({ size, params }) {\n        this.data = new Map();\n        this.parameters = [];\n        this.capacity = size ?? 50;\n        if (params) {\n            this.parameters = params;\n        }\n    }\n    get(endpointParams, resolver) {\n        const key = this.hash(endpointParams);\n        if (key === false) {\n            return resolver();\n        }\n        if (!this.data.has(key)) {\n            if (this.data.size > this.capacity + 10) {\n                const keys = this.data.keys();\n                let i = 0;\n                while (true) {\n                    const { value, done } = keys.next();\n                    this.data.delete(value);\n                    if (done || ++i > 10) {\n                        break;\n                    }\n                }\n            }\n            this.data.set(key, resolver());\n        }\n        return this.data.get(key);\n    }\n    size() {\n        return this.data.size;\n    }\n    hash(endpointParams) {\n        let buffer = \"\";\n        const { parameters } = this;\n        if (parameters.length === 0) {\n            return false;\n        }\n        for (const param of parameters) {\n            const val = String(endpointParams[param] ?? \"\");\n            if (val.includes(\"|;\")) {\n                return false;\n            }\n            buffer += val + \"|;\";\n        }\n        return buffer;\n    }\n}\n", "const IP_V4_REGEX = new RegExp(`^(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}$`);\nexport const isIpAddress = (value) => IP_V4_REGEX.test(value) || (value.startsWith(\"[\") && value.endsWith(\"]\"));\n", "const VALID_HOST_LABEL_REGEX = new RegExp(`^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$`);\nexport const isValidHostLabel = (value, allowSubDomains = false) => {\n    if (!allowSubDomains) {\n        return VALID_HOST_LABEL_REGEX.test(value);\n    }\n    const labels = value.split(\".\");\n    for (const label of labels) {\n        if (!isValidHostLabel(label)) {\n            return false;\n        }\n    }\n    return true;\n};\n", "export const customEndpointFunctions = {};\n", "export const debugId = \"endpoints\";\n", "export function toDebugString(input) {\n    if (typeof input !== \"object\" || input == null) {\n        return input;\n    }\n    if (\"ref\" in input) {\n        return `$${toDebugString(input.ref)}`;\n    }\n    if (\"fn\" in input) {\n        return `${input.fn}(${(input.argv || []).map(toDebugString).join(\", \")})`;\n    }\n    return JSON.stringify(input, null, 2);\n}\n", "export * from \"./debugId\";\nexport * from \"./toDebugString\";\n", "export class EndpointError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"EndpointError\";\n    }\n}\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./EndpointError\";\nexport * from \"./EndpointFunctions\";\nexport * from \"./EndpointRuleObject\";\nexport * from \"./ErrorRuleObject\";\nexport * from \"./RuleSetObject\";\nexport * from \"./TreeRuleObject\";\nexport * from \"./shared\";\n", "export const booleanEquals = (value1, value2) => value1 === value2;\n", "import { EndpointError } from \"../types\";\nexport const getAttrPathList = (path) => {\n    const parts = path.split(\".\");\n    const pathList = [];\n    for (const part of parts) {\n        const squareBracketIndex = part.indexOf(\"[\");\n        if (squareBracketIndex !== -1) {\n            if (part.indexOf(\"]\") !== part.length - 1) {\n                throw new EndpointError(`Path: '${path}' does not end with ']'`);\n            }\n            const arrayIndex = part.slice(squareBracketIndex + 1, -1);\n            if (Number.isNaN(parseInt(arrayIndex))) {\n                throw new EndpointError(`Invalid array index: '${arrayIndex}' in path: '${path}'`);\n            }\n            if (squareBracketIndex !== 0) {\n                pathList.push(part.slice(0, squareBracketIndex));\n            }\n            pathList.push(arrayIndex);\n        }\n        else {\n            pathList.push(part);\n        }\n    }\n    return pathList;\n};\n", "import { EndpointError } from \"../types\";\nimport { getAttrPathList } from \"./getAttrPathList\";\nexport const getAttr = (value, path) => getAttrPathList(path).reduce((acc, index) => {\n    if (typeof acc !== \"object\") {\n        throw new EndpointError(`Index '${index}' in '${path}' not found in '${JSON.stringify(value)}'`);\n    }\n    else if (Array.isArray(acc)) {\n        return acc[parseInt(index)];\n    }\n    return acc[index];\n}, value);\n", "export const isSet = (value) => value != null;\n", "export const not = (value) => !value;\n", "import { EndpointURLScheme } from \"@smithy/types\";\nimport { isIpAddress } from \"./isIpAddress\";\nconst DEFAULT_PORTS = {\n    [EndpointURLScheme.HTTP]: 80,\n    [EndpointURLScheme.HTTPS]: 443,\n};\nexport const parseURL = (value) => {\n    const whatwgURL = (() => {\n        try {\n            if (value instanceof URL) {\n                return value;\n            }\n            if (typeof value === \"object\" && \"hostname\" in value) {\n                const { hostname, port, protocol = \"\", path = \"\", query = {} } = value;\n                const url = new URL(`${protocol}//${hostname}${port ? `:${port}` : \"\"}${path}`);\n                url.search = Object.entries(query)\n                    .map(([k, v]) => `${k}=${v}`)\n                    .join(\"&\");\n                return url;\n            }\n            return new URL(value);\n        }\n        catch (error) {\n            return null;\n        }\n    })();\n    if (!whatwgURL) {\n        console.error(`Unable to parse ${JSON.stringify(value)} as a whatwg URL.`);\n        return null;\n    }\n    const urlString = whatwgURL.href;\n    const { host, hostname, pathname, protocol, search } = whatwgURL;\n    if (search) {\n        return null;\n    }\n    const scheme = protocol.slice(0, -1);\n    if (!Object.values(EndpointURLScheme).includes(scheme)) {\n        return null;\n    }\n    const isIp = isIpAddress(hostname);\n    const inputContainsDefaultPort = urlString.includes(`${host}:${DEFAULT_PORTS[scheme]}`) ||\n        (typeof value === \"string\" && value.includes(`${host}:${DEFAULT_PORTS[scheme]}`));\n    const authority = `${host}${inputContainsDefaultPort ? `:${DEFAULT_PORTS[scheme]}` : ``}`;\n    return {\n        scheme,\n        authority,\n        path: pathname,\n        normalizedPath: pathname.endsWith(\"/\") ? pathname : `${pathname}/`,\n        isIp,\n    };\n};\n", "export const stringEquals = (value1, value2) => value1 === value2;\n", "export const substring = (input, start, stop, reverse) => {\n    if (start >= stop || input.length < stop) {\n        return null;\n    }\n    if (!reverse) {\n        return input.substring(start, stop);\n    }\n    return input.substring(input.length - stop, input.length - start);\n};\n", "export const uriEncode = (value) => encodeURIComponent(value).replace(/[!*'()]/g, (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);\n", "export * from \"./booleanEquals\";\nexport * from \"./getAttr\";\nexport * from \"./isSet\";\nexport * from \"./isValidHostLabel\";\nexport * from \"./not\";\nexport * from \"./parseURL\";\nexport * from \"./stringEquals\";\nexport * from \"./substring\";\nexport * from \"./uriEncode\";\n", "import { booleanEquals, getAttr, isSet, isValidHostLabel, not, parseURL, stringEquals, substring, uriEncode, } from \"../lib\";\nexport const endpointFunctions = {\n    booleanEquals,\n    getAttr,\n    isSet,\n    isValidHostLabel,\n    not,\n    parseURL,\n    stringEquals,\n    substring,\n    uriEncode,\n};\n", "import { getAttr } from \"../lib\";\nexport const evaluateTemplate = (template, options) => {\n    const evaluatedTemplateArr = [];\n    const templateContext = {\n        ...options.endpointParams,\n        ...options.referenceRecord,\n    };\n    let currentIndex = 0;\n    while (currentIndex < template.length) {\n        const openingBraceIndex = template.indexOf(\"{\", currentIndex);\n        if (openingBraceIndex === -1) {\n            evaluatedTemplateArr.push(template.slice(currentIndex));\n            break;\n        }\n        evaluatedTemplateArr.push(template.slice(currentIndex, openingBraceIndex));\n        const closingBraceIndex = template.indexOf(\"}\", openingBraceIndex);\n        if (closingBraceIndex === -1) {\n            evaluatedTemplateArr.push(template.slice(openingBraceIndex));\n            break;\n        }\n        if (template[openingBraceIndex + 1] === \"{\" && template[closingBraceIndex + 1] === \"}\") {\n            evaluatedTemplateArr.push(template.slice(openingBraceIndex + 1, closingBraceIndex));\n            currentIndex = closingBraceIndex + 2;\n        }\n        const parameterName = template.substring(openingBraceIndex + 1, closingBraceIndex);\n        if (parameterName.includes(\"#\")) {\n            const [refName, attrName] = parameterName.split(\"#\");\n            evaluatedTemplateArr.push(getAttr(templateContext[refName], attrName));\n        }\n        else {\n            evaluatedTemplateArr.push(templateContext[parameterName]);\n        }\n        currentIndex = closingBraceIndex + 1;\n    }\n    return evaluatedTemplateArr.join(\"\");\n};\n", "export const getReferenceValue = ({ ref }, options) => {\n    const referenceRecord = {\n        ...options.endpointParams,\n        ...options.referenceRecord,\n    };\n    return referenceRecord[ref];\n};\n", "import { EndpointError } from \"../types\";\nimport { callFunction } from \"./callFunction\";\nimport { evaluateTemplate } from \"./evaluateTemplate\";\nimport { getReferenceValue } from \"./getReferenceValue\";\nexport const evaluateExpression = (obj, keyName, options) => {\n    if (typeof obj === \"string\") {\n        return evaluateTemplate(obj, options);\n    }\n    else if (obj[\"fn\"]) {\n        return callFunction(obj, options);\n    }\n    else if (obj[\"ref\"]) {\n        return getReferenceValue(obj, options);\n    }\n    throw new EndpointError(`'${keyName}': ${String(obj)} is not a string, function or reference.`);\n};\n", "import { customEndpointFunctions } from \"./customEndpointFunctions\";\nimport { endpointFunctions } from \"./endpointFunctions\";\nimport { evaluateExpression } from \"./evaluateExpression\";\nexport const callFunction = ({ fn, argv }, options) => {\n    const evaluatedArgs = argv.map((arg) => [\"boolean\", \"number\"].includes(typeof arg) ? arg : evaluateExpression(arg, \"arg\", options));\n    const fnSegments = fn.split(\".\");\n    if (fnSegments[0] in customEndpointFunctions && fnSegments[1] != null) {\n        return customEndpointFunctions[fnSegments[0]][fnSegments[1]](...evaluatedArgs);\n    }\n    return endpointFunctions[fn](...evaluatedArgs);\n};\n", "import { debugId, toDebugString } from \"../debug\";\nimport { EndpointError } from \"../types\";\nimport { callFunction } from \"./callFunction\";\nexport const evaluateCondition = ({ assign, ...fnArgs }, options) => {\n    if (assign && assign in options.referenceRecord) {\n        throw new EndpointError(`'${assign}' is already defined in Reference Record.`);\n    }\n    const value = callFunction(fnArgs, options);\n    options.logger?.debug?.(`${debugId} evaluateCondition: ${toDebugString(fnArgs)} = ${toDebugString(value)}`);\n    return {\n        result: value === \"\" ? true : !!value,\n        ...(assign != null && { toAssign: { name: assign, value } }),\n    };\n};\n", "import { debugId, toDebugString } from \"../debug\";\nimport { evaluateCondition } from \"./evaluateCondition\";\nexport const evaluateConditions = (conditions = [], options) => {\n    const conditionsReferenceRecord = {};\n    for (const condition of conditions) {\n        const { result, toAssign } = evaluateCondition(condition, {\n            ...options,\n            referenceRecord: {\n                ...options.referenceRecord,\n                ...conditionsReferenceRecord,\n            },\n        });\n        if (!result) {\n            return { result };\n        }\n        if (toAssign) {\n            conditionsReferenceRecord[toAssign.name] = toAssign.value;\n            options.logger?.debug?.(`${debugId} assign: ${toAssign.name} := ${toDebugString(toAssign.value)}`);\n        }\n    }\n    return { result: true, referenceRecord: conditionsReferenceRecord };\n};\n", "import { EndpointError } from \"../types\";\nimport { evaluateExpression } from \"./evaluateExpression\";\nexport const getEndpointHeaders = (headers, options) => Object.entries(headers).reduce((acc, [headerKey, headerVal]) => ({\n    ...acc,\n    [headerKey]: headerVal.map((headerValEntry) => {\n        const processedExpr = evaluateExpression(headerValEntry, \"Header value entry\", options);\n        if (typeof processedExpr !== \"string\") {\n            throw new EndpointError(`Header '${headerKey}' value '${processedExpr}' is not a string`);\n        }\n        return processedExpr;\n    }),\n}), {});\n", "import { EndpointError } from \"../types\";\nimport { evaluateTemplate } from \"./evaluateTemplate\";\nimport { getEndpointProperties } from \"./getEndpointProperties\";\nexport const getEndpointProperty = (property, options) => {\n    if (Array.isArray(property)) {\n        return property.map((propertyEntry) => getEndpointProperty(propertyEntry, options));\n    }\n    switch (typeof property) {\n        case \"string\":\n            return evaluateTemplate(property, options);\n        case \"object\":\n            if (property === null) {\n                throw new EndpointError(`Unexpected endpoint property: ${property}`);\n            }\n            return getEndpointProperties(property, options);\n        case \"boolean\":\n            return property;\n        default:\n            throw new EndpointError(`Unexpected endpoint property type: ${typeof property}`);\n    }\n};\n", "import { getEndpointProperty } from \"./getEndpointProperty\";\nexport const getEndpointProperties = (properties, options) => Object.entries(properties).reduce((acc, [propertyKey, propertyVal]) => ({\n    ...acc,\n    [propertyKey]: getEndpointProperty(propertyVal, options),\n}), {});\n", "import { EndpointError } from \"../types\";\nimport { evaluateExpression } from \"./evaluateExpression\";\nexport const getEndpointUrl = (endpointUrl, options) => {\n    const expression = evaluateExpression(endpointUrl, \"Endpoint URL\", options);\n    if (typeof expression === \"string\") {\n        try {\n            return new URL(expression);\n        }\n        catch (error) {\n            console.error(`Failed to construct URL with ${expression}`, error);\n            throw error;\n        }\n    }\n    throw new EndpointError(`Endpoint URL must be a string, got ${typeof expression}`);\n};\n", "import { debugId, toDebugString } from \"../debug\";\nimport { evaluateConditions } from \"./evaluateConditions\";\nimport { getEndpointHeaders } from \"./getEndpointHeaders\";\nimport { getEndpointProperties } from \"./getEndpointProperties\";\nimport { getEndpointUrl } from \"./getEndpointUrl\";\nexport const evaluateEndpointRule = (endpointRule, options) => {\n    const { conditions, endpoint } = endpointRule;\n    const { result, referenceRecord } = evaluateConditions(conditions, options);\n    if (!result) {\n        return;\n    }\n    const endpointRuleOptions = {\n        ...options,\n        referenceRecord: { ...options.referenceRecord, ...referenceRecord },\n    };\n    const { url, properties, headers } = endpoint;\n    options.logger?.debug?.(`${debugId} Resolving endpoint from template: ${toDebugString(endpoint)}`);\n    return {\n        ...(headers != undefined && {\n            headers: getEndpointHeaders(headers, endpointRuleOptions),\n        }),\n        ...(properties != undefined && {\n            properties: getEndpointProperties(properties, endpointRuleOptions),\n        }),\n        url: getEndpointUrl(url, endpointRuleOptions),\n    };\n};\n", "import { EndpointError } from \"../types\";\nimport { evaluateConditions } from \"./evaluateConditions\";\nimport { evaluateExpression } from \"./evaluateExpression\";\nexport const evaluateErrorRule = (errorRule, options) => {\n    const { conditions, error } = errorRule;\n    const { result, referenceRecord } = evaluateConditions(conditions, options);\n    if (!result) {\n        return;\n    }\n    throw new EndpointError(evaluateExpression(error, \"Error\", {\n        ...options,\n        referenceRecord: { ...options.referenceRecord, ...referenceRecord },\n    }));\n};\n", "import { evaluateConditions } from \"./evaluateConditions\";\nimport { evaluateRules } from \"./evaluateRules\";\nexport const evaluateTreeRule = (treeRule, options) => {\n    const { conditions, rules } = treeRule;\n    const { result, referenceRecord } = evaluateConditions(conditions, options);\n    if (!result) {\n        return;\n    }\n    return evaluateRules(rules, {\n        ...options,\n        referenceRecord: { ...options.referenceRecord, ...referenceRecord },\n    });\n};\n", "import { EndpointError } from \"../types\";\nimport { evaluateEndpointRule } from \"./evaluateEndpointRule\";\nimport { evaluateErrorRule } from \"./evaluateErrorRule\";\nimport { evaluateTreeRule } from \"./evaluateTreeRule\";\nexport const evaluateRules = (rules, options) => {\n    for (const rule of rules) {\n        if (rule.type === \"endpoint\") {\n            const endpointOrUndefined = evaluateEndpointRule(rule, options);\n            if (endpointOrUndefined) {\n                return endpointOrUndefined;\n            }\n        }\n        else if (rule.type === \"error\") {\n            evaluateErrorRule(rule, options);\n        }\n        else if (rule.type === \"tree\") {\n            const endpointOrUndefined = evaluateTreeRule(rule, options);\n            if (endpointOrUndefined) {\n                return endpointOrUndefined;\n            }\n        }\n        else {\n            throw new EndpointError(`Unknown endpoint rule: ${rule}`);\n        }\n    }\n    throw new EndpointError(`Rules evaluation failed`);\n};\n", "export * from \"./customEndpointFunctions\";\nexport * from \"./evaluateRules\";\n", "import { debugId, toDebugString } from \"./debug\";\nimport { EndpointError } from \"./types\";\nimport { evaluateRules } from \"./utils\";\nexport const resolveEndpoint = (ruleSetObject, options) => {\n    const { endpointParams, logger } = options;\n    const { parameters, rules } = ruleSetObject;\n    options.logger?.debug?.(`${debugId} Initial EndpointParams: ${toDebugString(endpointParams)}`);\n    const paramsWithDefault = Object.entries(parameters)\n        .filter(([, v]) => v.default != null)\n        .map(([k, v]) => [k, v.default]);\n    if (paramsWithDefault.length > 0) {\n        for (const [paramKey, paramDefaultValue] of paramsWithDefault) {\n            endpointParams[paramKey] = endpointParams[paramKey] ?? paramDefaultValue;\n        }\n    }\n    const requiredParams = Object.entries(parameters)\n        .filter(([, v]) => v.required)\n        .map(([k]) => k);\n    for (const requiredParam of requiredParams) {\n        if (endpointParams[requiredParam] == null) {\n            throw new EndpointError(`Missing required parameter: '${requiredParam}'`);\n        }\n    }\n    const endpoint = evaluateRules(rules, { endpointParams, logger, referenceRecord: {} });\n    options.logger?.debug?.(`${debugId} Resolved endpoint: ${toDebugString(endpoint)}`);\n    return endpoint;\n};\n", "export * from \"./cache/EndpointCache\";\nexport * from \"./lib/isIpAddress\";\nexport * from \"./lib/isValidHostLabel\";\nexport * from \"./utils/customEndpointFunctions\";\nexport * from \"./resolveEndpoint\";\nexport * from \"./types\";\n", "export { isIpAddress } from \"@smithy/util-endpoints\";\n", "import { isValidHostLabel } from \"@smithy/util-endpoints\";\nimport { isIpAddress } from \"../isIpAddress\";\nexport const isVirtualHostableS3Bucket = (value, allowSubDomains = false) => {\n    if (allowSubDomains) {\n        for (const label of value.split(\".\")) {\n            if (!isVirtualHostableS3Bucket(label)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (!isValidHostLabel(value)) {\n        return false;\n    }\n    if (value.length < 3 || value.length > 63) {\n        return false;\n    }\n    if (value !== value.toLowerCase()) {\n        return false;\n    }\n    if (isIpAddress(value)) {\n        return false;\n    }\n    return true;\n};\n", "const ARN_DELIMITER = \":\";\nconst RESOURCE_DELIMITER = \"/\";\nexport const parseArn = (value) => {\n    const segments = value.split(ARN_DELIMITER);\n    if (segments.length < 6)\n        return null;\n    const [arn, partition, service, region, accountId, ...resourcePath] = segments;\n    if (arn !== \"arn\" || partition === \"\" || service === \"\" || resourcePath.join(ARN_DELIMITER) === \"\")\n        return null;\n    const resourceId = resourcePath.map((resource) => resource.split(RESOURCE_DELIMITER)).flat();\n    return {\n        partition,\n        service,\n        region,\n        accountId,\n        resourceId,\n    };\n};\n", "{\n    \"partitions\": [{\n            \"id\": \"aws\",\n            \"outputs\": {\n                \"dnsSuffix\": \"amazonaws.com\",\n                \"dualStackDnsSuffix\": \"api.aws\",\n                \"implicitGlobalRegion\": \"us-east-1\",\n                \"name\": \"aws\",\n                \"supportsDualStack\": true,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^(us|eu|ap|sa|ca|me|af|il|mx)\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"af-south-1\": {\n                    \"description\": \"Africa (Cape Town)\"\n                },\n                \"ap-east-1\": {\n                    \"description\": \"Asia Pacific (Hong Kong)\"\n                },\n                \"ap-northeast-1\": {\n                    \"description\": \"Asia Pacific (Tokyo)\"\n                },\n                \"ap-northeast-2\": {\n                    \"description\": \"Asia Pacific (Seoul)\"\n                },\n                \"ap-northeast-3\": {\n                    \"description\": \"Asia Pacific (Osaka)\"\n                },\n                \"ap-south-1\": {\n                    \"description\": \"Asia Pacific (Mumbai)\"\n                },\n                \"ap-south-2\": {\n                    \"description\": \"Asia Pacific (Hyderabad)\"\n                },\n                \"ap-southeast-1\": {\n                    \"description\": \"Asia Pacific (Singapore)\"\n                },\n                \"ap-southeast-2\": {\n                    \"description\": \"Asia Pacific (Sydney)\"\n                },\n                \"ap-southeast-3\": {\n                    \"description\": \"Asia Pacific (Jakarta)\"\n                },\n                \"ap-southeast-4\": {\n                    \"description\": \"Asia Pacific (Melbourne)\"\n                },\n                \"ap-southeast-5\": {\n                    \"description\": \"Asia Pacific (Malaysia)\"\n                },\n                \"ap-southeast-7\": {\n                    \"description\": \"Asia Pacific (Thailand)\"\n                },\n                \"aws-global\": {\n                    \"description\": \"AWS Standard global region\"\n                },\n                \"ca-central-1\": {\n                    \"description\": \"Canada (Central)\"\n                },\n                \"ca-west-1\": {\n                    \"description\": \"Canada West (Calgary)\"\n                },\n                \"eu-central-1\": {\n                    \"description\": \"Europe (Frankfurt)\"\n                },\n                \"eu-central-2\": {\n                    \"description\": \"Europe (Zurich)\"\n                },\n                \"eu-north-1\": {\n                    \"description\": \"Europe (Stockholm)\"\n                },\n                \"eu-south-1\": {\n                    \"description\": \"Europe (Milan)\"\n                },\n                \"eu-south-2\": {\n                    \"description\": \"Europe (Spain)\"\n                },\n                \"eu-west-1\": {\n                    \"description\": \"Europe (Ireland)\"\n                },\n                \"eu-west-2\": {\n                    \"description\": \"Europe (London)\"\n                },\n                \"eu-west-3\": {\n                    \"description\": \"Europe (Paris)\"\n                },\n                \"il-central-1\": {\n                    \"description\": \"Israel (Tel Aviv)\"\n                },\n                \"me-central-1\": {\n                    \"description\": \"Middle East (UAE)\"\n                },\n                \"me-south-1\": {\n                    \"description\": \"Middle East (Bahrain)\"\n                },\n                \"mx-central-1\": {\n                    \"description\": \"Mexico (Central)\"\n                },\n                \"sa-east-1\": {\n                    \"description\": \"South America (Sao Paulo)\"\n                },\n                \"us-east-1\": {\n                    \"description\": \"US East (N. Virginia)\"\n                },\n                \"us-east-2\": {\n                    \"description\": \"US East (Ohio)\"\n                },\n                \"us-west-1\": {\n                    \"description\": \"US West (N. California)\"\n                },\n                \"us-west-2\": {\n                    \"description\": \"US West (Oregon)\"\n                }\n            }\n        }, {\n            \"id\": \"aws-cn\",\n            \"outputs\": {\n                \"dnsSuffix\": \"amazonaws.com.cn\",\n                \"dualStackDnsSuffix\": \"api.amazonwebservices.com.cn\",\n                \"implicitGlobalRegion\": \"cn-northwest-1\",\n                \"name\": \"aws-cn\",\n                \"supportsDualStack\": true,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^cn\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"aws-cn-global\": {\n                    \"description\": \"AWS China global region\"\n                },\n                \"cn-north-1\": {\n                    \"description\": \"China (Beijing)\"\n                },\n                \"cn-northwest-1\": {\n                    \"description\": \"China (Ningxia)\"\n                }\n            }\n        }, {\n            \"id\": \"aws-us-gov\",\n            \"outputs\": {\n                \"dnsSuffix\": \"amazonaws.com\",\n                \"dualStackDnsSuffix\": \"api.aws\",\n                \"implicitGlobalRegion\": \"us-gov-west-1\",\n                \"name\": \"aws-us-gov\",\n                \"supportsDualStack\": true,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"aws-us-gov-global\": {\n                    \"description\": \"AWS GovCloud (US) global region\"\n                },\n                \"us-gov-east-1\": {\n                    \"description\": \"AWS GovCloud (US-East)\"\n                },\n                \"us-gov-west-1\": {\n                    \"description\": \"AWS GovCloud (US-West)\"\n                }\n            }\n        }, {\n            \"id\": \"aws-iso\",\n            \"outputs\": {\n                \"dnsSuffix\": \"c2s.ic.gov\",\n                \"dualStackDnsSuffix\": \"c2s.ic.gov\",\n                \"implicitGlobalRegion\": \"us-iso-east-1\",\n                \"name\": \"aws-iso\",\n                \"supportsDualStack\": false,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"aws-iso-global\": {\n                    \"description\": \"AWS ISO (US) global region\"\n                },\n                \"us-iso-east-1\": {\n                    \"description\": \"US ISO East\"\n                },\n                \"us-iso-west-1\": {\n                    \"description\": \"US ISO WEST\"\n                }\n            }\n        }, {\n            \"id\": \"aws-iso-b\",\n            \"outputs\": {\n                \"dnsSuffix\": \"sc2s.sgov.gov\",\n                \"dualStackDnsSuffix\": \"sc2s.sgov.gov\",\n                \"implicitGlobalRegion\": \"us-isob-east-1\",\n                \"name\": \"aws-iso-b\",\n                \"supportsDualStack\": false,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"aws-iso-b-global\": {\n                    \"description\": \"AWS ISOB (US) global region\"\n                },\n                \"us-isob-east-1\": {\n                    \"description\": \"US ISOB East (Ohio)\"\n                }\n            }\n        }, {\n            \"id\": \"aws-iso-e\",\n            \"outputs\": {\n                \"dnsSuffix\": \"cloud.adc-e.uk\",\n                \"dualStackDnsSuffix\": \"cloud.adc-e.uk\",\n                \"implicitGlobalRegion\": \"eu-isoe-west-1\",\n                \"name\": \"aws-iso-e\",\n                \"supportsDualStack\": false,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^eu\\\\-isoe\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"aws-iso-e-global\": {\n                    \"description\": \"AWS ISOE (Europe) global region\"\n                },\n                \"eu-isoe-west-1\": {\n                    \"description\": \"EU ISOE West\"\n                }\n            }\n        }, {\n            \"id\": \"aws-iso-f\",\n            \"outputs\": {\n                \"dnsSuffix\": \"csp.hci.ic.gov\",\n                \"dualStackDnsSuffix\": \"csp.hci.ic.gov\",\n                \"implicitGlobalRegion\": \"us-isof-south-1\",\n                \"name\": \"aws-iso-f\",\n                \"supportsDualStack\": false,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^us\\\\-isof\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"aws-iso-f-global\": {\n                    \"description\": \"AWS ISOF global region\"\n                },\n                \"us-isof-east-1\": {\n                    \"description\": \"US ISOF EAST\"\n                },\n                \"us-isof-south-1\": {\n                    \"description\": \"US ISOF SOUTH\"\n                }\n            }\n        }, {\n            \"id\": \"aws-eusc\",\n            \"outputs\": {\n                \"dnsSuffix\": \"amazonaws.eu\",\n                \"dualStackDnsSuffix\": \"amazonaws.eu\",\n                \"implicitGlobalRegion\": \"eusc-de-east-1\",\n                \"name\": \"aws-eusc\",\n                \"supportsDualStack\": false,\n                \"supportsFIPS\": true\n            },\n            \"regionRegex\": \"^eusc\\\\-(de)\\\\-\\\\w+\\\\-\\\\d+$\",\n            \"regions\": {\n                \"eusc-de-east-1\": {\n                    \"description\": \"EU (Germany)\"\n                }\n            }\n        }],\n    \"version\": \"1.1\"\n}\n", "import partitionsInfo from \"./partitions.json\";\nlet selectedPartitionsInfo = partitionsInfo;\nlet selectedUserAgentPrefix = \"\";\nexport const partition = (value) => {\n    const { partitions } = selectedPartitionsInfo;\n    for (const partition of partitions) {\n        const { regions, outputs } = partition;\n        for (const [region, regionData] of Object.entries(regions)) {\n            if (region === value) {\n                return {\n                    ...outputs,\n                    ...regionData,\n                };\n            }\n        }\n    }\n    for (const partition of partitions) {\n        const { regionRegex, outputs } = partition;\n        if (new RegExp(regionRegex).test(value)) {\n            return {\n                ...outputs,\n            };\n        }\n    }\n    const DEFAULT_PARTITION = partitions.find((partition) => partition.id === \"aws\");\n    if (!DEFAULT_PARTITION) {\n        throw new Error(\"Provided region was not found in the partition array or regex,\" +\n            \" and default partition with id 'aws' doesn't exist.\");\n    }\n    return {\n        ...DEFAULT_PARTITION.outputs,\n    };\n};\nexport const setPartitionInfo = (partitionsInfo, userAgentPrefix = \"\") => {\n    selectedPartitionsInfo = partitionsInfo;\n    selectedUserAgentPrefix = userAgentPrefix;\n};\nexport const useDefaultPartitionInfo = () => {\n    setPartitionInfo(partitionsInfo, \"\");\n};\nexport const getUserAgentPrefix = () => selectedUserAgentPrefix;\n", "import { customEndpointFunctions } from \"@smithy/util-endpoints\";\nimport { isVirtualHostableS3Bucket } from \"./lib/aws/isVirtualHostableS3Bucket\";\nimport { parseArn } from \"./lib/aws/parseArn\";\nimport { partition } from \"./lib/aws/partition\";\nexport const awsEndpointFunctions = {\n    isVirtualHostableS3Bucket: isVirtualHostableS3Bucket,\n    parseArn: parseArn,\n    partition: partition,\n};\ncustomEndpointFunctions.aws = awsEndpointFunctions;\n", "export { resolveEndpoint } from \"@smithy/util-endpoints\";\n", "export { EndpointError } from \"@smithy/util-endpoints\";\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export {};\n", "export * from \"./EndpointError\";\nexport * from \"./EndpointRuleObject\";\nexport * from \"./ErrorRuleObject\";\nexport * from \"./RuleSetObject\";\nexport * from \"./TreeRuleObject\";\nexport * from \"./shared\";\n", "export * from \"./aws\";\nexport * from \"./lib/aws/partition\";\nexport * from \"./lib/isIpAddress\";\nexport * from \"./resolveEndpoint\";\nexport * from \"./types\";\n", "export const state = {\n    warningEmitted: false,\n};\nexport const emitWarningIfUnsupportedVersion = (version) => {\n    if (version && !state.warningEmitted && parseInt(version.substring(1, version.indexOf(\".\"))) < 18) {\n        state.warningEmitted = true;\n        process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will\nno longer support Node.js 16.x on January 6, 2025.\n\nTo continue receiving updates to AWS services, bug fixes, and security\nupdates please upgrade to a supported Node.js LTS version.\n\nMore information can be found at: https://a.co/74kJMmI`);\n    }\n};\n", "export function setCredentialFeature(credentials, feature, value) {\n    if (!credentials.$source) {\n        credentials.$source = {};\n    }\n    credentials.$source[feature] = value;\n    return credentials;\n}\n", "export function setFeature(context, feature, value) {\n    if (!context.__aws_sdk_context) {\n        context.__aws_sdk_context = {\n            features: {},\n        };\n    }\n    else if (!context.__aws_sdk_context.features) {\n        context.__aws_sdk_context.features = {};\n    }\n    context.__aws_sdk_context.features[feature] = value;\n}\n", "export * from \"./emitWarningIfUnsupportedVersion\";\nexport * from \"./setCredentialFeature\";\nexport * from \"./setFeature\";\n", "import { HttpResponse } from \"@smithy/protocol-http\";\nexport const getDateHeader = (response) => HttpResponse.isInstance(response) ? response.headers?.date ?? response.headers?.Date : undefined;\n", "export const getSkewCorrectedDate = (systemClockOffset) => new Date(Date.now() + systemClockOffset);\n", "import { getSkewCorrectedDate } from \"./getSkewCorrectedDate\";\nexport const isClockSkewed = (clockTime, systemClockOffset) => Math.abs(getSkewCorrectedDate(systemClockOffset).getTime() - clockTime) >= 300000;\n", "import { isClockSkewed } from \"./isClockSkewed\";\nexport const getUpdatedSystemClockOffset = (clockTime, currentSystemClockOffset) => {\n    const clockTimeInMs = Date.parse(clockTime);\n    if (isClockSkewed(clockTimeInMs, currentSystemClockOffset)) {\n        return clockTimeInMs - Date.now();\n    }\n    return currentSystemClockOffset;\n};\n", "export * from \"./getDateHeader\";\nexport * from \"./getSkewCorrectedDate\";\nexport * from \"./getUpdatedSystemClockOffset\";\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { getDateHeader, getSkewCorrectedDate, getUpdatedSystemClockOffset } from \"../utils\";\nconst throwSigningPropertyError = (name, property) => {\n    if (!property) {\n        throw new Error(`Property \\`${name}\\` is not resolved for AWS SDK SigV4Auth`);\n    }\n    return property;\n};\nexport const validateSigningProperties = async (signingProperties) => {\n    const context = throwSigningPropertyError(\"context\", signingProperties.context);\n    const config = throwSigningPropertyError(\"config\", signingProperties.config);\n    const authScheme = context.endpointV2?.properties?.authSchemes?.[0];\n    const signerFunction = throwSigningPropertyError(\"signer\", config.signer);\n    const signer = await signerFunction(authScheme);\n    const signingRegion = signingProperties?.signingRegion;\n    const signingRegionSet = signingProperties?.signingRegionSet;\n    const signingName = signingProperties?.signingName;\n    return {\n        config,\n        signer,\n        signingRegion,\n        signingRegionSet,\n        signingName,\n    };\n};\nexport class AwsSdkSigV4Signer {\n    async sign(httpRequest, identity, signingProperties) {\n        if (!HttpRequest.isInstance(httpRequest)) {\n            throw new Error(\"The request is not an instance of `HttpRequest` and cannot be signed\");\n        }\n        const validatedProps = await validateSigningProperties(signingProperties);\n        const { config, signer } = validatedProps;\n        let { signingRegion, signingName } = validatedProps;\n        const handlerExecutionContext = signingProperties.context;\n        if (handlerExecutionContext?.authSchemes?.length ?? 0 > 1) {\n            const [first, second] = handlerExecutionContext.authSchemes;\n            if (first?.name === \"sigv4a\" && second?.name === \"sigv4\") {\n                signingRegion = second?.signingRegion ?? signingRegion;\n                signingName = second?.signingName ?? signingName;\n            }\n        }\n        const signedRequest = await signer.sign(httpRequest, {\n            signingDate: getSkewCorrectedDate(config.systemClockOffset),\n            signingRegion: signingRegion,\n            signingService: signingName,\n        });\n        return signedRequest;\n    }\n    errorHandler(signingProperties) {\n        return (error) => {\n            const serverTime = error.ServerTime ?? getDateHeader(error.$response);\n            if (serverTime) {\n                const config = throwSigningPropertyError(\"config\", signingProperties.config);\n                const initialSystemClockOffset = config.systemClockOffset;\n                config.systemClockOffset = getUpdatedSystemClockOffset(serverTime, config.systemClockOffset);\n                const clockSkewCorrected = config.systemClockOffset !== initialSystemClockOffset;\n                if (clockSkewCorrected && error.$metadata) {\n                    error.$metadata.clockSkewCorrected = true;\n                }\n            }\n            throw error;\n        };\n    }\n    successHandler(httpResponse, signingProperties) {\n        const dateHeader = getDateHeader(httpResponse);\n        if (dateHeader) {\n            const config = throwSigningPropertyError(\"config\", signingProperties.config);\n            config.systemClockOffset = getUpdatedSystemClockOffset(dateHeader, config.systemClockOffset);\n        }\n    }\n}\nexport const AWSSDKSigV4Signer = AwsSdkSigV4Signer;\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { getSkewCorrectedDate } from \"../utils\";\nimport { AwsSdkSigV4Signer, validateSigningProperties } from \"./AwsSdkSigV4Signer\";\nexport class AwsSdkSigV4ASigner extends AwsSdkSigV4Signer {\n    async sign(httpRequest, identity, signingProperties) {\n        if (!HttpRequest.isInstance(httpRequest)) {\n            throw new Error(\"The request is not an instance of `HttpRequest` and cannot be signed\");\n        }\n        const { config, signer, signingRegion, signingRegionSet, signingName } = await validateSigningProperties(signingProperties);\n        const configResolvedSigningRegionSet = await config.sigv4aSigningRegionSet?.();\n        const multiRegionOverride = (configResolvedSigningRegionSet ??\n            signingRegionSet ?? [signingRegion]).join(\",\");\n        const signedRequest = await signer.sign(httpRequest, {\n            signingDate: getSkewCorrectedDate(config.systemClockOffset),\n            signingRegion: multiRegionOverride,\n            signingService: signingName,\n        });\n        return signedRequest;\n    }\n}\n", "export const getArrayForCommaSeparatedString = (str) => typeof str === \"string\" && str.length > 0 ? str.split(\",\").map((item) => item.trim()) : [];\n", "import { getArrayForCommaSeparatedString } from \"../utils/getArrayForCommaSeparatedString\";\nconst NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY = \"AWS_AUTH_SCHEME_PREFERENCE\";\nconst NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY = \"auth_scheme_preference\";\nexport const NODE_AUTH_SCHEME_PREFERENCE_OPTIONS = {\n    environmentVariableSelector: (env) => {\n        if (!(NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY in env))\n            return undefined;\n        return getArrayForCommaSeparatedString(env[NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY]);\n    },\n    configFileSelector: (profile) => {\n        if (!(NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY in profile))\n            return undefined;\n        return getArrayForCommaSeparatedString(profile[NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY]);\n    },\n    default: [],\n};\n", "import { normalizeProvider } from \"@smithy/core\";\nimport { ProviderError } from \"@smithy/property-provider\";\nexport const resolveAwsSdkSigV4AConfig = (config) => {\n    config.sigv4aSigningRegionSet = normalizeProvider(config.sigv4aSigningRegionSet);\n    return config;\n};\nexport const NODE_SIGV4A_CONFIG_OPTIONS = {\n    environmentVariableSelector(env) {\n        if (env.AWS_SIGV4A_SIGNING_REGION_SET) {\n            return env.AWS_SIGV4A_SIGNING_REGION_SET.split(\",\").map((_) => _.trim());\n        }\n        throw new ProviderError(\"AWS_SIGV4A_SIGNING_REGION_SET not set in env.\", {\n            tryNextLink: true,\n        });\n    },\n    configFileSelector(profile) {\n        if (profile.sigv4a_signing_region_set) {\n            return (profile.sigv4a_signing_region_set ?? \"\").split(\",\").map((_) => _.trim());\n        }\n        throw new ProviderError(\"sigv4a_signing_region_set not set in profile.\", {\n            tryNextLink: true,\n        });\n    },\n    default: undefined,\n};\n", "export const ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nexport const CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nexport const AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nexport const SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nexport const EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nexport const SIGNATURE_QUERY_PARAM = \"X-Amz-Signature\";\nexport const TOKEN_QUERY_PARAM = \"X-Amz-Security-Token\";\nexport const REGION_SET_PARAM = \"X-Amz-Region-Set\";\nexport const AUTH_HEADER = \"authorization\";\nexport const AMZ_DATE_HEADER = AMZ_DATE_QUERY_PARAM.toLowerCase();\nexport const DATE_HEADER = \"date\";\nexport const GENERATED_HEADERS = [AUTH_HEADER, AMZ_DATE_HEADER, DATE_HEADER];\nexport const SIGNATURE_HEADER = SIGNATURE_QUERY_PARAM.toLowerCase();\nexport const SHA256_HEADER = \"x-amz-content-sha256\";\nexport const TOKEN_HEADER = TOKEN_QUERY_PARAM.toLowerCase();\nexport const HOST_HEADER = \"host\";\nexport const ALWAYS_UNSIGNABLE_HEADERS = {\n    authorization: true,\n    \"cache-control\": true,\n    connection: true,\n    expect: true,\n    from: true,\n    \"keep-alive\": true,\n    \"max-forwards\": true,\n    pragma: true,\n    referer: true,\n    te: true,\n    trailer: true,\n    \"transfer-encoding\": true,\n    upgrade: true,\n    \"user-agent\": true,\n    \"x-amzn-trace-id\": true,\n};\nexport const PROXY_HEADER_PATTERN = /^proxy-/;\nexport const SEC_HEADER_PATTERN = /^sec-/;\nexport const UNSIGNABLE_PATTERNS = [/^proxy-/i, /^sec-/i];\nexport const ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\nexport const ALGORITHM_IDENTIFIER_V4A = \"AWS4-ECDSA-P256-SHA256\";\nexport const EVENT_ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256-PAYLOAD\";\nexport const UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nexport const MAX_CACHE_SIZE = 50;\nexport const KEY_TYPE_IDENTIFIER = \"aws4_request\";\nexport const MAX_PRESIGNED_TTL = 60 * 60 * 24 * 7;\n", "import { toHex } from \"@smithy/util-hex-encoding\";\nimport { toUint8Array } from \"@smithy/util-utf8\";\nimport { KEY_TYPE_IDENTIFIER, MAX_CACHE_SIZE } from \"./constants\";\nconst signingKeyCache = {};\nconst cacheQueue = [];\nexport const createScope = (shortDate, region, service) => `${shortDate}/${region}/${service}/${KEY_TYPE_IDENTIFIER}`;\nexport const getSigningKey = async (sha256Constructor, credentials, shortDate, region, service) => {\n    const credsHash = await hmac(sha256Constructor, credentials.secretAccessKey, credentials.accessKeyId);\n    const cacheKey = `${shortDate}:${region}:${service}:${toHex(credsHash)}:${credentials.sessionToken}`;\n    if (cacheKey in signingKeyCache) {\n        return signingKeyCache[cacheKey];\n    }\n    cacheQueue.push(cacheKey);\n    while (cacheQueue.length > MAX_CACHE_SIZE) {\n        delete signingKeyCache[cacheQueue.shift()];\n    }\n    let key = `AWS4${credentials.secretAccessKey}`;\n    for (const signable of [shortDate, region, service, KEY_TYPE_IDENTIFIER]) {\n        key = await hmac(sha256Constructor, key, signable);\n    }\n    return (signingKeyCache[cacheKey] = key);\n};\nexport const clearCredentialCache = () => {\n    cacheQueue.length = 0;\n    Object.keys(signingKeyCache).forEach((cacheKey) => {\n        delete signingKeyCache[cacheKey];\n    });\n};\nconst hmac = (ctor, secret, data) => {\n    const hash = new ctor(secret);\n    hash.update(toUint8Array(data));\n    return hash.digest();\n};\n", "import { ALWAYS_UNSIGNABLE_HEADERS, PROXY_HEADER_PATTERN, SEC_HEADER_PATTERN } from \"./constants\";\nexport const getCanonicalHeaders = ({ headers }, unsignableHeaders, signableHeaders) => {\n    const canonical = {};\n    for (const headerName of Object.keys(headers).sort()) {\n        if (headers[headerName] == undefined) {\n            continue;\n        }\n        const canonicalHeaderName = headerName.toLowerCase();\n        if (canonicalHeaderName in ALWAYS_UNSIGNABLE_HEADERS ||\n            unsignableHeaders?.has(canonicalHeaderName) ||\n            PROXY_HEADER_PATTERN.test(canonicalHeaderName) ||\n            SEC_HEADER_PATTERN.test(canonicalHeaderName)) {\n            if (!signableHeaders || (signableHeaders && !signableHeaders.has(canonicalHeaderName))) {\n                continue;\n            }\n        }\n        canonical[canonicalHeaderName] = headers[headerName].trim().replace(/\\s+/g, \" \");\n    }\n    return canonical;\n};\n", "export const isArrayBuffer = (arg) => (typeof ArrayBuffer === \"function\" && arg instanceof ArrayBuffer) ||\n    Object.prototype.toString.call(arg) === \"[object ArrayBuffer]\";\n", "import { isArrayBuffer } from \"@smithy/is-array-buffer\";\nimport { toHex } from \"@smithy/util-hex-encoding\";\nimport { toUint8Array } from \"@smithy/util-utf8\";\nimport { SHA256_HEADER, UNSIGNED_PAYLOAD } from \"./constants\";\nexport const getPayloadHash = async ({ headers, body }, hashConstructor) => {\n    for (const headerName of Object.keys(headers)) {\n        if (headerName.toLowerCase() === SHA256_HEADER) {\n            return headers[headerName];\n        }\n    }\n    if (body == undefined) {\n        return \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\";\n    }\n    else if (typeof body === \"string\" || ArrayBuffer.isView(body) || isArrayBuffer(body)) {\n        const hashCtor = new hashConstructor();\n        hashCtor.update(toUint8Array(body));\n        return toHex(await hashCtor.digest());\n    }\n    return UNSIGNED_PAYLOAD;\n};\n", "import { fromHex, toHex } from \"@smithy/util-hex-encoding\";\nimport { fromUtf8 } from \"@smithy/util-utf8\";\nexport class HeaderFormatter {\n    format(headers) {\n        const chunks = [];\n        for (const headerName of Object.keys(headers)) {\n            const bytes = fromUtf8(headerName);\n            chunks.push(Uint8Array.from([bytes.byteLength]), bytes, this.formatHeaderValue(headers[headerName]));\n        }\n        const out = new Uint8Array(chunks.reduce((carry, bytes) => carry + bytes.byteLength, 0));\n        let position = 0;\n        for (const chunk of chunks) {\n            out.set(chunk, position);\n            position += chunk.byteLength;\n        }\n        return out;\n    }\n    formatHeaderValue(header) {\n        switch (header.type) {\n            case \"boolean\":\n                return Uint8Array.from([header.value ? 0 : 1]);\n            case \"byte\":\n                return Uint8Array.from([2, header.value]);\n            case \"short\":\n                const shortView = new DataView(new ArrayBuffer(3));\n                shortView.setUint8(0, 3);\n                shortView.setInt16(1, header.value, false);\n                return new Uint8Array(shortView.buffer);\n            case \"integer\":\n                const intView = new DataView(new ArrayBuffer(5));\n                intView.setUint8(0, 4);\n                intView.setInt32(1, header.value, false);\n                return new Uint8Array(intView.buffer);\n            case \"long\":\n                const longBytes = new Uint8Array(9);\n                longBytes[0] = 5;\n                longBytes.set(header.value.bytes, 1);\n                return longBytes;\n            case \"binary\":\n                const binView = new DataView(new ArrayBuffer(3 + header.value.byteLength));\n                binView.setUint8(0, 6);\n                binView.setUint16(1, header.value.byteLength, false);\n                const binBytes = new Uint8Array(binView.buffer);\n                binBytes.set(header.value, 3);\n                return binBytes;\n            case \"string\":\n                const utf8Bytes = fromUtf8(header.value);\n                const strView = new DataView(new ArrayBuffer(3 + utf8Bytes.byteLength));\n                strView.setUint8(0, 7);\n                strView.setUint16(1, utf8Bytes.byteLength, false);\n                const strBytes = new Uint8Array(strView.buffer);\n                strBytes.set(utf8Bytes, 3);\n                return strBytes;\n            case \"timestamp\":\n                const tsBytes = new Uint8Array(9);\n                tsBytes[0] = 8;\n                tsBytes.set(Int64.fromNumber(header.value.valueOf()).bytes, 1);\n                return tsBytes;\n            case \"uuid\":\n                if (!UUID_PATTERN.test(header.value)) {\n                    throw new Error(`Invalid UUID received: ${header.value}`);\n                }\n                const uuidBytes = new Uint8Array(17);\n                uuidBytes[0] = 9;\n                uuidBytes.set(fromHex(header.value.replace(/\\-/g, \"\")), 1);\n                return uuidBytes;\n        }\n    }\n}\nvar HEADER_VALUE_TYPE;\n(function (HEADER_VALUE_TYPE) {\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"boolTrue\"] = 0] = \"boolTrue\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"boolFalse\"] = 1] = \"boolFalse\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"byte\"] = 2] = \"byte\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"short\"] = 3] = \"short\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"integer\"] = 4] = \"integer\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"long\"] = 5] = \"long\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"byteArray\"] = 6] = \"byteArray\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"string\"] = 7] = \"string\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"timestamp\"] = 8] = \"timestamp\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"uuid\"] = 9] = \"uuid\";\n})(HEADER_VALUE_TYPE || (HEADER_VALUE_TYPE = {}));\nconst UUID_PATTERN = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;\nexport class Int64 {\n    constructor(bytes) {\n        this.bytes = bytes;\n        if (bytes.byteLength !== 8) {\n            throw new Error(\"Int64 buffers must be exactly 8 bytes\");\n        }\n    }\n    static fromNumber(number) {\n        if (number > 9223372036854776000 || number < -9223372036854776000) {\n            throw new Error(`${number} is too large (or, if negative, too small) to represent as an Int64`);\n        }\n        const bytes = new Uint8Array(8);\n        for (let i = 7, remaining = Math.abs(Math.round(number)); i > -1 && remaining > 0; i--, remaining /= 256) {\n            bytes[i] = remaining;\n        }\n        if (number < 0) {\n            negate(bytes);\n        }\n        return new Int64(bytes);\n    }\n    valueOf() {\n        const bytes = this.bytes.slice(0);\n        const negative = bytes[0] & 0b10000000;\n        if (negative) {\n            negate(bytes);\n        }\n        return parseInt(toHex(bytes), 16) * (negative ? -1 : 1);\n    }\n    toString() {\n        return String(this.valueOf());\n    }\n}\nfunction negate(bytes) {\n    for (let i = 0; i < 8; i++) {\n        bytes[i] ^= 0xff;\n    }\n    for (let i = 7; i > -1; i--) {\n        bytes[i]++;\n        if (bytes[i] !== 0)\n            break;\n    }\n}\n", "export const hasHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return true;\n        }\n    }\n    return false;\n};\nexport const getHeaderValue = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return headers[headerName];\n        }\n    }\n    return undefined;\n};\nexport const deleteHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            delete headers[headerName];\n        }\n    }\n};\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nexport const moveHeadersToQuery = (request, options = {}) => {\n    const { headers, query = {} } = HttpRequest.clone(request);\n    for (const name of Object.keys(headers)) {\n        const lname = name.toLowerCase();\n        if ((lname.slice(0, 6) === \"x-amz-\" && !options.unhoistableHeaders?.has(lname)) ||\n            options.hoistableHeaders?.has(lname)) {\n            query[name] = headers[name];\n            delete headers[name];\n        }\n    }\n    return {\n        ...request,\n        headers,\n        query,\n    };\n};\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { GENERATED_HEADERS } from \"./constants\";\nexport const prepareRequest = (request) => {\n    request = HttpRequest.clone(request);\n    for (const headerName of Object.keys(request.headers)) {\n        if (GENERATED_HEADERS.indexOf(headerName.toLowerCase()) > -1) {\n            delete request.headers[headerName];\n        }\n    }\n    return request;\n};\n", "import { escapeUri } from \"@smithy/util-uri-escape\";\nimport { SIGNATURE_HEADER } from \"./constants\";\nexport const getCanonicalQuery = ({ query = {} }) => {\n    const keys = [];\n    const serialized = {};\n    for (const key of Object.keys(query)) {\n        if (key.toLowerCase() === SIGNATURE_HEADER) {\n            continue;\n        }\n        const encodedKey = escapeUri(key);\n        keys.push(encodedKey);\n        const value = query[key];\n        if (typeof value === \"string\") {\n            serialized[encodedKey] = `${encodedKey}=${escapeUri(value)}`;\n        }\n        else if (Array.isArray(value)) {\n            serialized[encodedKey] = value\n                .slice(0)\n                .reduce((encoded, value) => encoded.concat([`${encodedKey}=${escapeUri(value)}`]), [])\n                .sort()\n                .join(\"&\");\n        }\n    }\n    return keys\n        .sort()\n        .map((key) => serialized[key])\n        .filter((serialized) => serialized)\n        .join(\"&\");\n};\n", "export const iso8601 = (time) => toDate(time)\n    .toISOString()\n    .replace(/\\.\\d{3}Z$/, \"Z\");\nexport const toDate = (time) => {\n    if (typeof time === \"number\") {\n        return new Date(time * 1000);\n    }\n    if (typeof time === \"string\") {\n        if (Number(time)) {\n            return new Date(Number(time) * 1000);\n        }\n        return new Date(time);\n    }\n    return time;\n};\n", "import { toHex } from \"@smithy/util-hex-encoding\";\nimport { normalizeProvider } from \"@smithy/util-middleware\";\nimport { escapeUri } from \"@smithy/util-uri-escape\";\nimport { toUint8Array } from \"@smithy/util-utf8\";\nimport { getCanonicalQuery } from \"./getCanonicalQuery\";\nimport { iso8601 } from \"./utilDate\";\nexport class SignatureV4Base {\n    constructor({ applyChecksum, credentials, region, service, sha256, uriEscapePath = true, }) {\n        this.service = service;\n        this.sha256 = sha256;\n        this.uriEscapePath = uriEscapePath;\n        this.applyChecksum = typeof applyChecksum === \"boolean\" ? applyChecksum : true;\n        this.regionProvider = normalizeProvider(region);\n        this.credentialProvider = normalizeProvider(credentials);\n    }\n    createCanonicalRequest(request, canonicalHeaders, payloadHash) {\n        const sortedHeaders = Object.keys(canonicalHeaders).sort();\n        return `${request.method}\n${this.getCanonicalPath(request)}\n${getCanonicalQuery(request)}\n${sortedHeaders.map((name) => `${name}:${canonicalHeaders[name]}`).join(\"\\n\")}\n\n${sortedHeaders.join(\";\")}\n${payloadHash}`;\n    }\n    async createStringToSign(longDate, credentialScope, canonicalRequest, algorithmIdentifier) {\n        const hash = new this.sha256();\n        hash.update(toUint8Array(canonicalRequest));\n        const hashedRequest = await hash.digest();\n        return `${algorithmIdentifier}\n${longDate}\n${credentialScope}\n${toHex(hashedRequest)}`;\n    }\n    getCanonicalPath({ path }) {\n        if (this.uriEscapePath) {\n            const normalizedPathSegments = [];\n            for (const pathSegment of path.split(\"/\")) {\n                if (pathSegment?.length === 0)\n                    continue;\n                if (pathSegment === \".\")\n                    continue;\n                if (pathSegment === \"..\") {\n                    normalizedPathSegments.pop();\n                }\n                else {\n                    normalizedPathSegments.push(pathSegment);\n                }\n            }\n            const normalizedPath = `${path?.startsWith(\"/\") ? \"/\" : \"\"}${normalizedPathSegments.join(\"/\")}${normalizedPathSegments.length > 0 && path?.endsWith(\"/\") ? \"/\" : \"\"}`;\n            const doubleEncoded = escapeUri(normalizedPath);\n            return doubleEncoded.replace(/%2F/g, \"/\");\n        }\n        return path;\n    }\n    validateResolvedCredentials(credentials) {\n        if (typeof credentials !== \"object\" ||\n            typeof credentials.accessKeyId !== \"string\" ||\n            typeof credentials.secretAccessKey !== \"string\") {\n            throw new Error(\"Resolved credential object is not valid\");\n        }\n    }\n    formatDate(now) {\n        const longDate = iso8601(now).replace(/[\\-:]/g, \"\");\n        return {\n            longDate,\n            shortDate: longDate.slice(0, 8),\n        };\n    }\n    getCanonicalHeaderList(headers) {\n        return Object.keys(headers).sort().join(\";\");\n    }\n}\n", "import { toHex } from \"@smithy/util-hex-encoding\";\nimport { toUint8Array } from \"@smithy/util-utf8\";\nimport { ALGORITHM_IDENTIFIER, ALGORITHM_QUERY_PARAM, AMZ_DATE_HEADER, AMZ_DATE_QUERY_PARAM, AUTH_HEADER, CREDENTIAL_QUERY_PARAM, EVENT_ALGORITHM_IDENTIFIER, EXPIRES_QUERY_PARAM, MAX_PRESIGNED_TTL, SHA256_HEADER, SIGNATURE_QUERY_PARAM, SIGNED_HEADERS_QUERY_PARAM, TOKEN_HEADER, TOKEN_QUERY_PARAM, } from \"./constants\";\nimport { createScope, getSigningKey } from \"./credentialDerivation\";\nimport { getCanonicalHeaders } from \"./getCanonicalHeaders\";\nimport { getPayloadHash } from \"./getPayloadHash\";\nimport { HeaderFormatter } from \"./HeaderFormatter\";\nimport { hasHeader } from \"./headerUtil\";\nimport { moveHeadersToQuery } from \"./moveHeadersToQuery\";\nimport { prepareRequest } from \"./prepareRequest\";\nimport { SignatureV4Base } from \"./SignatureV4Base\";\nexport class SignatureV4 extends SignatureV4Base {\n    constructor({ applyChecksum, credentials, region, service, sha256, uriEscapePath = true, }) {\n        super({\n            applyChecksum,\n            credentials,\n            region,\n            service,\n            sha256,\n            uriEscapePath,\n        });\n        this.headerFormatter = new HeaderFormatter();\n    }\n    async presign(originalRequest, options = {}) {\n        const { signingDate = new Date(), expiresIn = 3600, unsignableHeaders, unhoistableHeaders, signableHeaders, hoistableHeaders, signingRegion, signingService, } = options;\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { longDate, shortDate } = this.formatDate(signingDate);\n        if (expiresIn > MAX_PRESIGNED_TTL) {\n            return Promise.reject(\"Signature version 4 presigned URLs\" + \" must have an expiration date less than one week in\" + \" the future\");\n        }\n        const scope = createScope(shortDate, region, signingService ?? this.service);\n        const request = moveHeadersToQuery(prepareRequest(originalRequest), { unhoistableHeaders, hoistableHeaders });\n        if (credentials.sessionToken) {\n            request.query[TOKEN_QUERY_PARAM] = credentials.sessionToken;\n        }\n        request.query[ALGORITHM_QUERY_PARAM] = ALGORITHM_IDENTIFIER;\n        request.query[CREDENTIAL_QUERY_PARAM] = `${credentials.accessKeyId}/${scope}`;\n        request.query[AMZ_DATE_QUERY_PARAM] = longDate;\n        request.query[EXPIRES_QUERY_PARAM] = expiresIn.toString(10);\n        const canonicalHeaders = getCanonicalHeaders(request, unsignableHeaders, signableHeaders);\n        request.query[SIGNED_HEADERS_QUERY_PARAM] = this.getCanonicalHeaderList(canonicalHeaders);\n        request.query[SIGNATURE_QUERY_PARAM] = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, await getPayloadHash(originalRequest, this.sha256)));\n        return request;\n    }\n    async sign(toSign, options) {\n        if (typeof toSign === \"string\") {\n            return this.signString(toSign, options);\n        }\n        else if (toSign.headers && toSign.payload) {\n            return this.signEvent(toSign, options);\n        }\n        else if (toSign.message) {\n            return this.signMessage(toSign, options);\n        }\n        else {\n            return this.signRequest(toSign, options);\n        }\n    }\n    async signEvent({ headers, payload }, { signingDate = new Date(), priorSignature, signingRegion, signingService }) {\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate, longDate } = this.formatDate(signingDate);\n        const scope = createScope(shortDate, region, signingService ?? this.service);\n        const hashedPayload = await getPayloadHash({ headers: {}, body: payload }, this.sha256);\n        const hash = new this.sha256();\n        hash.update(headers);\n        const hashedHeaders = toHex(await hash.digest());\n        const stringToSign = [\n            EVENT_ALGORITHM_IDENTIFIER,\n            longDate,\n            scope,\n            priorSignature,\n            hashedHeaders,\n            hashedPayload,\n        ].join(\"\\n\");\n        return this.signString(stringToSign, { signingDate, signingRegion: region, signingService });\n    }\n    async signMessage(signableMessage, { signingDate = new Date(), signingRegion, signingService }) {\n        const promise = this.signEvent({\n            headers: this.headerFormatter.format(signableMessage.message.headers),\n            payload: signableMessage.message.body,\n        }, {\n            signingDate,\n            signingRegion,\n            signingService,\n            priorSignature: signableMessage.priorSignature,\n        });\n        return promise.then((signature) => {\n            return { message: signableMessage.message, signature };\n        });\n    }\n    async signString(stringToSign, { signingDate = new Date(), signingRegion, signingService } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate } = this.formatDate(signingDate);\n        const hash = new this.sha256(await this.getSigningKey(credentials, region, shortDate, signingService));\n        hash.update(toUint8Array(stringToSign));\n        return toHex(await hash.digest());\n    }\n    async signRequest(requestToSign, { signingDate = new Date(), signableHeaders, unsignableHeaders, signingRegion, signingService, } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const request = prepareRequest(requestToSign);\n        const { longDate, shortDate } = this.formatDate(signingDate);\n        const scope = createScope(shortDate, region, signingService ?? this.service);\n        request.headers[AMZ_DATE_HEADER] = longDate;\n        if (credentials.sessionToken) {\n            request.headers[TOKEN_HEADER] = credentials.sessionToken;\n        }\n        const payloadHash = await getPayloadHash(request, this.sha256);\n        if (!hasHeader(SHA256_HEADER, request.headers) && this.applyChecksum) {\n            request.headers[SHA256_HEADER] = payloadHash;\n        }\n        const canonicalHeaders = getCanonicalHeaders(request, unsignableHeaders, signableHeaders);\n        const signature = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, payloadHash));\n        request.headers[AUTH_HEADER] =\n            `${ALGORITHM_IDENTIFIER} ` +\n                `Credential=${credentials.accessKeyId}/${scope}, ` +\n                `SignedHeaders=${this.getCanonicalHeaderList(canonicalHeaders)}, ` +\n                `Signature=${signature}`;\n        return request;\n    }\n    async getSignature(longDate, credentialScope, keyPromise, canonicalRequest) {\n        const stringToSign = await this.createStringToSign(longDate, credentialScope, canonicalRequest, ALGORITHM_IDENTIFIER);\n        const hash = new this.sha256(await keyPromise);\n        hash.update(toUint8Array(stringToSign));\n        return toHex(await hash.digest());\n    }\n    getSigningKey(credentials, region, shortDate, service) {\n        return getSigningKey(this.sha256, credentials, shortDate, region, service || this.service);\n    }\n}\n", "export const signatureV4aContainer = {\n    SignatureV4a: null,\n};\n", "export * from \"./SignatureV4\";\nexport * from \"./constants\";\nexport { getCanonicalHeaders } from \"./getCanonicalHeaders\";\nexport { getCanonicalQuery } from \"./getCanonicalQuery\";\nexport { getPayloadHash } from \"./getPayloadHash\";\nexport { moveHeadersToQuery } from \"./moveHeadersToQuery\";\nexport { prepareRequest } from \"./prepareRequest\";\nexport * from \"./credentialDerivation\";\nexport { SignatureV4Base } from \"./SignatureV4Base\";\nexport { hasHeader } from \"./headerUtil\";\nexport * from \"./signature-v4a-container\";\n", "import { setCredentialFeature } from \"@aws-sdk/core/client\";\nimport { doesIdentityRequireRefresh, isIdentityExpired, memoizeIdentityProvider, normalizeProvider, } from \"@smithy/core\";\nimport { SignatureV4 } from \"@smithy/signature-v4\";\nexport const resolveAwsSdkSigV4Config = (config) => {\n    let inputCredentials = config.credentials;\n    let isUserSupplied = !!config.credentials;\n    let resolvedCredentials = undefined;\n    Object.defineProperty(config, \"credentials\", {\n        set(credentials) {\n            if (credentials && credentials !== inputCredentials && credentials !== resolvedCredentials) {\n                isUserSupplied = true;\n            }\n            inputCredentials = credentials;\n            const memoizedProvider = normalizeCredentialProvider(config, {\n                credentials: inputCredentials,\n                credentialDefaultProvider: config.credentialDefaultProvider,\n            });\n            const boundProvider = bindCallerConfig(config, memoizedProvider);\n            if (isUserSupplied && !boundProvider.attributed) {\n                resolvedCredentials = async (options) => boundProvider(options).then((creds) => setCredentialFeature(creds, \"CREDENTIALS_CODE\", \"e\"));\n                resolvedCredentials.memoized = boundProvider.memoized;\n                resolvedCredentials.configBound = boundProvider.configBound;\n                resolvedCredentials.attributed = true;\n            }\n            else {\n                resolvedCredentials = boundProvider;\n            }\n        },\n        get() {\n            return resolvedCredentials;\n        },\n        enumerable: true,\n        configurable: true,\n    });\n    config.credentials = inputCredentials;\n    const { signingEscapePath = true, systemClockOffset = config.systemClockOffset || 0, sha256, } = config;\n    let signer;\n    if (config.signer) {\n        signer = normalizeProvider(config.signer);\n    }\n    else if (config.regionInfoProvider) {\n        signer = () => normalizeProvider(config.region)()\n            .then(async (region) => [\n            (await config.regionInfoProvider(region, {\n                useFipsEndpoint: await config.useFipsEndpoint(),\n                useDualstackEndpoint: await config.useDualstackEndpoint(),\n            })) || {},\n            region,\n        ])\n            .then(([regionInfo, region]) => {\n            const { signingRegion, signingService } = regionInfo;\n            config.signingRegion = config.signingRegion || signingRegion || region;\n            config.signingName = config.signingName || signingService || config.serviceId;\n            const params = {\n                ...config,\n                credentials: config.credentials,\n                region: config.signingRegion,\n                service: config.signingName,\n                sha256,\n                uriEscapePath: signingEscapePath,\n            };\n            const SignerCtor = config.signerConstructor || SignatureV4;\n            return new SignerCtor(params);\n        });\n    }\n    else {\n        signer = async (authScheme) => {\n            authScheme = Object.assign({}, {\n                name: \"sigv4\",\n                signingName: config.signingName || config.defaultSigningName,\n                signingRegion: await normalizeProvider(config.region)(),\n                properties: {},\n            }, authScheme);\n            const signingRegion = authScheme.signingRegion;\n            const signingService = authScheme.signingName;\n            config.signingRegion = config.signingRegion || signingRegion;\n            config.signingName = config.signingName || signingService || config.serviceId;\n            const params = {\n                ...config,\n                credentials: config.credentials,\n                region: config.signingRegion,\n                service: config.signingName,\n                sha256,\n                uriEscapePath: signingEscapePath,\n            };\n            const SignerCtor = config.signerConstructor || SignatureV4;\n            return new SignerCtor(params);\n        };\n    }\n    const resolvedConfig = Object.assign(config, {\n        systemClockOffset,\n        signingEscapePath,\n        signer,\n    });\n    return resolvedConfig;\n};\nexport const resolveAWSSDKSigV4Config = resolveAwsSdkSigV4Config;\nfunction normalizeCredentialProvider(config, { credentials, credentialDefaultProvider, }) {\n    let credentialsProvider;\n    if (credentials) {\n        if (!credentials?.memoized) {\n            credentialsProvider = memoizeIdentityProvider(credentials, isIdentityExpired, doesIdentityRequireRefresh);\n        }\n        else {\n            credentialsProvider = credentials;\n        }\n    }\n    else {\n        if (credentialDefaultProvider) {\n            credentialsProvider = normalizeProvider(credentialDefaultProvider(Object.assign({}, config, {\n                parentClientConfig: config,\n            })));\n        }\n        else {\n            credentialsProvider = async () => {\n                throw new Error(\"@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.\");\n            };\n        }\n    }\n    credentialsProvider.memoized = true;\n    return credentialsProvider;\n}\nfunction bindCallerConfig(config, credentialsProvider) {\n    if (credentialsProvider.configBound) {\n        return credentialsProvider;\n    }\n    const fn = async (options) => credentialsProvider({ ...options, callerClientConfig: config });\n    fn.memoized = credentialsProvider.memoized;\n    fn.configBound = true;\n    return fn;\n}\n", "export { AwsSdkSigV4Signer, AWSSDKSigV4Signer, validateSigningProperties } from \"./AwsSdkSigV4Signer\";\nexport { AwsSdkSigV4ASigner } from \"./AwsSdkSigV4ASigner\";\nexport * from \"./NODE_AUTH_SCHEME_PREFERENCE_OPTIONS\";\nexport * from \"./resolveAwsSdkSigV4AConfig\";\nexport * from \"./resolveAwsSdkSigV4Config\";\n", "export * from \"./aws_sdk\";\n", "export const _toStr = (val) => {\n    if (val == null) {\n        return val;\n    }\n    if (typeof val === \"number\" || typeof val === \"bigint\") {\n        const warning = new Error(`Received number ${val} where a string was expected.`);\n        warning.name = \"Warning\";\n        console.warn(warning);\n        return String(val);\n    }\n    if (typeof val === \"boolean\") {\n        const warning = new Error(`Received boolean ${val} where a string was expected.`);\n        warning.name = \"Warning\";\n        console.warn(warning);\n        return String(val);\n    }\n    return val;\n};\nexport const _toBool = (val) => {\n    if (val == null) {\n        return val;\n    }\n    if (typeof val === \"number\") {\n    }\n    if (typeof val === \"string\") {\n        const lowercase = val.toLowerCase();\n        if (val !== \"\" && lowercase !== \"false\" && lowercase !== \"true\") {\n            const warning = new Error(`Received string \"${val}\" where a boolean was expected.`);\n            warning.name = \"Warning\";\n            console.warn(warning);\n        }\n        return val !== \"\" && lowercase !== \"false\";\n    }\n    return val;\n};\nexport const _toNum = (val) => {\n    if (val == null) {\n        return val;\n    }\n    if (typeof val === \"boolean\") {\n    }\n    if (typeof val === \"string\") {\n        const num = Number(val);\n        if (num.toString() !== val) {\n            const warning = new Error(`Received string \"${val}\" where a number was expected.`);\n            warning.name = \"Warning\";\n            console.warn(warning);\n            return val;\n        }\n        return num;\n    }\n    return val;\n};\n", "import { expectUnion } from \"@smithy/smithy-client\";\nexport const awsExpectUnion = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (typeof value === \"object\" && \"__type\" in value) {\n        delete value.__type;\n    }\n    return expectUnion(value);\n};\n", "import { collectBody } from \"@smithy/smithy-client\";\nexport const collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));\n", "import { collectBodyString } from \"../common\";\nexport const parseJsonBody = (streamBody, context) => collectBodyString(streamBody, context).then((encoded) => {\n    if (encoded.length) {\n        try {\n            return JSON.parse(encoded);\n        }\n        catch (e) {\n            if (e?.name === \"SyntaxError\") {\n                Object.defineProperty(e, \"$responseBodyText\", {\n                    value: encoded,\n                });\n            }\n            throw e;\n        }\n    }\n    return {};\n});\nexport const parseJsonErrorBody = async (errorBody, context) => {\n    const value = await parseJsonBody(errorBody, context);\n    value.message = value.message ?? value.Message;\n    return value;\n};\nexport const loadRestJsonErrorCode = (output, data) => {\n    const findKey = (object, key) => Object.keys(object).find((k) => k.toLowerCase() === key.toLowerCase());\n    const sanitizeErrorCode = (rawValue) => {\n        let cleanValue = rawValue;\n        if (typeof cleanValue === \"number\") {\n            cleanValue = cleanValue.toString();\n        }\n        if (cleanValue.indexOf(\",\") >= 0) {\n            cleanValue = cleanValue.split(\",\")[0];\n        }\n        if (cleanValue.indexOf(\":\") >= 0) {\n            cleanValue = cleanValue.split(\":\")[0];\n        }\n        if (cleanValue.indexOf(\"#\") >= 0) {\n            cleanValue = cleanValue.split(\"#\")[1];\n        }\n        return cleanValue;\n    };\n    const headerKey = findKey(output.headers, \"x-amzn-errortype\");\n    if (headerKey !== undefined) {\n        return sanitizeErrorCode(output.headers[headerKey]);\n    }\n    if (data.code !== undefined) {\n        return sanitizeErrorCode(data.code);\n    }\n    if (data[\"__type\"] !== undefined) {\n        return sanitizeErrorCode(data[\"__type\"]);\n    }\n};\n", "'use strict';\n\nconst nameStartChar = ':A-Za-z_\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD';\nconst nameChar = nameStartChar + '\\\\-.\\\\d\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040';\nconst nameRegexp = '[' + nameStartChar + '][' + nameChar + ']*'\nconst regexName = new RegExp('^' + nameRegexp + '$');\n\nconst getAllMatches = function(string, regex) {\n  const matches = [];\n  let match = regex.exec(string);\n  while (match) {\n    const allmatches = [];\n    allmatches.startIndex = regex.lastIndex - match[0].length;\n    const len = match.length;\n    for (let index = 0; index < len; index++) {\n      allmatches.push(match[index]);\n    }\n    matches.push(allmatches);\n    match = regex.exec(string);\n  }\n  return matches;\n};\n\nconst isName = function(string) {\n  const match = regexName.exec(string);\n  return !(match === null || typeof match === 'undefined');\n};\n\nexports.isExist = function(v) {\n  return typeof v !== 'undefined';\n};\n\nexports.isEmptyObject = function(obj) {\n  return Object.keys(obj).length === 0;\n};\n\n/**\n * Copy all the properties of a into b.\n * @param {*} target\n * @param {*} a\n */\nexports.merge = function(target, a, arrayMode) {\n  if (a) {\n    const keys = Object.keys(a); // will return an array of own properties\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      if (arrayMode === 'strict') {\n        target[keys[i]] = [ a[keys[i]] ];\n      } else {\n        target[keys[i]] = a[keys[i]];\n      }\n    }\n  }\n};\n/* exports.merge =function (b,a){\n  return Object.assign(b,a);\n} */\n\nexports.getValue = function(v) {\n  if (exports.isExist(v)) {\n    return v;\n  } else {\n    return '';\n  }\n};\n\n// const fakeCall = function(a) {return a;};\n// const fakeCallNoReturn = function() {};\n\nexports.isName = isName;\nexports.getAllMatches = getAllMatches;\nexports.nameRegexp = nameRegexp;\n", "'use strict';\n\nconst util = require('./util');\n\nconst defaultOptions = {\n  allowBooleanAttributes: false, //A tag can have attributes without any value\n  unpairedTags: []\n};\n\n//const tagsPattern = new RegExp(\"<\\\\/?([\\\\w:\\\\-_\\.]+)\\\\s*\\/?>\",\"g\");\nexports.validate = function (xmlData, options) {\n  options = Object.assign({}, defaultOptions, options);\n\n  //xmlData = xmlData.replace(/(\\r\\n|\\n|\\r)/gm,\"\");//make it single line\n  //xmlData = xmlData.replace(/(^\\s*<\\?xml.*?\\?>)/g,\"\");//Remove XML starting tag\n  //xmlData = xmlData.replace(/(<!DOCTYPE[\\s\\w\\\"\\.\\/\\-\\:]+(\\[.*\\])*\\s*>)/g,\"\");//Remove DOCTYPE\n  const tags = [];\n  let tagFound = false;\n\n  //indicates that the root tag has been closed (aka. depth 0 has been reached)\n  let reachedRoot = false;\n\n  if (xmlData[0] === '\\ufeff') {\n    // check for byte order mark (BOM)\n    xmlData = xmlData.substr(1);\n  }\n  \n  for (let i = 0; i < xmlData.length; i++) {\n\n    if (xmlData[i] === '<' && xmlData[i+1] === '?') {\n      i+=2;\n      i = readPI(xmlData,i);\n      if (i.err) return i;\n    }else if (xmlData[i] === '<') {\n      //starting of tag\n      //read until you reach to '>' avoiding any '>' in attribute value\n      let tagStartPos = i;\n      i++;\n      \n      if (xmlData[i] === '!') {\n        i = readCommentAndCDATA(xmlData, i);\n        continue;\n      } else {\n        let closingTag = false;\n        if (xmlData[i] === '/') {\n          //closing tag\n          closingTag = true;\n          i++;\n        }\n        //read tagname\n        let tagName = '';\n        for (; i < xmlData.length &&\n          xmlData[i] !== '>' &&\n          xmlData[i] !== ' ' &&\n          xmlData[i] !== '\\t' &&\n          xmlData[i] !== '\\n' &&\n          xmlData[i] !== '\\r'; i++\n        ) {\n          tagName += xmlData[i];\n        }\n        tagName = tagName.trim();\n        //console.log(tagName);\n\n        if (tagName[tagName.length - 1] === '/') {\n          //self closing tag without attributes\n          tagName = tagName.substring(0, tagName.length - 1);\n          //continue;\n          i--;\n        }\n        if (!validateTagName(tagName)) {\n          let msg;\n          if (tagName.trim().length === 0) {\n            msg = \"Invalid space after '<'.\";\n          } else {\n            msg = \"Tag '\"+tagName+\"' is an invalid name.\";\n          }\n          return getErrorObject('InvalidTag', msg, getLineNumberForPosition(xmlData, i));\n        }\n\n        const result = readAttributeStr(xmlData, i);\n        if (result === false) {\n          return getErrorObject('InvalidAttr', \"Attributes for '\"+tagName+\"' have open quote.\", getLineNumberForPosition(xmlData, i));\n        }\n        let attrStr = result.value;\n        i = result.index;\n\n        if (attrStr[attrStr.length - 1] === '/') {\n          //self closing tag\n          const attrStrStart = i - attrStr.length;\n          attrStr = attrStr.substring(0, attrStr.length - 1);\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid === true) {\n            tagFound = true;\n            //continue; //text may presents after self closing tag\n          } else {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, attrStrStart + isValid.err.line));\n          }\n        } else if (closingTag) {\n          if (!result.tagClosed) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' doesn't have proper closing.\", getLineNumberForPosition(xmlData, i));\n          } else if (attrStr.trim().length > 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' can't have attributes or invalid starting.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else if (tags.length === 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' has not been opened.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else {\n            const otg = tags.pop();\n            if (tagName !== otg.tagName) {\n              let openPos = getLineNumberForPosition(xmlData, otg.tagStartPos);\n              return getErrorObject('InvalidTag',\n                \"Expected closing tag '\"+otg.tagName+\"' (opened in line \"+openPos.line+\", col \"+openPos.col+\") instead of closing tag '\"+tagName+\"'.\",\n                getLineNumberForPosition(xmlData, tagStartPos));\n            }\n\n            //when there are no more tags, we reached the root level.\n            if (tags.length == 0) {\n              reachedRoot = true;\n            }\n          }\n        } else {\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid !== true) {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, i - attrStr.length + isValid.err.line));\n          }\n\n          //if the root level has been reached before ...\n          if (reachedRoot === true) {\n            return getErrorObject('InvalidXml', 'Multiple possible root nodes found.', getLineNumberForPosition(xmlData, i));\n          } else if(options.unpairedTags.indexOf(tagName) !== -1){\n            //don't push into stack\n          } else {\n            tags.push({tagName, tagStartPos});\n          }\n          tagFound = true;\n        }\n\n        //skip tag text value\n        //It may include comments and CDATA value\n        for (i++; i < xmlData.length; i++) {\n          if (xmlData[i] === '<') {\n            if (xmlData[i + 1] === '!') {\n              //comment or CADATA\n              i++;\n              i = readCommentAndCDATA(xmlData, i);\n              continue;\n            } else if (xmlData[i+1] === '?') {\n              i = readPI(xmlData, ++i);\n              if (i.err) return i;\n            } else{\n              break;\n            }\n          } else if (xmlData[i] === '&') {\n            const afterAmp = validateAmpersand(xmlData, i);\n            if (afterAmp == -1)\n              return getErrorObject('InvalidChar', \"char '&' is not expected.\", getLineNumberForPosition(xmlData, i));\n            i = afterAmp;\n          }else{\n            if (reachedRoot === true && !isWhiteSpace(xmlData[i])) {\n              return getErrorObject('InvalidXml', \"Extra text at the end\", getLineNumberForPosition(xmlData, i));\n            }\n          }\n        } //end of reading tag text value\n        if (xmlData[i] === '<') {\n          i--;\n        }\n      }\n    } else {\n      if ( isWhiteSpace(xmlData[i])) {\n        continue;\n      }\n      return getErrorObject('InvalidChar', \"char '\"+xmlData[i]+\"' is not expected.\", getLineNumberForPosition(xmlData, i));\n    }\n  }\n\n  if (!tagFound) {\n    return getErrorObject('InvalidXml', 'Start tag expected.', 1);\n  }else if (tags.length == 1) {\n      return getErrorObject('InvalidTag', \"Unclosed tag '\"+tags[0].tagName+\"'.\", getLineNumberForPosition(xmlData, tags[0].tagStartPos));\n  }else if (tags.length > 0) {\n      return getErrorObject('InvalidXml', \"Invalid '\"+\n          JSON.stringify(tags.map(t => t.tagName), null, 4).replace(/\\r?\\n/g, '')+\n          \"' found.\", {line: 1, col: 1});\n  }\n\n  return true;\n};\n\nfunction isWhiteSpace(char){\n  return char === ' ' || char === '\\t' || char === '\\n'  || char === '\\r';\n}\n/**\n * Read Processing insstructions and skip\n * @param {*} xmlData\n * @param {*} i\n */\nfunction readPI(xmlData, i) {\n  const start = i;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] == '?' || xmlData[i] == ' ') {\n      //tagname\n      const tagname = xmlData.substr(start, i - start);\n      if (i > 5 && tagname === 'xml') {\n        return getErrorObject('InvalidXml', 'XML declaration allowed only at the start of the document.', getLineNumberForPosition(xmlData, i));\n      } else if (xmlData[i] == '?' && xmlData[i + 1] == '>') {\n        //check if valid attribut string\n        i++;\n        break;\n      } else {\n        continue;\n      }\n    }\n  }\n  return i;\n}\n\nfunction readCommentAndCDATA(xmlData, i) {\n  if (xmlData.length > i + 5 && xmlData[i + 1] === '-' && xmlData[i + 2] === '-') {\n    //comment\n    for (i += 3; i < xmlData.length; i++) {\n      if (xmlData[i] === '-' && xmlData[i + 1] === '-' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  } else if (\n    xmlData.length > i + 8 &&\n    xmlData[i + 1] === 'D' &&\n    xmlData[i + 2] === 'O' &&\n    xmlData[i + 3] === 'C' &&\n    xmlData[i + 4] === 'T' &&\n    xmlData[i + 5] === 'Y' &&\n    xmlData[i + 6] === 'P' &&\n    xmlData[i + 7] === 'E'\n  ) {\n    let angleBracketsCount = 1;\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === '<') {\n        angleBracketsCount++;\n      } else if (xmlData[i] === '>') {\n        angleBracketsCount--;\n        if (angleBracketsCount === 0) {\n          break;\n        }\n      }\n    }\n  } else if (\n    xmlData.length > i + 9 &&\n    xmlData[i + 1] === '[' &&\n    xmlData[i + 2] === 'C' &&\n    xmlData[i + 3] === 'D' &&\n    xmlData[i + 4] === 'A' &&\n    xmlData[i + 5] === 'T' &&\n    xmlData[i + 6] === 'A' &&\n    xmlData[i + 7] === '['\n  ) {\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === ']' && xmlData[i + 1] === ']' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  }\n\n  return i;\n}\n\nconst doubleQuote = '\"';\nconst singleQuote = \"'\";\n\n/**\n * Keep reading xmlData until '<' is found outside the attribute value.\n * @param {string} xmlData\n * @param {number} i\n */\nfunction readAttributeStr(xmlData, i) {\n  let attrStr = '';\n  let startChar = '';\n  let tagClosed = false;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === doubleQuote || xmlData[i] === singleQuote) {\n      if (startChar === '') {\n        startChar = xmlData[i];\n      } else if (startChar !== xmlData[i]) {\n        //if vaue is enclosed with double quote then single quotes are allowed inside the value and vice versa\n      } else {\n        startChar = '';\n      }\n    } else if (xmlData[i] === '>') {\n      if (startChar === '') {\n        tagClosed = true;\n        break;\n      }\n    }\n    attrStr += xmlData[i];\n  }\n  if (startChar !== '') {\n    return false;\n  }\n\n  return {\n    value: attrStr,\n    index: i,\n    tagClosed: tagClosed\n  };\n}\n\n/**\n * Select all the attributes whether valid or invalid.\n */\nconst validAttrStrRegxp = new RegExp('(\\\\s*)([^\\\\s=]+)(\\\\s*=)?(\\\\s*([\\'\"])(([\\\\s\\\\S])*?)\\\\5)?', 'g');\n\n//attr, =\"sd\", a=\"amit's\", a=\"sd\"b=\"saf\", ab  cd=\"\"\n\nfunction validateAttributeString(attrStr, options) {\n  //console.log(\"start:\"+attrStr+\":end\");\n\n  //if(attrStr.trim().length === 0) return true; //empty string\n\n  const matches = util.getAllMatches(attrStr, validAttrStrRegxp);\n  const attrNames = {};\n\n  for (let i = 0; i < matches.length; i++) {\n    if (matches[i][1].length === 0) {\n      //nospace before attribute name: a=\"sd\"b=\"saf\"\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' has no space in starting.\", getPositionFromMatch(matches[i]))\n    } else if (matches[i][3] !== undefined && matches[i][4] === undefined) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' is without value.\", getPositionFromMatch(matches[i]));\n    } else if (matches[i][3] === undefined && !options.allowBooleanAttributes) {\n      //independent attribute: ab\n      return getErrorObject('InvalidAttr', \"boolean attribute '\"+matches[i][2]+\"' is not allowed.\", getPositionFromMatch(matches[i]));\n    }\n    /* else if(matches[i][6] === undefined){//attribute without value: ab=\n                    return { err: { code:\"InvalidAttr\",msg:\"attribute \" + matches[i][2] + \" has no value assigned.\"}};\n                } */\n    const attrName = matches[i][2];\n    if (!validateAttrName(attrName)) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is an invalid name.\", getPositionFromMatch(matches[i]));\n    }\n    if (!attrNames.hasOwnProperty(attrName)) {\n      //check for duplicate attribute.\n      attrNames[attrName] = 1;\n    } else {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is repeated.\", getPositionFromMatch(matches[i]));\n    }\n  }\n\n  return true;\n}\n\nfunction validateNumberAmpersand(xmlData, i) {\n  let re = /\\d/;\n  if (xmlData[i] === 'x') {\n    i++;\n    re = /[\\da-fA-F]/;\n  }\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === ';')\n      return i;\n    if (!xmlData[i].match(re))\n      break;\n  }\n  return -1;\n}\n\nfunction validateAmpersand(xmlData, i) {\n  // https://www.w3.org/TR/xml/#dt-charref\n  i++;\n  if (xmlData[i] === ';')\n    return -1;\n  if (xmlData[i] === '#') {\n    i++;\n    return validateNumberAmpersand(xmlData, i);\n  }\n  let count = 0;\n  for (; i < xmlData.length; i++, count++) {\n    if (xmlData[i].match(/\\w/) && count < 20)\n      continue;\n    if (xmlData[i] === ';')\n      break;\n    return -1;\n  }\n  return i;\n}\n\nfunction getErrorObject(code, message, lineNumber) {\n  return {\n    err: {\n      code: code,\n      msg: message,\n      line: lineNumber.line || lineNumber,\n      col: lineNumber.col,\n    },\n  };\n}\n\nfunction validateAttrName(attrName) {\n  return util.isName(attrName);\n}\n\n// const startsWithXML = /^xml/i;\n\nfunction validateTagName(tagname) {\n  return util.isName(tagname) /* && !tagname.match(startsWithXML) */;\n}\n\n//this function returns the line number for the character at the given index\nfunction getLineNumberForPosition(xmlData, index) {\n  const lines = xmlData.substring(0, index).split(/\\r?\\n/);\n  return {\n    line: lines.length,\n\n    // column number is last line's length + 1, because column numbering starts at 1:\n    col: lines[lines.length - 1].length + 1\n  };\n}\n\n//this function returns the position of the first character of match within attrStr\nfunction getPositionFromMatch(match) {\n  return match.startIndex + match[1].length;\n}\n", "\nconst defaultOptions = {\n    preserveOrder: false,\n    attributeNamePrefix: '@_',\n    attributesGroupName: false,\n    textNodeName: '#text',\n    ignoreAttributes: true,\n    removeNSPrefix: false, // remove NS from tag name or attribute name if true\n    allowBooleanAttributes: false, //a tag can have attributes without any value\n    //ignoreRootElement : false,\n    parseTagValue: true,\n    parseAttributeValue: false,\n    trimValues: true, //Trim string values of tag and attributes\n    cdataPropName: false,\n    numberParseOptions: {\n      hex: true,\n      leadingZeros: true,\n      eNotation: true\n    },\n    tagValueProcessor: function(tagName, val) {\n      return val;\n    },\n    attributeValueProcessor: function(attrName, val) {\n      return val;\n    },\n    stopNodes: [], //nested tags will not be parsed even for errors\n    alwaysCreateTextNode: false,\n    isArray: () => false,\n    commentPropName: false,\n    unpairedTags: [],\n    processEntities: true,\n    htmlEntities: false,\n    ignoreDeclaration: false,\n    ignorePiTags: false,\n    transformTagName: false,\n    transformAttributeName: false,\n    updateTag: function(tagName, jPath, attrs){\n      return tagName\n    },\n    // skipEmptyListItem: false\n};\n   \nconst buildOptions = function(options) {\n    return Object.assign({}, defaultOptions, options);\n};\n\nexports.buildOptions = buildOptions;\nexports.defaultOptions = defaultOptions;", "'use strict';\n\nclass XmlNode{\n  constructor(tagname) {\n    this.tagname = tagname;\n    this.child = []; //nested tags, text, cdata, comments in order\n    this[\":@\"] = {}; //attributes map\n  }\n  add(key,val){\n    // this.child.push( {name : key, val: val, isCdata: isCdata });\n    if(key === \"__proto__\") key = \"#__proto__\";\n    this.child.push( {[key]: val });\n  }\n  addChild(node) {\n    if(node.tagname === \"__proto__\") node.tagname = \"#__proto__\";\n    if(node[\":@\"] && Object.keys(node[\":@\"]).length > 0){\n      this.child.push( { [node.tagname]: node.child, [\":@\"]: node[\":@\"] });\n    }else{\n      this.child.push( { [node.tagname]: node.child });\n    }\n  };\n};\n\n\nmodule.exports = XmlNode;", "const util = require('../util');\n\n//TODO: handle comments\nfunction readDocType(xmlData, i){\n    \n    const entities = {};\n    if( xmlData[i + 3] === 'O' &&\n         xmlData[i + 4] === 'C' &&\n         xmlData[i + 5] === 'T' &&\n         xmlData[i + 6] === 'Y' &&\n         xmlData[i + 7] === 'P' &&\n         xmlData[i + 8] === 'E')\n    {    \n        i = i+9;\n        let angleBracketsCount = 1;\n        let hasBody = false, comment = false;\n        let exp = \"\";\n        for(;i<xmlData.length;i++){\n            if (xmlData[i] === '<' && !comment) { //Determine the tag type\n                if( hasBody && isEntity(xmlData, i)){\n                    i += 7; \n                    [entityName, val,i] = readEntityExp(xmlData,i+1);\n                    if(val.indexOf(\"&\") === -1) //Parameter entities are not supported\n                        entities[ validateEntityName(entityName) ] = {\n                            regx : RegExp( `&${entityName};`,\"g\"),\n                            val: val\n                        };\n                }\n                else if( hasBody && isElement(xmlData, i))  i += 8;//Not supported\n                else if( hasBody && isAttlist(xmlData, i))  i += 8;//Not supported\n                else if( hasBody && isNotation(xmlData, i)) i += 9;//Not supported\n                else if( isComment)                         comment = true;\n                else                                        throw new Error(\"Invalid DOCTYPE\");\n\n                angleBracketsCount++;\n                exp = \"\";\n            } else if (xmlData[i] === '>') { //Read tag content\n                if(comment){\n                    if( xmlData[i - 1] === \"-\" && xmlData[i - 2] === \"-\"){\n                        comment = false;\n                        angleBracketsCount--;\n                    }\n                }else{\n                    angleBracketsCount--;\n                }\n                if (angleBracketsCount === 0) {\n                  break;\n                }\n            }else if( xmlData[i] === '['){\n                hasBody = true;\n            }else{\n                exp += xmlData[i];\n            }\n        }\n        if(angleBracketsCount !== 0){\n            throw new Error(`Unclosed DOCTYPE`);\n        }\n    }else{\n        throw new Error(`Invalid Tag instead of DOCTYPE`);\n    }\n    return {entities, i};\n}\n\nfunction readEntityExp(xmlData,i){\n    //External entities are not supported\n    //    <!ENTITY ext SYSTEM \"http://normal-website.com\" >\n\n    //Parameter entities are not supported\n    //    <!ENTITY entityname \"&anotherElement;\">\n\n    //Internal entities are supported\n    //    <!ENTITY entityname \"replacement text\">\n    \n    //read EntityName\n    let entityName = \"\";\n    for (; i < xmlData.length && (xmlData[i] !== \"'\" && xmlData[i] !== '\"' ); i++) {\n        // if(xmlData[i] === \" \") continue;\n        // else \n        entityName += xmlData[i];\n    }\n    entityName = entityName.trim();\n    if(entityName.indexOf(\" \") !== -1) throw new Error(\"External entites are not supported\");\n\n    //read Entity Value\n    const startChar = xmlData[i++];\n    let val = \"\"\n    for (; i < xmlData.length && xmlData[i] !== startChar ; i++) {\n        val += xmlData[i];\n    }\n    return [entityName, val, i];\n}\n\nfunction isComment(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === '-' &&\n    xmlData[i+3] === '-') return true\n    return false\n}\nfunction isEntity(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'E' &&\n    xmlData[i+3] === 'N' &&\n    xmlData[i+4] === 'T' &&\n    xmlData[i+5] === 'I' &&\n    xmlData[i+6] === 'T' &&\n    xmlData[i+7] === 'Y') return true\n    return false\n}\nfunction isElement(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'E' &&\n    xmlData[i+3] === 'L' &&\n    xmlData[i+4] === 'E' &&\n    xmlData[i+5] === 'M' &&\n    xmlData[i+6] === 'E' &&\n    xmlData[i+7] === 'N' &&\n    xmlData[i+8] === 'T') return true\n    return false\n}\n\nfunction isAttlist(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'A' &&\n    xmlData[i+3] === 'T' &&\n    xmlData[i+4] === 'T' &&\n    xmlData[i+5] === 'L' &&\n    xmlData[i+6] === 'I' &&\n    xmlData[i+7] === 'S' &&\n    xmlData[i+8] === 'T') return true\n    return false\n}\nfunction isNotation(xmlData, i){\n    if(xmlData[i+1] === '!' &&\n    xmlData[i+2] === 'N' &&\n    xmlData[i+3] === 'O' &&\n    xmlData[i+4] === 'T' &&\n    xmlData[i+5] === 'A' &&\n    xmlData[i+6] === 'T' &&\n    xmlData[i+7] === 'I' &&\n    xmlData[i+8] === 'O' &&\n    xmlData[i+9] === 'N') return true\n    return false\n}\n\nfunction validateEntityName(name){\n    if (util.isName(name))\n\treturn name;\n    else\n        throw new Error(`Invalid entity name ${name}`);\n}\n\nmodule.exports = readDocType;\n", "const hexRegex = /^[-+]?0x[a-fA-F0-9]+$/;\nconst numRegex = /^([\\-\\+])?(0*)([0-9]*(\\.[0-9]*)?)$/;\n// const octRegex = /^0x[a-z0-9]+/;\n// const binRegex = /0x[a-z0-9]+/;\n\n \nconst consider = {\n    hex :  true,\n    // oct: false,\n    leadingZeros: true,\n    decimalPoint: \"\\.\",\n    eNotation: true,\n    //skipLike: /regex/\n};\n\nfunction toNumber(str, options = {}){\n    options = Object.assign({}, consider, options );\n    if(!str || typeof str !== \"string\" ) return str;\n    \n    let trimmedStr  = str.trim();\n    \n    if(options.skipLike !== undefined && options.skipLike.test(trimmedStr)) return str;\n    else if(str===\"0\") return 0;\n    else if (options.hex && hexRegex.test(trimmedStr)) {\n        return parse_int(trimmedStr, 16);\n    // }else if (options.oct && octRegex.test(str)) {\n    //     return Number.parseInt(val, 8);\n    }else if (trimmedStr.search(/[eE]/)!== -1) { //eNotation\n        const notation = trimmedStr.match(/^([-\\+])?(0*)([0-9]*(\\.[0-9]*)?[eE][-\\+]?[0-9]+)$/); \n        // +00.123 => [ , '+', '00', '.123', ..\n        if(notation){\n            // console.log(notation)\n            if(options.leadingZeros){ //accept with leading zeros\n                trimmedStr = (notation[1] || \"\") + notation[3];\n            }else{\n                if(notation[2] === \"0\" && notation[3][0]=== \".\"){ //valid number\n                }else{\n                    return str;\n                }\n            }\n            return options.eNotation ? Number(trimmedStr) : str;\n        }else{\n            return str;\n        }\n    // }else if (options.parseBin && binRegex.test(str)) {\n    //     return Number.parseInt(val, 2);\n    }else{\n        //separate negative sign, leading zeros, and rest number\n        const match = numRegex.exec(trimmedStr);\n        // +00.123 => [ , '+', '00', '.123', ..\n        if(match){\n            const sign = match[1];\n            const leadingZeros = match[2];\n            let numTrimmedByZeros = trimZeros(match[3]); //complete num without leading zeros\n            //trim ending zeros for floating number\n            \n            if(!options.leadingZeros && leadingZeros.length > 0 && sign && trimmedStr[2] !== \".\") return str; //-0123\n            else if(!options.leadingZeros && leadingZeros.length > 0 && !sign && trimmedStr[1] !== \".\") return str; //0123\n            else if(options.leadingZeros && leadingZeros===str) return 0; //00\n            \n            else{//no leading zeros or leading zeros are allowed\n                const num = Number(trimmedStr);\n                const numStr = \"\" + num;\n\n                if(numStr.search(/[eE]/) !== -1){ //given number is long and parsed to eNotation\n                    if(options.eNotation) return num;\n                    else return str;\n                }else if(trimmedStr.indexOf(\".\") !== -1){ //floating number\n                    if(numStr === \"0\" && (numTrimmedByZeros === \"\") ) return num; //0.0\n                    else if(numStr === numTrimmedByZeros) return num; //0.456. 0.79000\n                    else if( sign && numStr === \"-\"+numTrimmedByZeros) return num;\n                    else return str;\n                }\n                \n                if(leadingZeros){\n                    return (numTrimmedByZeros === numStr) || (sign+numTrimmedByZeros === numStr) ? num : str\n                }else  {\n                    return (trimmedStr === numStr) || (trimmedStr === sign+numStr) ? num : str\n                }\n            }\n        }else{ //non-numeric string\n            return str;\n        }\n    }\n}\n\n/**\n * \n * @param {string} numStr without leading zeros\n * @returns \n */\nfunction trimZeros(numStr){\n    if(numStr && numStr.indexOf(\".\") !== -1){//float\n        numStr = numStr.replace(/0+$/, \"\"); //remove ending zeros\n        if(numStr === \".\")  numStr = \"0\";\n        else if(numStr[0] === \".\")  numStr = \"0\"+numStr;\n        else if(numStr[numStr.length-1] === \".\")  numStr = numStr.substr(0,numStr.length-1);\n        return numStr;\n    }\n    return numStr;\n}\n\nfunction parse_int(numStr, base){\n    //polyfill\n    if(parseInt) return parseInt(numStr, base);\n    else if(Number.parseInt) return Number.parseInt(numStr, base);\n    else if(window && window.parseInt) return window.parseInt(numStr, base);\n    else throw new Error(\"parseInt, Number.parseInt, window.parseInt are not supported\")\n}\n\nmodule.exports = toNumber;", "'use strict';\n///@ts-check\n\nconst util = require('../util');\nconst xmlNode = require('./xmlNode');\nconst readDocType = require(\"./DocTypeReader\");\nconst toNumber = require(\"strnum\");\n\n// const regx =\n//   '<((!\\\\[CDATA\\\\[([\\\\s\\\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\\\/)(NAME)\\\\s*>))([^<]*)'\n//   .replace(/NAME/g, util.nameRegexp);\n\n//const tagsRegx = new RegExp(\"<(\\\\/?[\\\\w:\\\\-\\._]+)([^>]*)>(\\\\s*\"+cdataRegx+\")*([^<]+)?\",\"g\");\n//const tagsRegx = new RegExp(\"<(\\\\/?)((\\\\w*:)?([\\\\w:\\\\-\\._]+))([^>]*)>([^<]*)(\"+cdataRegx+\"([^<]*))*([^<]+)?\",\"g\");\n\nclass OrderedObjParser{\n  constructor(options){\n    this.options = options;\n    this.currentNode = null;\n    this.tagsNodeStack = [];\n    this.docTypeEntities = {};\n    this.lastEntities = {\n      \"apos\" : { regex: /&(apos|#39|#x27);/g, val : \"'\"},\n      \"gt\" : { regex: /&(gt|#62|#x3E);/g, val : \">\"},\n      \"lt\" : { regex: /&(lt|#60|#x3C);/g, val : \"<\"},\n      \"quot\" : { regex: /&(quot|#34|#x22);/g, val : \"\\\"\"},\n    };\n    this.ampEntity = { regex: /&(amp|#38|#x26);/g, val : \"&\"};\n    this.htmlEntities = {\n      \"space\": { regex: /&(nbsp|#160);/g, val: \" \" },\n      // \"lt\" : { regex: /&(lt|#60);/g, val: \"<\" },\n      // \"gt\" : { regex: /&(gt|#62);/g, val: \">\" },\n      // \"amp\" : { regex: /&(amp|#38);/g, val: \"&\" },\n      // \"quot\" : { regex: /&(quot|#34);/g, val: \"\\\"\" },\n      // \"apos\" : { regex: /&(apos|#39);/g, val: \"'\" },\n      \"cent\" : { regex: /&(cent|#162);/g, val: \"¢\" },\n      \"pound\" : { regex: /&(pound|#163);/g, val: \"£\" },\n      \"yen\" : { regex: /&(yen|#165);/g, val: \"¥\" },\n      \"euro\" : { regex: /&(euro|#8364);/g, val: \"€\" },\n      \"copyright\" : { regex: /&(copy|#169);/g, val: \"©\" },\n      \"reg\" : { regex: /&(reg|#174);/g, val: \"®\" },\n      \"inr\" : { regex: /&(inr|#8377);/g, val: \"₹\" },\n      \"num_dec\": { regex: /&#([0-9]{1,7});/g, val : (_, str) => String.fromCharCode(Number.parseInt(str, 10)) },\n      \"num_hex\": { regex: /&#x([0-9a-fA-F]{1,6});/g, val : (_, str) => String.fromCharCode(Number.parseInt(str, 16)) },\n    };\n    this.addExternalEntities = addExternalEntities;\n    this.parseXml = parseXml;\n    this.parseTextData = parseTextData;\n    this.resolveNameSpace = resolveNameSpace;\n    this.buildAttributesMap = buildAttributesMap;\n    this.isItStopNode = isItStopNode;\n    this.replaceEntitiesValue = replaceEntitiesValue;\n    this.readStopNodeData = readStopNodeData;\n    this.saveTextToParentTag = saveTextToParentTag;\n    this.addChild = addChild;\n  }\n\n}\n\nfunction addExternalEntities(externalEntities){\n  const entKeys = Object.keys(externalEntities);\n  for (let i = 0; i < entKeys.length; i++) {\n    const ent = entKeys[i];\n    this.lastEntities[ent] = {\n       regex: new RegExp(\"&\"+ent+\";\",\"g\"),\n       val : externalEntities[ent]\n    }\n  }\n}\n\n/**\n * @param {string} val\n * @param {string} tagName\n * @param {string} jPath\n * @param {boolean} dontTrim\n * @param {boolean} hasAttributes\n * @param {boolean} isLeafNode\n * @param {boolean} escapeEntities\n */\nfunction parseTextData(val, tagName, jPath, dontTrim, hasAttributes, isLeafNode, escapeEntities) {\n  if (val !== undefined) {\n    if (this.options.trimValues && !dontTrim) {\n      val = val.trim();\n    }\n    if(val.length > 0){\n      if(!escapeEntities) val = this.replaceEntitiesValue(val);\n      \n      const newval = this.options.tagValueProcessor(tagName, val, jPath, hasAttributes, isLeafNode);\n      if(newval === null || newval === undefined){\n        //don't parse\n        return val;\n      }else if(typeof newval !== typeof val || newval !== val){\n        //overwrite\n        return newval;\n      }else if(this.options.trimValues){\n        return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n      }else{\n        const trimmedVal = val.trim();\n        if(trimmedVal === val){\n          return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n        }else{\n          return val;\n        }\n      }\n    }\n  }\n}\n\nfunction resolveNameSpace(tagname) {\n  if (this.options.removeNSPrefix) {\n    const tags = tagname.split(':');\n    const prefix = tagname.charAt(0) === '/' ? '/' : '';\n    if (tags[0] === 'xmlns') {\n      return '';\n    }\n    if (tags.length === 2) {\n      tagname = prefix + tags[1];\n    }\n  }\n  return tagname;\n}\n\n//TODO: change regex to capture NS\n//const attrsRegx = new RegExp(\"([\\\\w\\\\-\\\\.\\\\:]+)\\\\s*=\\\\s*(['\\\"])((.|\\n)*?)\\\\2\",\"gm\");\nconst attrsRegx = new RegExp('([^\\\\s=]+)\\\\s*(=\\\\s*([\\'\"])([\\\\s\\\\S]*?)\\\\3)?', 'gm');\n\nfunction buildAttributesMap(attrStr, jPath, tagName) {\n  if (!this.options.ignoreAttributes && typeof attrStr === 'string') {\n    // attrStr = attrStr.replace(/\\r?\\n/g, ' ');\n    //attrStr = attrStr || attrStr.trim();\n\n    const matches = util.getAllMatches(attrStr, attrsRegx);\n    const len = matches.length; //don't make it inline\n    const attrs = {};\n    for (let i = 0; i < len; i++) {\n      const attrName = this.resolveNameSpace(matches[i][1]);\n      let oldVal = matches[i][4];\n      let aName = this.options.attributeNamePrefix + attrName;\n      if (attrName.length) {\n        if (this.options.transformAttributeName) {\n          aName = this.options.transformAttributeName(aName);\n        }\n        if(aName === \"__proto__\") aName  = \"#__proto__\";\n        if (oldVal !== undefined) {\n          if (this.options.trimValues) {\n            oldVal = oldVal.trim();\n          }\n          oldVal = this.replaceEntitiesValue(oldVal);\n          const newVal = this.options.attributeValueProcessor(attrName, oldVal, jPath);\n          if(newVal === null || newVal === undefined){\n            //don't parse\n            attrs[aName] = oldVal;\n          }else if(typeof newVal !== typeof oldVal || newVal !== oldVal){\n            //overwrite\n            attrs[aName] = newVal;\n          }else{\n            //parse\n            attrs[aName] = parseValue(\n              oldVal,\n              this.options.parseAttributeValue,\n              this.options.numberParseOptions\n            );\n          }\n        } else if (this.options.allowBooleanAttributes) {\n          attrs[aName] = true;\n        }\n      }\n    }\n    if (!Object.keys(attrs).length) {\n      return;\n    }\n    if (this.options.attributesGroupName) {\n      const attrCollection = {};\n      attrCollection[this.options.attributesGroupName] = attrs;\n      return attrCollection;\n    }\n    return attrs\n  }\n}\n\nconst parseXml = function(xmlData) {\n  xmlData = xmlData.replace(/\\r\\n?/g, \"\\n\"); //TODO: remove this line\n  const xmlObj = new xmlNode('!xml');\n  let currentNode = xmlObj;\n  let textData = \"\";\n  let jPath = \"\";\n  for(let i=0; i< xmlData.length; i++){//for each char in XML data\n    const ch = xmlData[i];\n    if(ch === '<'){\n      // const nextIndex = i+1;\n      // const _2ndChar = xmlData[nextIndex];\n      if( xmlData[i+1] === '/') {//Closing Tag\n        const closeIndex = findClosingIndex(xmlData, \">\", i, \"Closing Tag is not closed.\")\n        let tagName = xmlData.substring(i+2,closeIndex).trim();\n\n        if(this.options.removeNSPrefix){\n          const colonIndex = tagName.indexOf(\":\");\n          if(colonIndex !== -1){\n            tagName = tagName.substr(colonIndex+1);\n          }\n        }\n\n        if(this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n\n        if(currentNode){\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        }\n\n        //check if last tag of nested tag was unpaired tag\n        const lastTagName = jPath.substring(jPath.lastIndexOf(\".\")+1);\n        if(tagName && this.options.unpairedTags.indexOf(tagName) !== -1 ){\n          throw new Error(`Unpaired tag can not be used as closing tag: </${tagName}>`);\n        }\n        let propIndex = 0\n        if(lastTagName && this.options.unpairedTags.indexOf(lastTagName) !== -1 ){\n          propIndex = jPath.lastIndexOf('.', jPath.lastIndexOf('.')-1)\n          this.tagsNodeStack.pop();\n        }else{\n          propIndex = jPath.lastIndexOf(\".\");\n        }\n        jPath = jPath.substring(0, propIndex);\n\n        currentNode = this.tagsNodeStack.pop();//avoid recursion, set the parent tag scope\n        textData = \"\";\n        i = closeIndex;\n      } else if( xmlData[i+1] === '?') {\n\n        let tagData = readTagExp(xmlData,i, false, \"?>\");\n        if(!tagData) throw new Error(\"Pi Tag is not closed.\");\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        if( (this.options.ignoreDeclaration && tagData.tagName === \"?xml\") || this.options.ignorePiTags){\n\n        }else{\n  \n          const childNode = new xmlNode(tagData.tagName);\n          childNode.add(this.options.textNodeName, \"\");\n          \n          if(tagData.tagName !== tagData.tagExp && tagData.attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagData.tagExp, jPath, tagData.tagName);\n          }\n          this.addChild(currentNode, childNode, jPath)\n\n        }\n\n\n        i = tagData.closeIndex + 1;\n      } else if(xmlData.substr(i + 1, 3) === '!--') {\n        const endIndex = findClosingIndex(xmlData, \"-->\", i+4, \"Comment is not closed.\")\n        if(this.options.commentPropName){\n          const comment = xmlData.substring(i + 4, endIndex - 2);\n\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n          currentNode.add(this.options.commentPropName, [ { [this.options.textNodeName] : comment } ]);\n        }\n        i = endIndex;\n      } else if( xmlData.substr(i + 1, 2) === '!D') {\n        const result = readDocType(xmlData, i);\n        this.docTypeEntities = result.entities;\n        i = result.i;\n      }else if(xmlData.substr(i + 1, 2) === '![') {\n        const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"CDATA is not closed.\") - 2;\n        const tagExp = xmlData.substring(i + 9,closeIndex);\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n        let val = this.parseTextData(tagExp, currentNode.tagname, jPath, true, false, true, true);\n        if(val == undefined) val = \"\";\n\n        //cdata should be set even if it is 0 length string\n        if(this.options.cdataPropName){\n          currentNode.add(this.options.cdataPropName, [ { [this.options.textNodeName] : tagExp } ]);\n        }else{\n          currentNode.add(this.options.textNodeName, val);\n        }\n        \n        i = closeIndex + 2;\n      }else {//Opening tag\n        let result = readTagExp(xmlData,i, this.options.removeNSPrefix);\n        let tagName= result.tagName;\n        const rawTagName = result.rawTagName;\n        let tagExp = result.tagExp;\n        let attrExpPresent = result.attrExpPresent;\n        let closeIndex = result.closeIndex;\n\n        if (this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n        \n        //save text as child node\n        if (currentNode && textData) {\n          if(currentNode.tagname !== '!xml'){\n            //when nested tag is found\n            textData = this.saveTextToParentTag(textData, currentNode, jPath, false);\n          }\n        }\n\n        //check if last tag was unpaired tag\n        const lastTag = currentNode;\n        if(lastTag && this.options.unpairedTags.indexOf(lastTag.tagname) !== -1 ){\n          currentNode = this.tagsNodeStack.pop();\n          jPath = jPath.substring(0, jPath.lastIndexOf(\".\"));\n        }\n        if(tagName !== xmlObj.tagname){\n          jPath += jPath ? \".\" + tagName : tagName;\n        }\n        if (this.isItStopNode(this.options.stopNodes, jPath, tagName)) {\n          let tagContent = \"\";\n          //self-closing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            i = result.closeIndex;\n          }\n          //unpaired tag\n          else if(this.options.unpairedTags.indexOf(tagName) !== -1){\n            \n            i = result.closeIndex;\n          }\n          //normal tag\n          else{\n            //read until closing tag is found\n            const result = this.readStopNodeData(xmlData, rawTagName, closeIndex + 1);\n            if(!result) throw new Error(`Unexpected end of ${rawTagName}`);\n            i = result.i;\n            tagContent = result.tagContent;\n          }\n\n          const childNode = new xmlNode(tagName);\n          if(tagName !== tagExp && attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n          }\n          if(tagContent) {\n            tagContent = this.parseTextData(tagContent, tagName, jPath, true, attrExpPresent, true, true);\n          }\n          \n          jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          childNode.add(this.options.textNodeName, tagContent);\n          \n          this.addChild(currentNode, childNode, jPath)\n        }else{\n  //selfClosing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            \n            if(this.options.transformTagName) {\n              tagName = this.options.transformTagName(tagName);\n            }\n\n            const childNode = new xmlNode(tagName);\n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath)\n            jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          }\n    //opening tag\n          else{\n            const childNode = new xmlNode( tagName);\n            this.tagsNodeStack.push(currentNode);\n            \n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath)\n            currentNode = childNode;\n          }\n          textData = \"\";\n          i = closeIndex;\n        }\n      }\n    }else{\n      textData += xmlData[i];\n    }\n  }\n  return xmlObj.child;\n}\n\nfunction addChild(currentNode, childNode, jPath){\n  const result = this.options.updateTag(childNode.tagname, jPath, childNode[\":@\"])\n  if(result === false){\n  }else if(typeof result === \"string\"){\n    childNode.tagname = result\n    currentNode.addChild(childNode);\n  }else{\n    currentNode.addChild(childNode);\n  }\n}\n\nconst replaceEntitiesValue = function(val){\n\n  if(this.options.processEntities){\n    for(let entityName in this.docTypeEntities){\n      const entity = this.docTypeEntities[entityName];\n      val = val.replace( entity.regx, entity.val);\n    }\n    for(let entityName in this.lastEntities){\n      const entity = this.lastEntities[entityName];\n      val = val.replace( entity.regex, entity.val);\n    }\n    if(this.options.htmlEntities){\n      for(let entityName in this.htmlEntities){\n        const entity = this.htmlEntities[entityName];\n        val = val.replace( entity.regex, entity.val);\n      }\n    }\n    val = val.replace( this.ampEntity.regex, this.ampEntity.val);\n  }\n  return val;\n}\nfunction saveTextToParentTag(textData, currentNode, jPath, isLeafNode) {\n  if (textData) { //store previously collected data as textNode\n    if(isLeafNode === undefined) isLeafNode = Object.keys(currentNode.child).length === 0\n    \n    textData = this.parseTextData(textData,\n      currentNode.tagname,\n      jPath,\n      false,\n      currentNode[\":@\"] ? Object.keys(currentNode[\":@\"]).length !== 0 : false,\n      isLeafNode);\n\n    if (textData !== undefined && textData !== \"\")\n      currentNode.add(this.options.textNodeName, textData);\n    textData = \"\";\n  }\n  return textData;\n}\n\n//TODO: use jPath to simplify the logic\n/**\n * \n * @param {string[]} stopNodes \n * @param {string} jPath\n * @param {string} currentTagName \n */\nfunction isItStopNode(stopNodes, jPath, currentTagName){\n  const allNodesExp = \"*.\" + currentTagName;\n  for (const stopNodePath in stopNodes) {\n    const stopNodeExp = stopNodes[stopNodePath];\n    if( allNodesExp === stopNodeExp || jPath === stopNodeExp  ) return true;\n  }\n  return false;\n}\n\n/**\n * Returns the tag Expression and where it is ending handling single-double quotes situation\n * @param {string} xmlData \n * @param {number} i starting index\n * @returns \n */\nfunction tagExpWithClosingIndex(xmlData, i, closingChar = \">\"){\n  let attrBoundary;\n  let tagExp = \"\";\n  for (let index = i; index < xmlData.length; index++) {\n    let ch = xmlData[index];\n    if (attrBoundary) {\n        if (ch === attrBoundary) attrBoundary = \"\";//reset\n    } else if (ch === '\"' || ch === \"'\") {\n        attrBoundary = ch;\n    } else if (ch === closingChar[0]) {\n      if(closingChar[1]){\n        if(xmlData[index + 1] === closingChar[1]){\n          return {\n            data: tagExp,\n            index: index\n          }\n        }\n      }else{\n        return {\n          data: tagExp,\n          index: index\n        }\n      }\n    } else if (ch === '\\t') {\n      ch = \" \"\n    }\n    tagExp += ch;\n  }\n}\n\nfunction findClosingIndex(xmlData, str, i, errMsg){\n  const closingIndex = xmlData.indexOf(str, i);\n  if(closingIndex === -1){\n    throw new Error(errMsg)\n  }else{\n    return closingIndex + str.length - 1;\n  }\n}\n\nfunction readTagExp(xmlData,i, removeNSPrefix, closingChar = \">\"){\n  const result = tagExpWithClosingIndex(xmlData, i+1, closingChar);\n  if(!result) return;\n  let tagExp = result.data;\n  const closeIndex = result.index;\n  const separatorIndex = tagExp.search(/\\s/);\n  let tagName = tagExp;\n  let attrExpPresent = true;\n  if(separatorIndex !== -1){//separate tag name and attributes expression\n    tagName = tagExp.substring(0, separatorIndex);\n    tagExp = tagExp.substring(separatorIndex + 1).trimStart();\n  }\n\n  const rawTagName = tagName;\n  if(removeNSPrefix){\n    const colonIndex = tagName.indexOf(\":\");\n    if(colonIndex !== -1){\n      tagName = tagName.substr(colonIndex+1);\n      attrExpPresent = tagName !== result.data.substr(colonIndex + 1);\n    }\n  }\n\n  return {\n    tagName: tagName,\n    tagExp: tagExp,\n    closeIndex: closeIndex,\n    attrExpPresent: attrExpPresent,\n    rawTagName: rawTagName,\n  }\n}\n/**\n * find paired tag for a stop node\n * @param {string} xmlData \n * @param {string} tagName \n * @param {number} i \n */\nfunction readStopNodeData(xmlData, tagName, i){\n  const startIndex = i;\n  // Starting at 1 since we already have an open tag\n  let openTagCount = 1;\n\n  for (; i < xmlData.length; i++) {\n    if( xmlData[i] === \"<\"){ \n      if (xmlData[i+1] === \"/\") {//close tag\n          const closeIndex = findClosingIndex(xmlData, \">\", i, `${tagName} is not closed`);\n          let closeTagName = xmlData.substring(i+2,closeIndex).trim();\n          if(closeTagName === tagName){\n            openTagCount--;\n            if (openTagCount === 0) {\n              return {\n                tagContent: xmlData.substring(startIndex, i),\n                i : closeIndex\n              }\n            }\n          }\n          i=closeIndex;\n        } else if(xmlData[i+1] === '?') { \n          const closeIndex = findClosingIndex(xmlData, \"?>\", i+1, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 3) === '!--') { \n          const closeIndex = findClosingIndex(xmlData, \"-->\", i+3, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 2) === '![') { \n          const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"StopNode is not closed.\") - 2;\n          i=closeIndex;\n        } else {\n          const tagData = readTagExp(xmlData, i, '>')\n\n          if (tagData) {\n            const openTagName = tagData && tagData.tagName;\n            if (openTagName === tagName && tagData.tagExp[tagData.tagExp.length-1] !== \"/\") {\n              openTagCount++;\n            }\n            i=tagData.closeIndex;\n          }\n        }\n      }\n  }//end for loop\n}\n\nfunction parseValue(val, shouldParse, options) {\n  if (shouldParse && typeof val === 'string') {\n    //console.log(options)\n    const newval = val.trim();\n    if(newval === 'true' ) return true;\n    else if(newval === 'false' ) return false;\n    else return toNumber(val, options);\n  } else {\n    if (util.isExist(val)) {\n      return val;\n    } else {\n      return '';\n    }\n  }\n}\n\n\nmodule.exports = OrderedObjParser;\n", "'use strict';\n\n/**\n * \n * @param {array} node \n * @param {any} options \n * @returns \n */\nfunction prettify(node, options){\n  return compress( node, options);\n}\n\n/**\n * \n * @param {array} arr \n * @param {object} options \n * @param {string} jPath \n * @returns object\n */\nfunction compress(arr, options, jPath){\n  let text;\n  const compressedObj = {};\n  for (let i = 0; i < arr.length; i++) {\n    const tagObj = arr[i];\n    const property = propName(tagObj);\n    let newJpath = \"\";\n    if(jPath === undefined) newJpath = property;\n    else newJpath = jPath + \".\" + property;\n\n    if(property === options.textNodeName){\n      if(text === undefined) text = tagObj[property];\n      else text += \"\" + tagObj[property];\n    }else if(property === undefined){\n      continue;\n    }else if(tagObj[property]){\n      \n      let val = compress(tagObj[property], options, newJpath);\n      const isLeaf = isLeafTag(val, options);\n\n      if(tagObj[\":@\"]){\n        assignAttributes( val, tagObj[\":@\"], newJpath, options);\n      }else if(Object.keys(val).length === 1 && val[options.textNodeName] !== undefined && !options.alwaysCreateTextNode){\n        val = val[options.textNodeName];\n      }else if(Object.keys(val).length === 0){\n        if(options.alwaysCreateTextNode) val[options.textNodeName] = \"\";\n        else val = \"\";\n      }\n\n      if(compressedObj[property] !== undefined && compressedObj.hasOwnProperty(property)) {\n        if(!Array.isArray(compressedObj[property])) {\n            compressedObj[property] = [ compressedObj[property] ];\n        }\n        compressedObj[property].push(val);\n      }else{\n        //TODO: if a node is not an array, then check if it should be an array\n        //also determine if it is a leaf node\n        if (options.isArray(property, newJpath, isLeaf )) {\n          compressedObj[property] = [val];\n        }else{\n          compressedObj[property] = val;\n        }\n      }\n    }\n    \n  }\n  // if(text && text.length > 0) compressedObj[options.textNodeName] = text;\n  if(typeof text === \"string\"){\n    if(text.length > 0) compressedObj[options.textNodeName] = text;\n  }else if(text !== undefined) compressedObj[options.textNodeName] = text;\n  return compressedObj;\n}\n\nfunction propName(obj){\n  const keys = Object.keys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if(key !== \":@\") return key;\n  }\n}\n\nfunction assignAttributes(obj, attrMap, jpath, options){\n  if (attrMap) {\n    const keys = Object.keys(attrMap);\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      const atrrName = keys[i];\n      if (options.isArray(atrrName, jpath + \".\" + atrrName, true, true)) {\n        obj[atrrName] = [ attrMap[atrrName] ];\n      } else {\n        obj[atrrName] = attrMap[atrrName];\n      }\n    }\n  }\n}\n\nfunction isLeafTag(obj, options){\n  const { textNodeName } = options;\n  const propCount = Object.keys(obj).length;\n  \n  if (propCount === 0) {\n    return true;\n  }\n\n  if (\n    propCount === 1 &&\n    (obj[textNodeName] || typeof obj[textNodeName] === \"boolean\" || obj[textNodeName] === 0)\n  ) {\n    return true;\n  }\n\n  return false;\n}\nexports.prettify = prettify;\n", "const { buildOptions} = require(\"./OptionsBuilder\");\nconst OrderedObjParser = require(\"./OrderedObjParser\");\nconst { prettify} = require(\"./node2json\");\nconst validator = require('../validator');\n\nclass XMLParser{\n    \n    constructor(options){\n        this.externalEntities = {};\n        this.options = buildOptions(options);\n        \n    }\n    /**\n     * Parse XML dats to JS object \n     * @param {string|Buffer} xmlData \n     * @param {boolean|Object} validationOption \n     */\n    parse(xmlData,validationOption){\n        if(typeof xmlData === \"string\"){\n        }else if( xmlData.toString){\n            xmlData = xmlData.toString();\n        }else{\n            throw new Error(\"XML data is accepted in String or Bytes[] form.\")\n        }\n        if( validationOption){\n            if(validationOption === true) validationOption = {}; //validate with default options\n            \n            const result = validator.validate(xmlData, validationOption);\n            if (result !== true) {\n              throw Error( `${result.err.msg}:${result.err.line}:${result.err.col}` )\n            }\n          }\n        const orderedObjParser = new OrderedObjParser(this.options);\n        orderedObjParser.addExternalEntities(this.externalEntities);\n        const orderedResult = orderedObjParser.parseXml(xmlData);\n        if(this.options.preserveOrder || orderedResult === undefined) return orderedResult;\n        else return prettify(orderedResult, this.options);\n    }\n\n    /**\n     * Add Entity which is not by default supported by this library\n     * @param {string} key \n     * @param {string} value \n     */\n    addEntity(key, value){\n        if(value.indexOf(\"&\") !== -1){\n            throw new Error(\"Entity value can't have '&'\")\n        }else if(key.indexOf(\"&\") !== -1 || key.indexOf(\";\") !== -1){\n            throw new Error(\"An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'\")\n        }else if(value === \"&\"){\n            throw new Error(\"An entity with value '&' is not permitted\");\n        }else{\n            this.externalEntities[key] = value;\n        }\n    }\n}\n\nmodule.exports = XMLParser;", "const EOL = \"\\n\";\n\n/**\n * \n * @param {array} jArray \n * @param {any} options \n * @returns \n */\nfunction toXml(jArray, options) {\n    let indentation = \"\";\n    if (options.format && options.indentBy.length > 0) {\n        indentation = EOL;\n    }\n    return arrToStr(jArray, options, \"\", indentation);\n}\n\nfunction arrToStr(arr, options, jPath, indentation) {\n    let xmlStr = \"\";\n    let isPreviousElementTag = false;\n\n    for (let i = 0; i < arr.length; i++) {\n        const tagObj = arr[i];\n        const tagName = propName(tagObj);\n        if(tagName === undefined) continue;\n\n        let newJPath = \"\";\n        if (jPath.length === 0) newJPath = tagName\n        else newJPath = `${jPath}.${tagName}`;\n\n        if (tagName === options.textNodeName) {\n            let tagText = tagObj[tagName];\n            if (!isStopNode(newJPath, options)) {\n                tagText = options.tagValueProcessor(tagName, tagText);\n                tagText = replaceEntitiesValue(tagText, options);\n            }\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += tagText;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.cdataPropName) {\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += `<![CDATA[${tagObj[tagName][0][options.textNodeName]}]]>`;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.commentPropName) {\n            xmlStr += indentation + `<!--${tagObj[tagName][0][options.textNodeName]}-->`;\n            isPreviousElementTag = true;\n            continue;\n        } else if (tagName[0] === \"?\") {\n            const attStr = attr_to_str(tagObj[\":@\"], options);\n            const tempInd = tagName === \"?xml\" ? \"\" : indentation;\n            let piTextNodeName = tagObj[tagName][0][options.textNodeName];\n            piTextNodeName = piTextNodeName.length !== 0 ? \" \" + piTextNodeName : \"\"; //remove extra spacing\n            xmlStr += tempInd + `<${tagName}${piTextNodeName}${attStr}?>`;\n            isPreviousElementTag = true;\n            continue;\n        }\n        let newIdentation = indentation;\n        if (newIdentation !== \"\") {\n            newIdentation += options.indentBy;\n        }\n        const attStr = attr_to_str(tagObj[\":@\"], options);\n        const tagStart = indentation + `<${tagName}${attStr}`;\n        const tagValue = arrToStr(tagObj[tagName], options, newJPath, newIdentation);\n        if (options.unpairedTags.indexOf(tagName) !== -1) {\n            if (options.suppressUnpairedNode) xmlStr += tagStart + \">\";\n            else xmlStr += tagStart + \"/>\";\n        } else if ((!tagValue || tagValue.length === 0) && options.suppressEmptyNode) {\n            xmlStr += tagStart + \"/>\";\n        } else if (tagValue && tagValue.endsWith(\">\")) {\n            xmlStr += tagStart + `>${tagValue}${indentation}</${tagName}>`;\n        } else {\n            xmlStr += tagStart + \">\";\n            if (tagValue && indentation !== \"\" && (tagValue.includes(\"/>\") || tagValue.includes(\"</\"))) {\n                xmlStr += indentation + options.indentBy + tagValue + indentation;\n            } else {\n                xmlStr += tagValue;\n            }\n            xmlStr += `</${tagName}>`;\n        }\n        isPreviousElementTag = true;\n    }\n\n    return xmlStr;\n}\n\nfunction propName(obj) {\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        if(!obj.hasOwnProperty(key)) continue;\n        if (key !== \":@\") return key;\n    }\n}\n\nfunction attr_to_str(attrMap, options) {\n    let attrStr = \"\";\n    if (attrMap && !options.ignoreAttributes) {\n        for (let attr in attrMap) {\n            if(!attrMap.hasOwnProperty(attr)) continue;\n            let attrVal = options.attributeValueProcessor(attr, attrMap[attr]);\n            attrVal = replaceEntitiesValue(attrVal, options);\n            if (attrVal === true && options.suppressBooleanAttributes) {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}`;\n            } else {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}=\"${attrVal}\"`;\n            }\n        }\n    }\n    return attrStr;\n}\n\nfunction isStopNode(jPath, options) {\n    jPath = jPath.substr(0, jPath.length - options.textNodeName.length - 1);\n    let tagName = jPath.substr(jPath.lastIndexOf(\".\") + 1);\n    for (let index in options.stopNodes) {\n        if (options.stopNodes[index] === jPath || options.stopNodes[index] === \"*.\" + tagName) return true;\n    }\n    return false;\n}\n\nfunction replaceEntitiesValue(textValue, options) {\n    if (textValue && textValue.length > 0 && options.processEntities) {\n        for (let i = 0; i < options.entities.length; i++) {\n            const entity = options.entities[i];\n            textValue = textValue.replace(entity.regex, entity.val);\n        }\n    }\n    return textValue;\n}\nmodule.exports = toXml;\n", "'use strict';\n//parse Empty Node as self closing node\nconst buildFromOrderedJs = require('./orderedJs2Xml');\n\nconst defaultOptions = {\n  attributeNamePrefix: '@_',\n  attributesGroupName: false,\n  textNodeName: '#text',\n  ignoreAttributes: true,\n  cdataPropName: false,\n  format: false,\n  indentBy: '  ',\n  suppressEmptyNode: false,\n  suppressUnpairedNode: true,\n  suppressBooleanAttributes: true,\n  tagValueProcessor: function(key, a) {\n    return a;\n  },\n  attributeValueProcessor: function(attrName, a) {\n    return a;\n  },\n  preserveOrder: false,\n  commentPropName: false,\n  unpairedTags: [],\n  entities: [\n    { regex: new RegExp(\"&\", \"g\"), val: \"&amp;\" },//it must be on top\n    { regex: new RegExp(\">\", \"g\"), val: \"&gt;\" },\n    { regex: new RegExp(\"<\", \"g\"), val: \"&lt;\" },\n    { regex: new RegExp(\"\\'\", \"g\"), val: \"&apos;\" },\n    { regex: new RegExp(\"\\\"\", \"g\"), val: \"&quot;\" }\n  ],\n  processEntities: true,\n  stopNodes: [],\n  // transformTagName: false,\n  // transformAttributeName: false,\n  oneListGroup: false\n};\n\nfunction Builder(options) {\n  this.options = Object.assign({}, defaultOptions, options);\n  if (this.options.ignoreAttributes || this.options.attributesGroupName) {\n    this.isAttribute = function(/*a*/) {\n      return false;\n    };\n  } else {\n    this.attrPrefixLen = this.options.attributeNamePrefix.length;\n    this.isAttribute = isAttribute;\n  }\n\n  this.processTextOrObjNode = processTextOrObjNode\n\n  if (this.options.format) {\n    this.indentate = indentate;\n    this.tagEndChar = '>\\n';\n    this.newLine = '\\n';\n  } else {\n    this.indentate = function() {\n      return '';\n    };\n    this.tagEndChar = '>';\n    this.newLine = '';\n  }\n}\n\nBuilder.prototype.build = function(jObj) {\n  if(this.options.preserveOrder){\n    return buildFromOrderedJs(jObj, this.options);\n  }else {\n    if(Array.isArray(jObj) && this.options.arrayNodeName && this.options.arrayNodeName.length > 1){\n      jObj = {\n        [this.options.arrayNodeName] : jObj\n      }\n    }\n    return this.j2x(jObj, 0).val;\n  }\n};\n\nBuilder.prototype.j2x = function(jObj, level) {\n  let attrStr = '';\n  let val = '';\n  for (let key in jObj) {\n    if(!Object.prototype.hasOwnProperty.call(jObj, key)) continue;\n    if (typeof jObj[key] === 'undefined') {\n      // supress undefined node only if it is not an attribute\n      if (this.isAttribute(key)) {\n        val += '';\n      }\n    } else if (jObj[key] === null) {\n      // null attribute should be ignored by the attribute list, but should not cause the tag closing\n      if (this.isAttribute(key)) {\n        val += '';\n      } else if (key[0] === '?') {\n        val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n      } else {\n        val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n      }\n      // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n    } else if (jObj[key] instanceof Date) {\n      val += this.buildTextValNode(jObj[key], key, '', level);\n    } else if (typeof jObj[key] !== 'object') {\n      //premitive type\n      const attr = this.isAttribute(key);\n      if (attr) {\n        attrStr += this.buildAttrPairStr(attr, '' + jObj[key]);\n      }else {\n        //tag value\n        if (key === this.options.textNodeName) {\n          let newval = this.options.tagValueProcessor(key, '' + jObj[key]);\n          val += this.replaceEntitiesValue(newval);\n        } else {\n          val += this.buildTextValNode(jObj[key], key, '', level);\n        }\n      }\n    } else if (Array.isArray(jObj[key])) {\n      //repeated nodes\n      const arrLen = jObj[key].length;\n      let listTagVal = \"\";\n      let listTagAttr = \"\";\n      for (let j = 0; j < arrLen; j++) {\n        const item = jObj[key][j];\n        if (typeof item === 'undefined') {\n          // supress undefined node\n        } else if (item === null) {\n          if(key[0] === \"?\") val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n          else val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n          // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n        } else if (typeof item === 'object') {\n          if(this.options.oneListGroup){\n            const result = this.j2x(item, level + 1);\n            listTagVal += result.val;\n            if (this.options.attributesGroupName && item.hasOwnProperty(this.options.attributesGroupName)) {\n              listTagAttr += result.attrStr\n            }\n          }else{\n            listTagVal += this.processTextOrObjNode(item, key, level)\n          }\n        } else {\n          if (this.options.oneListGroup) {\n            let textValue = this.options.tagValueProcessor(key, item);\n            textValue = this.replaceEntitiesValue(textValue);\n            listTagVal += textValue;\n          } else {\n            listTagVal += this.buildTextValNode(item, key, '', level);\n          }\n        }\n      }\n      if(this.options.oneListGroup){\n        listTagVal = this.buildObjectNode(listTagVal, key, listTagAttr, level);\n      }\n      val += listTagVal;\n    } else {\n      //nested node\n      if (this.options.attributesGroupName && key === this.options.attributesGroupName) {\n        const Ks = Object.keys(jObj[key]);\n        const L = Ks.length;\n        for (let j = 0; j < L; j++) {\n          attrStr += this.buildAttrPairStr(Ks[j], '' + jObj[key][Ks[j]]);\n        }\n      } else {\n        val += this.processTextOrObjNode(jObj[key], key, level)\n      }\n    }\n  }\n  return {attrStr: attrStr, val: val};\n};\n\nBuilder.prototype.buildAttrPairStr = function(attrName, val){\n  val = this.options.attributeValueProcessor(attrName, '' + val);\n  val = this.replaceEntitiesValue(val);\n  if (this.options.suppressBooleanAttributes && val === \"true\") {\n    return ' ' + attrName;\n  } else return ' ' + attrName + '=\"' + val + '\"';\n}\n\nfunction processTextOrObjNode (object, key, level) {\n  const result = this.j2x(object, level + 1);\n  if (object[this.options.textNodeName] !== undefined && Object.keys(object).length === 1) {\n    return this.buildTextValNode(object[this.options.textNodeName], key, result.attrStr, level);\n  } else {\n    return this.buildObjectNode(result.val, key, result.attrStr, level);\n  }\n}\n\nBuilder.prototype.buildObjectNode = function(val, key, attrStr, level) {\n  if(val === \"\"){\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }\n  }else{\n\n    let tagEndExp = '</' + key + this.tagEndChar;\n    let piClosingChar = \"\";\n    \n    if(key[0] === \"?\") {\n      piClosingChar = \"?\";\n      tagEndExp = \"\";\n    }\n  \n    // attrStr is an empty string in case the attribute came as undefined or null\n    if ((attrStr || attrStr === '') && val.indexOf('<') === -1) {\n      return ( this.indentate(level) + '<' +  key + attrStr + piClosingChar + '>' + val + tagEndExp );\n    } else if (this.options.commentPropName !== false && key === this.options.commentPropName && piClosingChar.length === 0) {\n      return this.indentate(level) + `<!--${val}-->` + this.newLine;\n    }else {\n      return (\n        this.indentate(level) + '<' + key + attrStr + piClosingChar + this.tagEndChar +\n        val +\n        this.indentate(level) + tagEndExp    );\n    }\n  }\n}\n\nBuilder.prototype.closeTag = function(key){\n  let closeTag = \"\";\n  if(this.options.unpairedTags.indexOf(key) !== -1){ //unpaired\n    if(!this.options.suppressUnpairedNode) closeTag = \"/\"\n  }else if(this.options.suppressEmptyNode){ //empty\n    closeTag = \"/\";\n  }else{\n    closeTag = `></${key}`\n  }\n  return closeTag;\n}\n\nfunction buildEmptyObjNode(val, key, attrStr, level) {\n  if (val !== '') {\n    return this.buildObjectNode(val, key, attrStr, level);\n  } else {\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return  this.indentate(level) + '<' + key + attrStr + '/' + this.tagEndChar;\n      // return this.buildTagStr(level,key, attrStr);\n    }\n  }\n}\n\nBuilder.prototype.buildTextValNode = function(val, key, attrStr, level) {\n  if (this.options.cdataPropName !== false && key === this.options.cdataPropName) {\n    return this.indentate(level) + `<![CDATA[${val}]]>` +  this.newLine;\n  }else if (this.options.commentPropName !== false && key === this.options.commentPropName) {\n    return this.indentate(level) + `<!--${val}-->` +  this.newLine;\n  }else if(key[0] === \"?\") {//PI tag\n    return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar; \n  }else{\n    let textValue = this.options.tagValueProcessor(key, val);\n    textValue = this.replaceEntitiesValue(textValue);\n  \n    if( textValue === ''){\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }else{\n      return this.indentate(level) + '<' + key + attrStr + '>' +\n         textValue +\n        '</' + key + this.tagEndChar;\n    }\n  }\n}\n\nBuilder.prototype.replaceEntitiesValue = function(textValue){\n  if(textValue && textValue.length > 0 && this.options.processEntities){\n    for (let i=0; i<this.options.entities.length; i++) {\n      const entity = this.options.entities[i];\n      textValue = textValue.replace(entity.regex, entity.val);\n    }\n  }\n  return textValue;\n}\n\nfunction indentate(level) {\n  return this.options.indentBy.repeat(level);\n}\n\nfunction isAttribute(name /*, options*/) {\n  if (name.startsWith(this.options.attributeNamePrefix) && name !== this.options.textNodeName) {\n    return name.substr(this.attrPrefixLen);\n  } else {\n    return false;\n  }\n}\n\nmodule.exports = Builder;\n", "'use strict';\n\nconst validator = require('./validator');\nconst XMLParser = require('./xmlparser/XMLParser');\nconst XMLBuilder = require('./xmlbuilder/json2xml');\n\nmodule.exports = {\n  XMLParser: XMLParser,\n  XMLValidator: validator,\n  XMLBuilder: XMLBuilder\n}", "import { getValueFromTextNode } from \"@smithy/smithy-client\";\nimport { XMLParser } from \"fast-xml-parser\";\nimport { collectBodyString } from \"../common\";\nexport const parseXmlBody = (streamBody, context) => collectBodyString(streamBody, context).then((encoded) => {\n    if (encoded.length) {\n        const parser = new XMLParser({\n            attributeNamePrefix: \"\",\n            htmlEntities: true,\n            ignoreAttributes: false,\n            ignoreDeclaration: true,\n            parseTagValue: false,\n            trimValues: false,\n            tagValueProcessor: (_, val) => (val.trim() === \"\" && val.includes(\"\\n\") ? \"\" : undefined),\n        });\n        parser.addEntity(\"#xD\", \"\\r\");\n        parser.addEntity(\"#10\", \"\\n\");\n        let parsedObj;\n        try {\n            parsedObj = parser.parse(encoded, true);\n        }\n        catch (e) {\n            if (e && typeof e === \"object\") {\n                Object.defineProperty(e, \"$responseBodyText\", {\n                    value: encoded,\n                });\n            }\n            throw e;\n        }\n        const textNodeName = \"#text\";\n        const key = Object.keys(parsedObj)[0];\n        const parsedObjToReturn = parsedObj[key];\n        if (parsedObjToReturn[textNodeName]) {\n            parsedObjToReturn[key] = parsedObjToReturn[textNodeName];\n            delete parsedObjToReturn[textNodeName];\n        }\n        return getValueFromTextNode(parsedObjToReturn);\n    }\n    return {};\n});\nexport const parseXmlErrorBody = async (errorBody, context) => {\n    const value = await parseXmlBody(errorBody, context);\n    if (value.Error) {\n        value.Error.message = value.Error.message ?? value.Error.Message;\n    }\n    return value;\n};\nexport const loadRestXmlErrorCode = (output, data) => {\n    if (data?.Error?.Code !== undefined) {\n        return data.Error.Code;\n    }\n    if (data?.Code !== undefined) {\n        return data.Code;\n    }\n    if (output.statusCode == 404) {\n        return \"NotFound\";\n    }\n};\n", "export * from \"./coercing-serializers\";\nexport * from \"./json/awsExpectUnion\";\nexport * from \"./json/parseJsonBody\";\nexport * from \"./xml/parseXmlBody\";\n", "export * from \"./submodules/client/index\";\nexport * from \"./submodules/httpAuthSchemes/index\";\nexport * from \"./submodules/protocols/index\";\n", "import { setFeature } from \"@aws-sdk/core\";\nconst ACCOUNT_ID_ENDPOINT_REGEX = /\\d{12}\\.ddb/;\nexport async function checkFeatures(context, config, args) {\n    const request = args.request;\n    if (request?.headers?.[\"smithy-protocol\"] === \"rpc-v2-cbor\") {\n        setFeature(context, \"PROTOCOL_RPC_V2_CBOR\", \"M\");\n    }\n    if (typeof config.retryStrategy === \"function\") {\n        const retryStrategy = await config.retryStrategy();\n        if (typeof retryStrategy.acquireInitialRetryToken === \"function\") {\n            if (retryStrategy.constructor?.name?.includes(\"Adaptive\")) {\n                setFeature(context, \"RETRY_MODE_ADAPTIVE\", \"F\");\n            }\n            else {\n                setFeature(context, \"RETRY_MODE_STANDARD\", \"E\");\n            }\n        }\n        else {\n            setFeature(context, \"RETRY_MODE_LEGACY\", \"D\");\n        }\n    }\n    if (typeof config.accountIdEndpointMode === \"function\") {\n        const endpointV2 = context.endpointV2;\n        if (String(endpointV2?.url?.hostname).match(ACCOUNT_ID_ENDPOINT_REGEX)) {\n            setFeature(context, \"ACCOUNT_ID_ENDPOINT\", \"O\");\n        }\n        switch (await config.accountIdEndpointMode?.()) {\n            case \"disabled\":\n                setFeature(context, \"ACCOUNT_ID_MODE_DISABLED\", \"Q\");\n                break;\n            case \"preferred\":\n                setFeature(context, \"ACCOUNT_ID_MODE_PREFERRED\", \"P\");\n                break;\n            case \"required\":\n                setFeature(context, \"ACCOUNT_ID_MODE_REQUIRED\", \"R\");\n                break;\n        }\n    }\n    const identity = context.__smithy_context?.selectedHttpAuthScheme?.identity;\n    if (identity?.$source) {\n        const credentials = identity;\n        if (credentials.accountId) {\n            setFeature(context, \"RESOLVED_ACCOUNT_ID\", \"T\");\n        }\n        for (const [key, value] of Object.entries(credentials.$source ?? {})) {\n            setFeature(context, key, value);\n        }\n    }\n}\n", "export const USER_AGENT = \"user-agent\";\nexport const X_AMZ_USER_AGENT = \"x-amz-user-agent\";\nexport const SPACE = \" \";\nexport const UA_NAME_SEPARATOR = \"/\";\nexport const UA_NAME_ESCAPE_REGEX = /[^\\!\\$\\%\\&\\'\\*\\+\\-\\.\\^\\_\\`\\|\\~\\d\\w]/g;\nexport const UA_VALUE_ESCAPE_REGEX = /[^\\!\\$\\%\\&\\'\\*\\+\\-\\.\\^\\_\\`\\|\\~\\d\\w\\#]/g;\nexport const UA_ESCAPE_CHAR = \"-\";\n", "const BYTE_LIMIT = 1024;\nexport function encodeFeatures(features) {\n    let buffer = \"\";\n    for (const key in features) {\n        const val = features[key];\n        if (buffer.length + val.length + 1 <= BYTE_LIMIT) {\n            if (buffer.length) {\n                buffer += \",\" + val;\n            }\n            else {\n                buffer += val;\n            }\n            continue;\n        }\n        break;\n    }\n    return buffer;\n}\n", "import { getUserAgentPrefix } from \"@aws-sdk/util-endpoints\";\nimport { HttpRequest } from \"@smithy/protocol-http\";\nimport { checkFeatures } from \"./check-features\";\nimport { SPACE, UA_ESCAPE_CHAR, UA_NAME_ESCAPE_REGEX, UA_NAME_SEPARATOR, UA_VALUE_ESCAPE_REGEX, USER_AGENT, X_AMZ_USER_AGENT, } from \"./constants\";\nimport { encodeFeatures } from \"./encode-features\";\nexport const userAgentMiddleware = (options) => (next, context) => async (args) => {\n    const { request } = args;\n    if (!HttpRequest.isInstance(request)) {\n        return next(args);\n    }\n    const { headers } = request;\n    const userAgent = context?.userAgent?.map(escapeUserAgent) || [];\n    const defaultUserAgent = (await options.defaultUserAgentProvider()).map(escapeUserAgent);\n    await checkFeatures(context, options, args);\n    const awsContext = context;\n    defaultUserAgent.push(`m/${encodeFeatures(Object.assign({}, context.__smithy_context?.features, awsContext.__aws_sdk_context?.features))}`);\n    const customUserAgent = options?.customUserAgent?.map(escapeUserAgent) || [];\n    const appId = await options.userAgentAppId();\n    if (appId) {\n        defaultUserAgent.push(escapeUserAgent([`app/${appId}`]));\n    }\n    const prefix = getUserAgentPrefix();\n    const sdkUserAgentValue = (prefix ? [prefix] : [])\n        .concat([...defaultUserAgent, ...userAgent, ...customUserAgent])\n        .join(SPACE);\n    const normalUAValue = [\n        ...defaultUserAgent.filter((section) => section.startsWith(\"aws-sdk-\")),\n        ...customUserAgent,\n    ].join(SPACE);\n    if (options.runtime !== \"browser\") {\n        if (normalUAValue) {\n            headers[X_AMZ_USER_AGENT] = headers[X_AMZ_USER_AGENT]\n                ? `${headers[USER_AGENT]} ${normalUAValue}`\n                : normalUAValue;\n        }\n        headers[USER_AGENT] = sdkUserAgentValue;\n    }\n    else {\n        headers[X_AMZ_USER_AGENT] = sdkUserAgentValue;\n    }\n    return next({\n        ...args,\n        request,\n    });\n};\nconst escapeUserAgent = (userAgentPair) => {\n    const name = userAgentPair[0]\n        .split(UA_NAME_SEPARATOR)\n        .map((part) => part.replace(UA_NAME_ESCAPE_REGEX, UA_ESCAPE_CHAR))\n        .join(UA_NAME_SEPARATOR);\n    const version = userAgentPair[1]?.replace(UA_VALUE_ESCAPE_REGEX, UA_ESCAPE_CHAR);\n    const prefixSeparatorIndex = name.indexOf(UA_NAME_SEPARATOR);\n    const prefix = name.substring(0, prefixSeparatorIndex);\n    let uaName = name.substring(prefixSeparatorIndex + 1);\n    if (prefix === \"api\") {\n        uaName = uaName.toLowerCase();\n    }\n    return [prefix, uaName, version]\n        .filter((item) => item && item.length > 0)\n        .reduce((acc, item, index) => {\n        switch (index) {\n            case 0:\n                return item;\n            case 1:\n                return `${acc}/${item}`;\n            default:\n                return `${acc}#${item}`;\n        }\n    }, \"\");\n};\nexport const getUserAgentMiddlewareOptions = {\n    name: \"getUserAgentMiddleware\",\n    step: \"build\",\n    priority: \"low\",\n    tags: [\"SET_USER_AGENT\", \"USER_AGENT\"],\n    override: true,\n};\nexport const getUserAgentPlugin = (config) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(userAgentMiddleware(config), getUserAgentMiddlewareOptions);\n    },\n});\n", "export * from \"./configurations\";\nexport * from \"./user-agent-middleware\";\n", "export const isFipsRegion = (region) => typeof region === \"string\" && (region.startsWith(\"fips-\") || region.endsWith(\"-fips\"));\n", "import { isFipsRegion } from \"./isFipsRegion\";\nexport const getRealRegion = (region) => isFipsRegion(region)\n    ? [\"fips-aws-global\", \"aws-fips\"].includes(region)\n        ? \"us-east-1\"\n        : region.replace(/fips-(dkr-|prod-)?|-fips/, \"\")\n    : region;\n", "import { getRealRegion } from \"./getRealRegion\";\nimport { isFipsRegion } from \"./isFipsRegion\";\nexport const resolveRegionConfig = (input) => {\n    const { region, useFipsEndpoint } = input;\n    if (!region) {\n        throw new Error(\"Region is missing\");\n    }\n    return Object.assign(input, {\n        region: async () => {\n            if (typeof region === \"string\") {\n                return getRealRegion(region);\n            }\n            const providedRegion = await region();\n            return getRealRegion(providedRegion);\n        },\n        useFipsEndpoint: async () => {\n            const providedRegion = typeof region === \"string\" ? region : await region();\n            if (isFipsRegion(providedRegion)) {\n                return true;\n            }\n            return typeof useFipsEndpoint !== \"function\" ? Promise.resolve(!!useFipsEndpoint) : useFipsEndpoint();\n        },\n    });\n};\n", "export const booleanSelector = (obj, key, type) => {\n    if (!(key in obj))\n        return undefined;\n    if (obj[key] === \"true\")\n        return true;\n    if (obj[key] === \"false\")\n        return false;\n    throw new Error(`Cannot load ${type} \"${key}\". Expected \"true\" or \"false\", got ${obj[key]}.`);\n};\n", "export const numberSelector = (obj, key, type) => {\n    if (!(key in obj))\n        return undefined;\n    const numberValue = parseInt(obj[key], 10);\n    if (Number.isNaN(numberValue)) {\n        throw new TypeError(`Cannot load ${type} '${key}'. Expected number, got '${obj[key]}'.`);\n    }\n    return numberValue;\n};\n", "export var SelectorType;\n(function (SelectorType) {\n    SelectorType[\"ENV\"] = \"env\";\n    SelectorType[\"CONFIG\"] = \"shared config entry\";\n})(SelectorType || (SelectorType = {}));\n", "export * from \"./booleanSelector\";\nexport * from \"./numberSelector\";\nexport * from \"./types\";\n", "import { booleanSelector, SelectorType } from \"@smithy/util-config-provider\";\nexport const ENV_USE_DUALSTACK_ENDPOINT = \"AWS_USE_DUALSTACK_ENDPOINT\";\nexport const CONFIG_USE_DUALSTACK_ENDPOINT = \"use_dualstack_endpoint\";\nexport const DEFAULT_USE_DUALSTACK_ENDPOINT = false;\nexport const NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => booleanSelector(env, ENV_USE_DUALSTACK_ENDPOINT, SelectorType.ENV),\n    configFileSelector: (profile) => booleanSelector(profile, CONFIG_USE_DUALSTACK_ENDPOINT, SelectorType.CONFIG),\n    default: false,\n};\n", "import { booleanSelector, SelectorType } from \"@smithy/util-config-provider\";\nexport const ENV_USE_FIPS_ENDPOINT = \"AWS_USE_FIPS_ENDPOINT\";\nexport const CONFIG_USE_FIPS_ENDPOINT = \"use_fips_endpoint\";\nexport const DEFAULT_USE_FIPS_ENDPOINT = false;\nexport const NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => booleanSelector(env, ENV_USE_FIPS_ENDPOINT, SelectorType.ENV),\n    configFileSelector: (profile) => booleanSelector(profile, CONFIG_USE_FIPS_ENDPOINT, SelectorType.CONFIG),\n    default: false,\n};\n", "import { normalizeProvider } from \"@smithy/util-middleware\";\nexport const resolveCustomEndpointsConfig = (input) => {\n    const { tls, endpoint, urlParser, useDualstackEndpoint } = input;\n    return Object.assign(input, {\n        tls: tls ?? true,\n        endpoint: normalizeProvider(typeof endpoint === \"string\" ? urlParser(endpoint) : endpoint),\n        isCustomEndpoint: true,\n        useDualstackEndpoint: normalizeProvider(useDualstackEndpoint ?? false),\n    });\n};\n", "export const getEndpointFromRegion = async (input) => {\n    const { tls = true } = input;\n    const region = await input.region();\n    const dnsHostRegex = new RegExp(/^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/);\n    if (!dnsHostRegex.test(region)) {\n        throw new Error(\"Invalid region in client config\");\n    }\n    const useDualstackEndpoint = await input.useDualstackEndpoint();\n    const useFipsEndpoint = await input.useFipsEndpoint();\n    const { hostname } = (await input.regionInfoProvider(region, { useDualstackEndpoint, useFipsEndpoint })) ?? {};\n    if (!hostname) {\n        throw new Error(\"Cannot resolve hostname from client config\");\n    }\n    return input.urlParser(`${tls ? \"https:\" : \"http:\"}//${hostname}`);\n};\n", "import { normalizeProvider } from \"@smithy/util-middleware\";\nimport { getEndpointFromRegion } from \"./utils/getEndpointFromRegion\";\nexport const resolveEndpointsConfig = (input) => {\n    const useDualstackEndpoint = normalizeProvider(input.useDualstackEndpoint ?? false);\n    const { endpoint, useFipsEndpoint, urlParser, tls } = input;\n    return Object.assign(input, {\n        tls: tls ?? true,\n        endpoint: endpoint\n            ? normalizeProvider(typeof endpoint === \"string\" ? urlParser(endpoint) : endpoint)\n            : () => getEndpointFromRegion({ ...input, useDualstackEndpoint, useFipsEndpoint }),\n        isCustomEndpoint: !!endpoint,\n        useDualstackEndpoint,\n    });\n};\n", "export * from \"./NodeUseDualstackEndpointConfigOptions\";\nexport * from \"./NodeUseFipsEndpointConfigOptions\";\nexport * from \"./resolveCustomEndpointsConfig\";\nexport * from \"./resolveEndpointsConfig\";\n", "export const REGION_ENV_NAME = \"AWS_REGION\";\nexport const REGION_INI_NAME = \"region\";\nexport const NODE_REGION_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[REGION_ENV_NAME],\n    configFileSelector: (profile) => profile[REGION_INI_NAME],\n    default: () => {\n        throw new Error(\"Region is missing\");\n    },\n};\nexport const NODE_REGION_CONFIG_FILE_OPTIONS = {\n    preferredFile: \"credentials\",\n};\n", "export * from \"./config\";\nexport * from \"./resolveRegionConfig\";\n", "export {};\n", "export {};\n", "export const getHostnameFromVariants = (variants = [], { useFipsEndpoint, useDualstackEndpoint }) => variants.find(({ tags }) => useFipsEndpoint === tags.includes(\"fips\") && useDualstackEndpoint === tags.includes(\"dualstack\"))?.hostname;\n", "export const getResolvedHostname = (resolvedRegion, { regionHostname, partitionHostname }) => regionHostname\n    ? regionHostname\n    : partitionHostname\n        ? partitionHostname.replace(\"{region}\", resolvedRegion)\n        : undefined;\n", "export const getResolvedPartition = (region, { partitionHash }) => Object.keys(partitionHash || {}).find((key) => partitionHash[key].regions.includes(region)) ?? \"aws\";\n", "export const getResolvedSigningRegion = (hostname, { signingRegion, regionRegex, useFipsEndpoint }) => {\n    if (signingRegion) {\n        return signingRegion;\n    }\n    else if (useFipsEndpoint) {\n        const regionRegexJs = regionRegex.replace(\"\\\\\\\\\", \"\\\\\").replace(/^\\^/g, \"\\\\.\").replace(/\\$$/g, \"\\\\.\");\n        const regionRegexmatchArray = hostname.match(regionRegexJs);\n        if (regionRegexmatchArray) {\n            return regionRegexmatchArray[0].slice(1, -1);\n        }\n    }\n};\n", "import { getHostnameFromVariants } from \"./getHostnameFromVariants\";\nimport { getResolvedHostname } from \"./getResolvedHostname\";\nimport { getResolvedPartition } from \"./getResolvedPartition\";\nimport { getResolvedSigningRegion } from \"./getResolvedSigningRegion\";\nexport const getRegionInfo = (region, { useFipsEndpoint = false, useDualstackEndpoint = false, signingService, regionHash, partitionHash, }) => {\n    const partition = getResolvedPartition(region, { partitionHash });\n    const resolvedRegion = region in regionHash ? region : partitionHash[partition]?.endpoint ?? region;\n    const hostnameOptions = { useFipsEndpoint, useDualstackEndpoint };\n    const regionHostname = getHostnameFromVariants(regionHash[resolvedRegion]?.variants, hostnameOptions);\n    const partitionHostname = getHostnameFromVariants(partitionHash[partition]?.variants, hostnameOptions);\n    const hostname = getResolvedHostname(resolvedRegion, { regionHostname, partitionHostname });\n    if (hostname === undefined) {\n        throw new Error(`Endpoint resolution failed for: ${{ resolvedRegion, useFipsEndpoint, useDualstackEndpoint }}`);\n    }\n    const signingRegion = getResolvedSigningRegion(hostname, {\n        signingRegion: regionHash[resolvedRegion]?.signingRegion,\n        regionRegex: partitionHash[partition].regionRegex,\n        useFipsEndpoint,\n    });\n    return {\n        partition,\n        signingService,\n        hostname,\n        ...(signingRegion && { signingRegion }),\n        ...(regionHash[resolvedRegion]?.signingService && {\n            signingService: regionHash[resolvedRegion].signingService,\n        }),\n    };\n};\n", "export * from \"./PartitionHash\";\nexport * from \"./RegionHash\";\nexport * from \"./getRegionInfo\";\n", "export * from \"./endpointsConfig\";\nexport * from \"./regionConfig\";\nexport * from \"./regionInfo\";\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nconst CONTENT_LENGTH_HEADER = \"content-length\";\nexport function contentLengthMiddleware(bodyLengthChecker) {\n    return (next) => async (args) => {\n        const request = args.request;\n        if (HttpRequest.isInstance(request)) {\n            const { body, headers } = request;\n            if (body &&\n                Object.keys(headers)\n                    .map((str) => str.toLowerCase())\n                    .indexOf(CONTENT_LENGTH_HEADER) === -1) {\n                try {\n                    const length = bodyLengthChecker(body);\n                    request.headers = {\n                        ...request.headers,\n                        [CONTENT_LENGTH_HEADER]: String(length),\n                    };\n                }\n                catch (error) {\n                }\n            }\n        }\n        return next({\n            ...args,\n            request,\n        });\n    };\n}\nexport const contentLengthMiddlewareOptions = {\n    step: \"build\",\n    tags: [\"SET_CONTENT_LENGTH\", \"CONTENT_LENGTH\"],\n    name: \"contentLengthMiddleware\",\n    override: true,\n};\nexport const getContentLengthPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(contentLengthMiddleware(options.bodyLengthChecker), contentLengthMiddlewareOptions);\n    },\n});\n", "export const resolveParamsForS3 = async (endpointParams) => {\n    const bucket = endpointParams?.Bucket || \"\";\n    if (typeof endpointParams.Bucket === \"string\") {\n        endpointParams.Bucket = bucket.replace(/#/g, encodeURIComponent(\"#\")).replace(/\\?/g, encodeURIComponent(\"?\"));\n    }\n    if (isArnBucketName(bucket)) {\n        if (endpointParams.ForcePathStyle === true) {\n            throw new Error(\"Path-style addressing cannot be used with ARN buckets\");\n        }\n    }\n    else if (!isDnsCompatibleBucketName(bucket) ||\n        (bucket.indexOf(\".\") !== -1 && !String(endpointParams.Endpoint).startsWith(\"http:\")) ||\n        bucket.toLowerCase() !== bucket ||\n        bucket.length < 3) {\n        endpointParams.ForcePathStyle = true;\n    }\n    if (endpointParams.DisableMultiRegionAccessPoints) {\n        endpointParams.disableMultiRegionAccessPoints = true;\n        endpointParams.DisableMRAP = true;\n    }\n    return endpointParams;\n};\nconst DOMAIN_PATTERN = /^[a-z0-9][a-z0-9\\.\\-]{1,61}[a-z0-9]$/;\nconst IP_ADDRESS_PATTERN = /(\\d+\\.){3}\\d+/;\nconst DOTS_PATTERN = /\\.\\./;\nexport const DOT_PATTERN = /\\./;\nexport const S3_HOSTNAME_PATTERN = /^(.+\\.)?s3(-fips)?(\\.dualstack)?[.-]([a-z0-9-]+)\\./;\nexport const isDnsCompatibleBucketName = (bucketName) => DOMAIN_PATTERN.test(bucketName) && !IP_ADDRESS_PATTERN.test(bucketName) && !DOTS_PATTERN.test(bucketName);\nexport const isArnBucketName = (bucketName) => {\n    const [arn, partition, service, , , bucket] = bucketName.split(\":\");\n    const isArn = arn === \"arn\" && bucketName.split(\":\").length >= 6;\n    const isValidArn = Boolean(isArn && partition && service && bucket);\n    if (isArn && !isValidArn) {\n        throw new Error(`Invalid ARN: ${bucketName} was an invalid ARN.`);\n    }\n    return isValidArn;\n};\n", "export * from \"./s3\";\n", "export const createConfigValueProvider = (config<PERSON>ey, canonicalEndpointParamKey, config) => {\n    const configProvider = async () => {\n        const configValue = config[configKey] ?? config[canonicalEndpointParamKey];\n        if (typeof configValue === \"function\") {\n            return configValue();\n        }\n        return configValue;\n    };\n    if (configKey === \"credentialScope\" || canonicalEndpointParamKey === \"CredentialScope\") {\n        return async () => {\n            const credentials = typeof config.credentials === \"function\" ? await config.credentials() : config.credentials;\n            const configValue = credentials?.credentialScope ?? credentials?.CredentialScope;\n            return configValue;\n        };\n    }\n    if (configKey === \"accountId\" || canonicalEndpointParamKey === \"AccountId\") {\n        return async () => {\n            const credentials = typeof config.credentials === \"function\" ? await config.credentials() : config.credentials;\n            const configValue = credentials?.accountId ?? credentials?.AccountId;\n            return configValue;\n        };\n    }\n    if (configKey === \"endpoint\" || canonicalEndpointParamKey === \"endpoint\") {\n        return async () => {\n            const endpoint = await configProvider();\n            if (endpoint && typeof endpoint === \"object\") {\n                if (\"url\" in endpoint) {\n                    return endpoint.url.href;\n                }\n                if (\"hostname\" in endpoint) {\n                    const { protocol, hostname, port, path } = endpoint;\n                    return `${protocol}//${hostname}${port ? \":\" + port : \"\"}${path}`;\n                }\n            }\n            return endpoint;\n        };\n    }\n    return configProvider;\n};\n", "export const getEndpointFromConfig = async (serviceId) => undefined;\n", "export function parseQueryString(querystring) {\n    const query = {};\n    querystring = querystring.replace(/^\\?/, \"\");\n    if (querystring) {\n        for (const pair of querystring.split(\"&\")) {\n            let [key, value = null] = pair.split(\"=\");\n            key = decodeURIComponent(key);\n            if (value) {\n                value = decodeURIComponent(value);\n            }\n            if (!(key in query)) {\n                query[key] = value;\n            }\n            else if (Array.isArray(query[key])) {\n                query[key].push(value);\n            }\n            else {\n                query[key] = [query[key], value];\n            }\n        }\n    }\n    return query;\n}\n", "import { parseQueryString } from \"@smithy/querystring-parser\";\nexport const parseUrl = (url) => {\n    if (typeof url === \"string\") {\n        return parseUrl(new URL(url));\n    }\n    const { hostname, pathname, port, protocol, search } = url;\n    let query;\n    if (search) {\n        query = parseQueryString(search);\n    }\n    return {\n        hostname,\n        port: port ? parseInt(port) : undefined,\n        protocol,\n        path: pathname,\n        query,\n    };\n};\n", "import { parseUrl } from \"@smithy/url-parser\";\nexport const toEndpointV1 = (endpoint) => {\n    if (typeof endpoint === \"object\") {\n        if (\"url\" in endpoint) {\n            return parseUrl(endpoint.url);\n        }\n        return endpoint;\n    }\n    return parseUrl(endpoint);\n};\n", "import { resolveParamsForS3 } from \"../service-customizations\";\nimport { createConfigValueProvider } from \"./createConfigValueProvider\";\nimport { getEndpointFromConfig } from \"./getEndpointFromConfig\";\nimport { toEndpointV1 } from \"./toEndpointV1\";\nexport const getEndpointFromInstructions = async (commandInput, instructionsSupplier, clientConfig, context) => {\n    if (!clientConfig.endpoint) {\n        let endpointFromConfig;\n        if (clientConfig.serviceConfiguredEndpoint) {\n            endpointFromConfig = await clientConfig.serviceConfiguredEndpoint();\n        }\n        else {\n            endpointFromConfig = await getEndpointFromConfig(clientConfig.serviceId);\n        }\n        if (endpointFromConfig) {\n            clientConfig.endpoint = () => Promise.resolve(toEndpointV1(endpointFromConfig));\n        }\n    }\n    const endpointParams = await resolveParams(commandInput, instructionsSupplier, clientConfig);\n    if (typeof clientConfig.endpointProvider !== \"function\") {\n        throw new Error(\"config.endpointProvider is not set.\");\n    }\n    const endpoint = clientConfig.endpointProvider(endpointParams, context);\n    return endpoint;\n};\nexport const resolveParams = async (commandInput, instructionsSupplier, clientConfig) => {\n    const endpointParams = {};\n    const instructions = instructionsSupplier?.getEndpointParameterInstructions?.() || {};\n    for (const [name, instruction] of Object.entries(instructions)) {\n        switch (instruction.type) {\n            case \"staticContextParams\":\n                endpointParams[name] = instruction.value;\n                break;\n            case \"contextParams\":\n                endpointParams[name] = commandInput[instruction.name];\n                break;\n            case \"clientContextParams\":\n            case \"builtInParams\":\n                endpointParams[name] = await createConfigValueProvider(instruction.name, name, clientConfig)();\n                break;\n            case \"operationContextParams\":\n                endpointParams[name] = instruction.get(commandInput);\n                break;\n            default:\n                throw new Error(\"Unrecognized endpoint parameter instruction: \" + JSON.stringify(instruction));\n        }\n    }\n    if (Object.keys(instructions).length === 0) {\n        Object.assign(endpointParams, clientConfig);\n    }\n    if (String(clientConfig.serviceId).toLowerCase() === \"s3\") {\n        await resolveParamsForS3(endpointParams);\n    }\n    return endpointParams;\n};\n", "import { setFeature } from \"@smithy/core\";\nimport { getSmithyContext } from \"@smithy/util-middleware\";\nimport { getEndpointFromInstructions } from \"./adaptors/getEndpointFromInstructions\";\nexport const endpointMiddleware = ({ config, instructions, }) => {\n    return (next, context) => async (args) => {\n        if (config.endpoint) {\n            setFeature(context, \"ENDPOINT_OVERRIDE\", \"N\");\n        }\n        const endpoint = await getEndpointFromInstructions(args.input, {\n            getEndpointParameterInstructions() {\n                return instructions;\n            },\n        }, { ...config }, context);\n        context.endpointV2 = endpoint;\n        context.authSchemes = endpoint.properties?.authSchemes;\n        const authScheme = context.authSchemes?.[0];\n        if (authScheme) {\n            context[\"signing_region\"] = authScheme.signingRegion;\n            context[\"signing_service\"] = authScheme.signingName;\n            const smithyContext = getSmithyContext(context);\n            const httpAuthOption = smithyContext?.selectedHttpAuthScheme?.httpAuthOption;\n            if (httpAuthOption) {\n                httpAuthOption.signingProperties = Object.assign(httpAuthOption.signingProperties || {}, {\n                    signing_region: authScheme.signingRegion,\n                    signingRegion: authScheme.signingRegion,\n                    signing_service: authScheme.signingName,\n                    signingName: authScheme.signingName,\n                    signingRegionSet: authScheme.signingRegionSet,\n                }, authScheme.properties);\n            }\n        }\n        return next({\n            ...args,\n        });\n    };\n};\n", "import { serializerMiddlewareOption } from \"@smithy/middleware-serde\";\nimport { endpointMiddleware } from \"./endpointMiddleware\";\nexport const endpointMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"ENDPOINT_PARAMETERS\", \"ENDPOINT_V2\", \"ENDPOINT\"],\n    name: \"endpointV2Middleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: serializerMiddlewareOption.name,\n};\nexport const getEndpointPlugin = (config, instructions) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(endpointMiddleware({\n            config,\n            instructions,\n        }), endpointMiddlewareOptions);\n    },\n});\n", "import { normalizeProvider } from \"@smithy/util-middleware\";\nimport { getEndpointFromConfig } from \"./adaptors/getEndpointFromConfig\";\nimport { toEndpointV1 } from \"./adaptors/toEndpointV1\";\nexport const resolveEndpointConfig = (input) => {\n    const tls = input.tls ?? true;\n    const { endpoint, useDualstackEndpoint, useFipsEndpoint } = input;\n    const customEndpointProvider = endpoint != null ? async () => toEndpointV1(await normalizeProvider(endpoint)()) : undefined;\n    const isCustomEndpoint = !!endpoint;\n    const resolvedConfig = Object.assign(input, {\n        endpoint: customEndpointProvider,\n        tls,\n        isCustomEndpoint,\n        useDualstackEndpoint: normalizeProvider(useDualstackEndpoint ?? false),\n        useFipsEndpoint: normalizeProvider(useFipsEndpoint ?? false),\n    });\n    let configuredEndpointPromise = undefined;\n    resolvedConfig.serviceConfiguredEndpoint = async () => {\n        if (input.serviceId && !configuredEndpointPromise) {\n            configuredEndpointPromise = getEndpointFromConfig(input.serviceId);\n        }\n        return configuredEndpointPromise;\n    };\n    return resolvedConfig;\n};\n", "export * from \"./getEndpointFromInstructions\";\nexport * from \"./toEndpointV1\";\n", "export {};\n", "export * from \"./adaptors\";\nexport * from \"./endpointMiddleware\";\nexport * from \"./getEndpointPlugin\";\nexport * from \"./resolveEndpointConfig\";\nexport * from \"./types\";\n", "export var RETRY_MODES;\n(function (RETRY_MODES) {\n    RETRY_MODES[\"STANDARD\"] = \"standard\";\n    RETRY_MODES[\"ADAPTIVE\"] = \"adaptive\";\n})(RETRY_MODES || (RETRY_MODES = {}));\nexport const DEFAULT_MAX_ATTEMPTS = 3;\nexport const DEFAULT_RETRY_MODE = RETRY_MODES.STANDARD;\n", "export const CLOCK_SKEW_ERROR_CODES = [\n    \"AuthFailure\",\n    \"InvalidSignatureException\",\n    \"RequestExpired\",\n    \"RequestInTheFuture\",\n    \"RequestTimeTooSkewed\",\n    \"SignatureDoesNotMatch\",\n];\nexport const THROTTLING_ERROR_CODES = [\n    \"BandwidthLimitExceeded\",\n    \"EC2ThrottledException\",\n    \"LimitExceededException\",\n    \"PriorRequestNotComplete\",\n    \"ProvisionedThroughputExceededException\",\n    \"RequestLimitExceeded\",\n    \"RequestThrottled\",\n    \"RequestThrottledException\",\n    \"SlowDown\",\n    \"ThrottledException\",\n    \"Throttling\",\n    \"ThrottlingException\",\n    \"TooManyRequestsException\",\n    \"TransactionInProgressException\",\n];\nexport const TRANSIENT_ERROR_CODES = [\"TimeoutError\", \"RequestTimeout\", \"RequestTimeoutException\"];\nexport const TRANSIENT_ERROR_STATUS_CODES = [500, 502, 503, 504];\nexport const NODEJS_TIMEOUT_ERROR_CODES = [\"ECONNRESET\", \"ECONNREFUSED\", \"EPIPE\", \"ETIMEDOUT\"];\n", "import { CLOCK_SKEW_ERROR_CODES, NODEJS_TIMEOUT_ERROR_CODES, THROTTLING_ERROR_CODES, TRANSIENT_ERROR_CODES, TRANSIENT_ERROR_STATUS_CODES, } from \"./constants\";\nexport const isRetryableByTrait = (error) => error.$retryable !== undefined;\nexport const isClockSkewError = (error) => CLOCK_SKEW_ERROR_CODES.includes(error.name);\nexport const isClockSkewCorrectedError = (error) => error.$metadata?.clockSkewCorrected;\nexport const isBrowserNetworkError = (error) => {\n    const errorMessages = new Set([\n        \"Failed to fetch\",\n        \"NetworkError when attempting to fetch resource\",\n        \"The Internet connection appears to be offline\",\n        \"Load failed\",\n        \"Network request failed\",\n    ]);\n    const isValid = error && error instanceof TypeError;\n    if (!isValid) {\n        return false;\n    }\n    return errorMessages.has(error.message);\n};\nexport const isThrottlingError = (error) => error.$metadata?.httpStatusCode === 429 ||\n    THROTTLING_ERROR_CODES.includes(error.name) ||\n    error.$retryable?.throttling == true;\nexport const isTransientError = (error, depth = 0) => isClockSkewCorrectedError(error) ||\n    TRANSIENT_ERROR_CODES.includes(error.name) ||\n    NODEJS_TIMEOUT_ERROR_CODES.includes(error?.code || \"\") ||\n    TRANSIENT_ERROR_STATUS_CODES.includes(error.$metadata?.httpStatusCode || 0) ||\n    isBrowserNetworkError(error) ||\n    (error.cause !== undefined && depth <= 10 && isTransientError(error.cause, depth + 1));\nexport const isServerError = (error) => {\n    if (error.$metadata?.httpStatusCode !== undefined) {\n        const statusCode = error.$metadata.httpStatusCode;\n        if (500 <= statusCode && statusCode <= 599 && !isTransientError(error)) {\n            return true;\n        }\n        return false;\n    }\n    return false;\n};\n", "import { isThrottlingError } from \"@smithy/service-error-classification\";\nexport class DefaultRateLimiter {\n    constructor(options) {\n        this.currentCapacity = 0;\n        this.enabled = false;\n        this.lastMaxRate = 0;\n        this.measuredTxRate = 0;\n        this.requestCount = 0;\n        this.lastTimestamp = 0;\n        this.timeWindow = 0;\n        this.beta = options?.beta ?? 0.7;\n        this.minCapacity = options?.minCapacity ?? 1;\n        this.minFillRate = options?.minFillRate ?? 0.5;\n        this.scaleConstant = options?.scaleConstant ?? 0.4;\n        this.smooth = options?.smooth ?? 0.8;\n        const currentTimeInSeconds = this.getCurrentTimeInSeconds();\n        this.lastThrottleTime = currentTimeInSeconds;\n        this.lastTxRateBucket = Math.floor(this.getCurrentTimeInSeconds());\n        this.fillRate = this.minFillRate;\n        this.maxCapacity = this.minCapacity;\n    }\n    getCurrentTimeInSeconds() {\n        return Date.now() / 1000;\n    }\n    async getSendToken() {\n        return this.acquireTokenBucket(1);\n    }\n    async acquireTokenBucket(amount) {\n        if (!this.enabled) {\n            return;\n        }\n        this.refillTokenBucket();\n        if (amount > this.currentCapacity) {\n            const delay = ((amount - this.currentCapacity) / this.fillRate) * 1000;\n            await new Promise((resolve) => DefaultRateLimiter.setTimeoutFn(resolve, delay));\n        }\n        this.currentCapacity = this.currentCapacity - amount;\n    }\n    refillTokenBucket() {\n        const timestamp = this.getCurrentTimeInSeconds();\n        if (!this.lastTimestamp) {\n            this.lastTimestamp = timestamp;\n            return;\n        }\n        const fillAmount = (timestamp - this.lastTimestamp) * this.fillRate;\n        this.currentCapacity = Math.min(this.maxCapacity, this.currentCapacity + fillAmount);\n        this.lastTimestamp = timestamp;\n    }\n    updateClientSendingRate(response) {\n        let calculatedRate;\n        this.updateMeasuredRate();\n        if (isThrottlingError(response)) {\n            const rateToUse = !this.enabled ? this.measuredTxRate : Math.min(this.measuredTxRate, this.fillRate);\n            this.lastMaxRate = rateToUse;\n            this.calculateTimeWindow();\n            this.lastThrottleTime = this.getCurrentTimeInSeconds();\n            calculatedRate = this.cubicThrottle(rateToUse);\n            this.enableTokenBucket();\n        }\n        else {\n            this.calculateTimeWindow();\n            calculatedRate = this.cubicSuccess(this.getCurrentTimeInSeconds());\n        }\n        const newRate = Math.min(calculatedRate, 2 * this.measuredTxRate);\n        this.updateTokenBucketRate(newRate);\n    }\n    calculateTimeWindow() {\n        this.timeWindow = this.getPrecise(Math.pow((this.lastMaxRate * (1 - this.beta)) / this.scaleConstant, 1 / 3));\n    }\n    cubicThrottle(rateToUse) {\n        return this.getPrecise(rateToUse * this.beta);\n    }\n    cubicSuccess(timestamp) {\n        return this.getPrecise(this.scaleConstant * Math.pow(timestamp - this.lastThrottleTime - this.timeWindow, 3) + this.lastMaxRate);\n    }\n    enableTokenBucket() {\n        this.enabled = true;\n    }\n    updateTokenBucketRate(newRate) {\n        this.refillTokenBucket();\n        this.fillRate = Math.max(newRate, this.minFillRate);\n        this.maxCapacity = Math.max(newRate, this.minCapacity);\n        this.currentCapacity = Math.min(this.currentCapacity, this.maxCapacity);\n    }\n    updateMeasuredRate() {\n        const t = this.getCurrentTimeInSeconds();\n        const timeBucket = Math.floor(t * 2) / 2;\n        this.requestCount++;\n        if (timeBucket > this.lastTxRateBucket) {\n            const currentRate = this.requestCount / (timeBucket - this.lastTxRateBucket);\n            this.measuredTxRate = this.getPrecise(currentRate * this.smooth + this.measuredTxRate * (1 - this.smooth));\n            this.requestCount = 0;\n            this.lastTxRateBucket = timeBucket;\n        }\n    }\n    getPrecise(num) {\n        return parseFloat(num.toFixed(8));\n    }\n}\nDefaultRateLimiter.setTimeoutFn = setTimeout;\n", "export const DEFAULT_RETRY_DELAY_BASE = 100;\nexport const MAXIMUM_RETRY_DELAY = 20 * 1000;\nexport const THROTTLING_RETRY_DELAY_BASE = 500;\nexport const INITIAL_RETRY_TOKENS = 500;\nexport const RETRY_COST = 5;\nexport const TIMEOUT_RETRY_COST = 10;\nexport const NO_RETRY_INCREMENT = 1;\nexport const INVOCATION_ID_HEADER = \"amz-sdk-invocation-id\";\nexport const REQUEST_HEADER = \"amz-sdk-request\";\n", "import { DEFAULT_RETRY_DELAY_BASE, MAXIMUM_RETRY_DELAY } from \"./constants\";\nexport const getDefaultRetryBackoffStrategy = () => {\n    let delayBase = DEFAULT_RETRY_DELAY_BASE;\n    const computeNextBackoffDelay = (attempts) => {\n        return Math.floor(Math.min(MAXIMUM_RETRY_DELAY, Math.random() * 2 ** attempts * delayBase));\n    };\n    const setDelayBase = (delay) => {\n        delayBase = delay;\n    };\n    return {\n        computeNextBackoffDelay,\n        setDelayBase,\n    };\n};\n", "import { MAXIMUM_RETRY_DELAY } from \"./constants\";\nexport const createDefaultRetryToken = ({ retryDelay, retryCount, retryCost, }) => {\n    const getRetryCount = () => retryCount;\n    const getRetryDelay = () => Math.min(MAXIMUM_RETRY_DELAY, retryDelay);\n    const getRetryCost = () => retryCost;\n    return {\n        getRetryCount,\n        getRetryDelay,\n        getRetryCost,\n    };\n};\n", "import { DEFAULT_MAX_ATTEMPTS, RETRY_MODES } from \"./config\";\nimport { DEFAULT_RETRY_DELAY_BASE, INITIAL_RETRY_TOKENS, NO_RETRY_INCREMENT, RETRY_COST, THROTTLING_RETRY_DELAY_BASE, TIMEOUT_RETRY_COST, } from \"./constants\";\nimport { getDefaultRetryBackoffStrategy } from \"./defaultRetryBackoffStrategy\";\nimport { createDefaultRetryToken } from \"./defaultRetryToken\";\nexport class StandardRetryStrategy {\n    constructor(maxAttempts) {\n        this.maxAttempts = maxAttempts;\n        this.mode = RETRY_MODES.STANDARD;\n        this.capacity = INITIAL_RETRY_TOKENS;\n        this.retryBackoffStrategy = getDefaultRetryBackoffStrategy();\n        this.maxAttemptsProvider = typeof maxAttempts === \"function\" ? maxAttempts : async () => maxAttempts;\n    }\n    async acquireInitialRetryToken(retryTokenScope) {\n        return createDefaultRetryToken({\n            retryDelay: DEFAULT_RETRY_DELAY_BASE,\n            retryCount: 0,\n        });\n    }\n    async refreshRetryTokenForRetry(token, errorInfo) {\n        const maxAttempts = await this.getMaxAttempts();\n        if (this.shouldRetry(token, errorInfo, maxAttempts)) {\n            const errorType = errorInfo.errorType;\n            this.retryBackoffStrategy.setDelayBase(errorType === \"THROTTLING\" ? THROTTLING_RETRY_DELAY_BASE : DEFAULT_RETRY_DELAY_BASE);\n            const delayFromErrorType = this.retryBackoffStrategy.computeNextBackoffDelay(token.getRetryCount());\n            const retryDelay = errorInfo.retryAfterHint\n                ? Math.max(errorInfo.retryAfterHint.getTime() - Date.now() || 0, delayFromErrorType)\n                : delayFromErrorType;\n            const capacityCost = this.getCapacityCost(errorType);\n            this.capacity -= capacityCost;\n            return createDefaultRetryToken({\n                retryDelay,\n                retryCount: token.getRetryCount() + 1,\n                retryCost: capacityCost,\n            });\n        }\n        throw new Error(\"No retry token available\");\n    }\n    recordSuccess(token) {\n        this.capacity = Math.max(INITIAL_RETRY_TOKENS, this.capacity + (token.getRetryCost() ?? NO_RETRY_INCREMENT));\n    }\n    getCapacity() {\n        return this.capacity;\n    }\n    async getMaxAttempts() {\n        try {\n            return await this.maxAttemptsProvider();\n        }\n        catch (error) {\n            console.warn(`Max attempts provider could not resolve. Using default of ${DEFAULT_MAX_ATTEMPTS}`);\n            return DEFAULT_MAX_ATTEMPTS;\n        }\n    }\n    shouldRetry(tokenToRenew, errorInfo, maxAttempts) {\n        const attempts = tokenToRenew.getRetryCount() + 1;\n        return (attempts < maxAttempts &&\n            this.capacity >= this.getCapacityCost(errorInfo.errorType) &&\n            this.isRetryableError(errorInfo.errorType));\n    }\n    getCapacityCost(errorType) {\n        return errorType === \"TRANSIENT\" ? TIMEOUT_RETRY_COST : RETRY_COST;\n    }\n    isRetryableError(errorType) {\n        return errorType === \"THROTTLING\" || errorType === \"TRANSIENT\";\n    }\n}\n", "import { RETRY_MODES } from \"./config\";\nimport { DefaultRateLimiter } from \"./DefaultRateLimiter\";\nimport { StandardRetryStrategy } from \"./StandardRetryStrategy\";\nexport class AdaptiveRetryStrategy {\n    constructor(maxAttemptsProvider, options) {\n        this.maxAttemptsProvider = maxAttemptsProvider;\n        this.mode = RETRY_MODES.ADAPTIVE;\n        const { rateLimiter } = options ?? {};\n        this.rateLimiter = rateLimiter ?? new DefaultRateLimiter();\n        this.standardRetryStrategy = new StandardRetryStrategy(maxAttemptsProvider);\n    }\n    async acquireInitialRetryToken(retryTokenScope) {\n        await this.rateLimiter.getSendToken();\n        return this.standardRetryStrategy.acquireInitialRetryToken(retryTokenScope);\n    }\n    async refreshRetryTokenForRetry(tokenToRenew, errorInfo) {\n        this.rateLimiter.updateClientSendingRate(errorInfo);\n        return this.standardRetryStrategy.refreshRetryTokenForRetry(tokenToRenew, errorInfo);\n    }\n    recordSuccess(token) {\n        this.rateLimiter.updateClientSendingRate({});\n        this.standardRetryStrategy.recordSuccess(token);\n    }\n}\n", "import { DEFAULT_RETRY_DELAY_BASE } from \"./constants\";\nimport { StandardRetryStrategy } from \"./StandardRetryStrategy\";\nexport class ConfiguredRetryStrategy extends StandardRetryStrategy {\n    constructor(maxAttempts, computeNextBackoffDelay = DEFAULT_RETRY_DELAY_BASE) {\n        super(typeof maxAttempts === \"function\" ? maxAttempts : async () => maxAttempts);\n        if (typeof computeNextBackoffDelay === \"number\") {\n            this.computeNextBackoffDelay = () => computeNextBackoffDelay;\n        }\n        else {\n            this.computeNextBackoffDelay = computeNextBackoffDelay;\n        }\n    }\n    async refreshRetryTokenForRetry(tokenToRenew, errorInfo) {\n        const token = await super.refreshRetryTokenForRetry(tokenToRenew, errorInfo);\n        token.getRetryDelay = () => this.computeNextBackoffDelay(token.getRetryCount());\n        return token;\n    }\n}\n", "export {};\n", "export * from \"./AdaptiveRetryStrategy\";\nexport * from \"./ConfiguredRetryStrategy\";\nexport * from \"./DefaultRateLimiter\";\nexport * from \"./StandardRetryStrategy\";\nexport * from \"./config\";\nexport * from \"./constants\";\nexport * from \"./types\";\n", "import { normalizeProvider } from \"@smithy/util-middleware\";\nimport { AdaptiveRetryStrategy, DEFAULT_MAX_ATTEMPTS, DEFAULT_RETRY_MODE, RETRY_MODES, StandardRetryStrategy, } from \"@smithy/util-retry\";\nexport const ENV_MAX_ATTEMPTS = \"AWS_MAX_ATTEMPTS\";\nexport const CONFIG_MAX_ATTEMPTS = \"max_attempts\";\nexport const NODE_MAX_ATTEMPT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => {\n        const value = env[ENV_MAX_ATTEMPTS];\n        if (!value)\n            return undefined;\n        const maxAttempt = parseInt(value);\n        if (Number.isNaN(maxAttempt)) {\n            throw new Error(`Environment variable ${ENV_MAX_ATTEMPTS} mast be a number, got \"${value}\"`);\n        }\n        return maxAttempt;\n    },\n    configFileSelector: (profile) => {\n        const value = profile[CONFIG_MAX_ATTEMPTS];\n        if (!value)\n            return undefined;\n        const maxAttempt = parseInt(value);\n        if (Number.isNaN(maxAttempt)) {\n            throw new Error(`Shared config file entry ${CONFIG_MAX_ATTEMPTS} mast be a number, got \"${value}\"`);\n        }\n        return maxAttempt;\n    },\n    default: DEFAULT_MAX_ATTEMPTS,\n};\nexport const resolveRetryConfig = (input) => {\n    const { retryStrategy, retryMode: _retryMode, maxAttempts: _maxAttempts } = input;\n    const maxAttempts = normalizeProvider(_maxAttempts ?? DEFAULT_MAX_ATTEMPTS);\n    return Object.assign(input, {\n        maxAttempts,\n        retryStrategy: async () => {\n            if (retryStrategy) {\n                return retryStrategy;\n            }\n            const retryMode = await normalizeProvider(_retryMode)();\n            if (retryMode === RETRY_MODES.ADAPTIVE) {\n                return new AdaptiveRetryStrategy(maxAttempts);\n            }\n            return new StandardRetryStrategy(maxAttempts);\n        },\n    });\n};\nexport const ENV_RETRY_MODE = \"AWS_RETRY_MODE\";\nexport const CONFIG_RETRY_MODE = \"retry_mode\";\nexport const NODE_RETRY_MODE_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[ENV_RETRY_MODE],\n    configFileSelector: (profile) => profile[CONFIG_RETRY_MODE],\n    default: DEFAULT_RETRY_MODE,\n};\n", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js'; // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nlet _nodeId;\n\nlet _clockseq; // Previous uuid creation time\n\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || rng)();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || unsafeStringify(b);\n}\n\nexport default v1;", "import validate from './validate.js';\n\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nexport default parse;", "import { unsafeStringify } from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return unsafeStringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n/*\n * Convert an array of little-endian words to an array of bytes\n */\n\n\nfunction md5ToHexEncodedArray(input) {\n  const output = [];\n  const length32 = input.length * 32;\n  const hexTab = '0123456789abcdef';\n\n  for (let i = 0; i < length32; i += 8) {\n    const x = input[i >> 5] >>> i % 32 & 0xff;\n    const hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n\n  return output;\n}\n/**\n * Calculate output length with padding and bit length\n */\n\n\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\n\n\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  let a = 1732584193;\n  let b = -271733879;\n  let c = -1732584194;\n  let d = 271733878;\n\n  for (let i = 0; i < x.length; i += 16) {\n    const olda = a;\n    const oldb = b;\n    const oldc = c;\n    const oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n\n  return [a, b, c, d];\n}\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\n\n\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n\n  const length8 = input.length * 8;\n  const output = new Uint32Array(getOutputLength(length8));\n\n  for (let i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n\n  return output;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\n\n\nfunction safeAdd(x, y) {\n  const lsw = (x & 0xffff) + (y & 0xffff);\n  const msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\n\n\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\n\n\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\n\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\n\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\n\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\n\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\n\nexport default md5;", "import v35 from './v35.js';\nimport md5 from './md5.js';\nconst v3 = v35('v3', 0x30, md5);\nexport default v3;", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    const msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (let i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n\n    M[i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;", "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nconst v5 = v35('v5', 0x50, sha1);\nexport default v5;", "export default '00000000-0000-0000-0000-000000000000';", "import validate from './validate.js';\n\nfunction version(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.slice(14, 15), 16);\n}\n\nexport default version;", "export { default as v1 } from './v1.js';\nexport { default as v3 } from './v3.js';\nexport { default as v4 } from './v4.js';\nexport { default as v5 } from './v5.js';\nexport { default as NIL } from './nil.js';\nexport { default as version } from './version.js';\nexport { default as validate } from './validate.js';\nexport { default as stringify } from './stringify.js';\nexport { default as parse } from './parse.js';", "export const isStreamingPayload = (request) => request?.body instanceof ReadableStream;\n", "export const asSdkError = (error) => {\n    if (error instanceof Error)\n        return error;\n    if (error instanceof Object)\n        return Object.assign(new Error(), error);\n    if (typeof error === \"string\")\n        return new Error(error);\n    return new Error(`AWS SDK error wrapper for ${error}`);\n};\n", "import { HttpRequest, HttpResponse } from \"@smithy/protocol-http\";\nimport { isServerError, isThrottlingError, isTransientError } from \"@smithy/service-error-classification\";\nimport { NoOpLogger } from \"@smithy/smithy-client\";\nimport { INVOCATION_ID_HEADER, REQUEST_HEADER } from \"@smithy/util-retry\";\nimport { v4 } from \"uuid\";\nimport { isStreamingPayload } from \"./isStreamingPayload/isStreamingPayload\";\nimport { asSdkError } from \"./util\";\nexport const retryMiddleware = (options) => (next, context) => async (args) => {\n    let retryStrategy = await options.retryStrategy();\n    const maxAttempts = await options.maxAttempts();\n    if (isRetryStrategyV2(retryStrategy)) {\n        retryStrategy = retryStrategy;\n        let retryToken = await retryStrategy.acquireInitialRetryToken(context[\"partition_id\"]);\n        let lastError = new Error();\n        let attempts = 0;\n        let totalRetryDelay = 0;\n        const { request } = args;\n        const isRequest = HttpRequest.isInstance(request);\n        if (isRequest) {\n            request.headers[INVOCATION_ID_HEADER] = v4();\n        }\n        while (true) {\n            try {\n                if (isRequest) {\n                    request.headers[REQUEST_HEADER] = `attempt=${attempts + 1}; max=${maxAttempts}`;\n                }\n                const { response, output } = await next(args);\n                retryStrategy.recordSuccess(retryToken);\n                output.$metadata.attempts = attempts + 1;\n                output.$metadata.totalRetryDelay = totalRetryDelay;\n                return { response, output };\n            }\n            catch (e) {\n                const retryErrorInfo = getRetryErrorInfo(e);\n                lastError = asSdkError(e);\n                if (isRequest && isStreamingPayload(request)) {\n                    (context.logger instanceof NoOpLogger ? console : context.logger)?.warn(\"An error was encountered in a non-retryable streaming request.\");\n                    throw lastError;\n                }\n                try {\n                    retryToken = await retryStrategy.refreshRetryTokenForRetry(retryToken, retryErrorInfo);\n                }\n                catch (refreshError) {\n                    if (!lastError.$metadata) {\n                        lastError.$metadata = {};\n                    }\n                    lastError.$metadata.attempts = attempts + 1;\n                    lastError.$metadata.totalRetryDelay = totalRetryDelay;\n                    throw lastError;\n                }\n                attempts = retryToken.getRetryCount();\n                const delay = retryToken.getRetryDelay();\n                totalRetryDelay += delay;\n                await new Promise((resolve) => setTimeout(resolve, delay));\n            }\n        }\n    }\n    else {\n        retryStrategy = retryStrategy;\n        if (retryStrategy?.mode)\n            context.userAgent = [...(context.userAgent || []), [\"cfg/retry-mode\", retryStrategy.mode]];\n        return retryStrategy.retry(next, args);\n    }\n};\nconst isRetryStrategyV2 = (retryStrategy) => typeof retryStrategy.acquireInitialRetryToken !== \"undefined\" &&\n    typeof retryStrategy.refreshRetryTokenForRetry !== \"undefined\" &&\n    typeof retryStrategy.recordSuccess !== \"undefined\";\nconst getRetryErrorInfo = (error) => {\n    const errorInfo = {\n        error,\n        errorType: getRetryErrorType(error),\n    };\n    const retryAfterHint = getRetryAfterHint(error.$response);\n    if (retryAfterHint) {\n        errorInfo.retryAfterHint = retryAfterHint;\n    }\n    return errorInfo;\n};\nconst getRetryErrorType = (error) => {\n    if (isThrottlingError(error))\n        return \"THROTTLING\";\n    if (isTransientError(error))\n        return \"TRANSIENT\";\n    if (isServerError(error))\n        return \"SERVER_ERROR\";\n    return \"CLIENT_ERROR\";\n};\nexport const retryMiddlewareOptions = {\n    name: \"retryMiddleware\",\n    tags: [\"RETRY\"],\n    step: \"finalizeRequest\",\n    priority: \"high\",\n    override: true,\n};\nexport const getRetryPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(retryMiddleware(options), retryMiddlewareOptions);\n    },\n});\nexport const getRetryAfterHint = (response) => {\n    if (!HttpResponse.isInstance(response))\n        return;\n    const retryAfterHeaderName = Object.keys(response.headers).find((key) => key.toLowerCase() === \"retry-after\");\n    if (!retryAfterHeaderName)\n        return;\n    const retryAfter = response.headers[retryAfterHeaderName];\n    const retryAfterSeconds = Number(retryAfter);\n    if (!Number.isNaN(retryAfterSeconds))\n        return new Date(retryAfterSeconds * 1000);\n    const retryAfterDate = new Date(retryAfter);\n    return retryAfterDate;\n};\n", "import { NO_RETRY_INCREMENT, RETRY_COST, TIMEOUT_RETRY_COST } from \"@smithy/util-retry\";\nexport const getDefaultRetryQuota = (initialRetryTokens, options) => {\n    const MAX_CAPACITY = initialRetryTokens;\n    const noRetryIncrement = options?.noRetryIncrement ?? NO_RETRY_INCREMENT;\n    const retryCost = options?.retryCost ?? RETRY_COST;\n    const timeoutRetryCost = options?.timeoutRetryCost ?? TIMEOUT_RETRY_COST;\n    let availableCapacity = initialRetryTokens;\n    const getCapacityAmount = (error) => (error.name === \"TimeoutError\" ? timeoutRetryCost : retryCost);\n    const hasRetryTokens = (error) => getCapacityAmount(error) <= availableCapacity;\n    const retrieveRetryTokens = (error) => {\n        if (!hasRetryTokens(error)) {\n            throw new Error(\"No retry token available\");\n        }\n        const capacityAmount = getCapacityAmount(error);\n        availableCapacity -= capacityAmount;\n        return capacityAmount;\n    };\n    const releaseRetryTokens = (capacityReleaseAmount) => {\n        availableCapacity += capacityReleaseAmount ?? noRetryIncrement;\n        availableCapacity = Math.min(availableCapacity, MAX_CAPACITY);\n    };\n    return Object.freeze({\n        hasRetryTokens,\n        retrieveRetryTokens,\n        releaseRetryTokens,\n    });\n};\n", "import { MAXIMUM_RETRY_DELAY } from \"@smithy/util-retry\";\nexport const defaultDelayDecider = (delayBase, attempts) => Math.floor(Math.min(MAXIMUM_RETRY_DELAY, Math.random() * 2 ** attempts * delayBase));\n", "import { isClockSkewError, isRetryableByTrait, isThrottlingError, isTransientError, } from \"@smithy/service-error-classification\";\nexport const defaultRetryDecider = (error) => {\n    if (!error) {\n        return false;\n    }\n    return isRetryableByTrait(error) || isClockSkewError(error) || isThrottlingError(error) || isTransientError(error);\n};\n", "import { HttpRequest, HttpResponse } from \"@smithy/protocol-http\";\nimport { isThrottlingError } from \"@smithy/service-error-classification\";\nimport { DEFAULT_MAX_ATTEMPTS, DEFAULT_RETRY_DELAY_BASE, INITIAL_RETRY_TOKENS, INVOCATION_ID_HEADER, REQUEST_HEADER, RETRY_MODES, THROTTLING_RETRY_DELAY_BASE, } from \"@smithy/util-retry\";\nimport { v4 } from \"uuid\";\nimport { getDefaultRetryQuota } from \"./defaultRetryQuota\";\nimport { defaultDelayDecider } from \"./delayDecider\";\nimport { defaultRetryDecider } from \"./retryDecider\";\nimport { asSdkError } from \"./util\";\nexport class StandardRetryStrategy {\n    constructor(maxAttemptsProvider, options) {\n        this.maxAttemptsProvider = maxAttemptsProvider;\n        this.mode = RETRY_MODES.STANDARD;\n        this.retryDecider = options?.retryDecider ?? defaultRetryDecider;\n        this.delayDecider = options?.delayDecider ?? defaultDelayDecider;\n        this.retryQuota = options?.retryQuota ?? getDefaultRetryQuota(INITIAL_RETRY_TOKENS);\n    }\n    shouldRetry(error, attempts, maxAttempts) {\n        return attempts < maxAttempts && this.retryDecider(error) && this.retryQuota.hasRetryTokens(error);\n    }\n    async getMaxAttempts() {\n        let maxAttempts;\n        try {\n            maxAttempts = await this.maxAttemptsProvider();\n        }\n        catch (error) {\n            maxAttempts = DEFAULT_MAX_ATTEMPTS;\n        }\n        return maxAttempts;\n    }\n    async retry(next, args, options) {\n        let retryTokenAmount;\n        let attempts = 0;\n        let totalDelay = 0;\n        const maxAttempts = await this.getMaxAttempts();\n        const { request } = args;\n        if (HttpRequest.isInstance(request)) {\n            request.headers[INVOCATION_ID_HEADER] = v4();\n        }\n        while (true) {\n            try {\n                if (HttpRequest.isInstance(request)) {\n                    request.headers[REQUEST_HEADER] = `attempt=${attempts + 1}; max=${maxAttempts}`;\n                }\n                if (options?.beforeRequest) {\n                    await options.beforeRequest();\n                }\n                const { response, output } = await next(args);\n                if (options?.afterRequest) {\n                    options.afterRequest(response);\n                }\n                this.retryQuota.releaseRetryTokens(retryTokenAmount);\n                output.$metadata.attempts = attempts + 1;\n                output.$metadata.totalRetryDelay = totalDelay;\n                return { response, output };\n            }\n            catch (e) {\n                const err = asSdkError(e);\n                attempts++;\n                if (this.shouldRetry(err, attempts, maxAttempts)) {\n                    retryTokenAmount = this.retryQuota.retrieveRetryTokens(err);\n                    const delayFromDecider = this.delayDecider(isThrottlingError(err) ? THROTTLING_RETRY_DELAY_BASE : DEFAULT_RETRY_DELAY_BASE, attempts);\n                    const delayFromResponse = getDelayFromRetryAfterHeader(err.$response);\n                    const delay = Math.max(delayFromResponse || 0, delayFromDecider);\n                    totalDelay += delay;\n                    await new Promise((resolve) => setTimeout(resolve, delay));\n                    continue;\n                }\n                if (!err.$metadata) {\n                    err.$metadata = {};\n                }\n                err.$metadata.attempts = attempts;\n                err.$metadata.totalRetryDelay = totalDelay;\n                throw err;\n            }\n        }\n    }\n}\nconst getDelayFromRetryAfterHeader = (response) => {\n    if (!HttpResponse.isInstance(response))\n        return;\n    const retryAfterHeaderName = Object.keys(response.headers).find((key) => key.toLowerCase() === \"retry-after\");\n    if (!retryAfterHeaderName)\n        return;\n    const retryAfter = response.headers[retryAfterHeaderName];\n    const retryAfterSeconds = Number(retryAfter);\n    if (!Number.isNaN(retryAfterSeconds))\n        return retryAfterSeconds * 1000;\n    const retryAfterDate = new Date(retryAfter);\n    return retryAfterDate.getTime() - Date.now();\n};\n", "import { DefaultRateLimiter, RETRY_MODES } from \"@smithy/util-retry\";\nimport { StandardRetryStrategy } from \"./StandardRetryStrategy\";\nexport class AdaptiveRetryStrategy extends StandardRetryStrategy {\n    constructor(maxAttemptsProvider, options) {\n        const { rateLimiter, ...superOptions } = options ?? {};\n        super(maxAttemptsProvider, superOptions);\n        this.rateLimiter = rateLimiter ?? new DefaultRateLimiter();\n        this.mode = RETRY_MODES.ADAPTIVE;\n    }\n    async retry(next, args) {\n        return super.retry(next, args, {\n            beforeRequest: async () => {\n                return this.rateLimiter.getSendToken();\n            },\n            afterRequest: (response) => {\n                this.rateLimiter.updateClientSendingRate(response);\n            },\n        });\n    }\n}\n", "import { HttpRequest } from \"@smithy/protocol-http\";\nimport { INVOCATION_ID_HEADER, REQUEST_HEADER } from \"@smithy/util-retry\";\nexport const omitRetryHeadersMiddleware = () => (next) => async (args) => {\n    const { request } = args;\n    if (HttpRequest.isInstance(request)) {\n        delete request.headers[INVOCATION_ID_HEADER];\n        delete request.headers[REQUEST_HEADER];\n    }\n    return next(args);\n};\nexport const omitRetryHeadersMiddlewareOptions = {\n    name: \"omitRetryHeadersMiddleware\",\n    tags: [\"RETRY\", \"HEADERS\", \"OMIT_RETRY_HEADERS\"],\n    relation: \"before\",\n    toMiddleware: \"awsAuthMiddleware\",\n    override: true,\n};\nexport const getOmitRetryHeadersPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(omitRetryHeadersMiddleware(), omitRetryHeadersMiddlewareOptions);\n    },\n});\n", "export * from \"./AdaptiveRetryStrategy\";\nexport * from \"./StandardRetryStrategy\";\nexport * from \"./configurations\";\nexport * from \"./delayDecider\";\nexport * from \"./omitRetryHeadersMiddleware\";\nexport * from \"./retryDecider\";\nexport * from \"./retryMiddleware\";\n", "export const SHA_256_HASH: { name: \"SHA-256\" } = { name: \"SHA-256\" };\n\nexport const SHA_256_HMAC_ALGO: { name: \"<PERSON><PERSON>\"; hash: { name: \"SHA-256\" } } = {\n  name: \"HM<PERSON>\",\n  hash: SHA_256_HASH\n};\n\nexport const EMPTY_DATA_SHA_256 = new Uint8Array([\n  227,\n  176,\n  196,\n  66,\n  152,\n  252,\n  28,\n  20,\n  154,\n  251,\n  244,\n  200,\n  153,\n  111,\n  185,\n  36,\n  39,\n  174,\n  65,\n  228,\n  100,\n  155,\n  147,\n  76,\n  164,\n  149,\n  153,\n  27,\n  120,\n  82,\n  184,\n  85\n]);\n", "const fallbackWindow = {};\nexport function locateWindow() {\n    if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else if (typeof self !== \"undefined\") {\n        return self;\n    }\n    return fallbackWindow;\n}\n", "import { Checksum, SourceData } from \"@aws-sdk/types\";\nimport { isEmptyData, convertToBuffer } from \"@aws-crypto/util\";\nimport {\n  EMPTY_DATA_SHA_256,\n  SHA_256_HASH,\n  SHA_256_HMAC_ALGO,\n} from \"./constants\";\nimport { locateWindow } from \"@aws-sdk/util-locate-window\";\n\nexport class Sha256 implements Checksum {\n  private readonly secret?: SourceData;\n  private key: Promise<CryptoKey> | undefined;\n  private toHash: Uint8Array = new Uint8Array(0);\n\n  constructor(secret?: SourceData) {\n    this.secret = secret;\n    this.reset();\n  }\n\n  update(data: SourceData): void {\n    if (isEmptyData(data)) {\n      return;\n    }\n\n    const update = convertToBuffer(data);\n    const typedArray = new Uint8Array(\n      this.toHash.byteLength + update.byteLength\n    );\n    typedArray.set(this.toHash, 0);\n    typedArray.set(update, this.toHash.byteLength);\n    this.toHash = typedArray;\n  }\n\n  digest(): Promise<Uint8Array> {\n    if (this.key) {\n      return this.key.then((key) =>\n        locateWindow()\n          .crypto.subtle.sign(SHA_256_HMAC_ALGO, key, this.toHash)\n          .then((data) => new Uint8Array(data))\n      );\n    }\n\n    if (isEmptyData(this.toHash)) {\n      return Promise.resolve(EMPTY_DATA_SHA_256);\n    }\n\n    return Promise.resolve()\n      .then(() =>\n        locateWindow().crypto.subtle.digest(SHA_256_HASH, this.toHash)\n      )\n      .then((data) => Promise.resolve(new Uint8Array(data)));\n  }\n\n  reset(): void {\n    this.toHash = new Uint8Array(0);\n    if (this.secret && this.secret !== void 0) {\n      this.key = new Promise((resolve, reject) => {\n        locateWindow()\n            .crypto.subtle.importKey(\n            \"raw\",\n            convertToBuffer(this.secret as SourceData),\n            SHA_256_HMAC_ALGO,\n            false,\n            [\"sign\"]\n        )\n            .then(resolve, reject);\n      });\n      this.key.catch(() => {});\n    }\n  }\n}\n", "type SubtleCryptoMethod =\n  | \"decrypt\"\n  | \"digest\"\n  | \"encrypt\"\n  | \"exportKey\"\n  | \"generateKey\"\n  | \"importKey\"\n  | \"sign\"\n  | \"verify\";\n\nconst subtleCryptoMethods: Array<SubtleCryptoMethod> = [\n  \"decrypt\",\n  \"digest\",\n  \"encrypt\",\n  \"exportKey\",\n  \"generateKey\",\n  \"importKey\",\n  \"sign\",\n  \"verify\"\n];\n\nexport function supportsWebCrypto(window: Window): boolean {\n  if (\n    supportsSecureRandom(window) &&\n    typeof window.crypto.subtle === \"object\"\n  ) {\n    const { subtle } = window.crypto;\n\n    return supportsSubtleCrypto(subtle);\n  }\n\n  return false;\n}\n\nexport function supportsSecureRandom(window: Window): boolean {\n  if (typeof window === \"object\" && typeof window.crypto === \"object\") {\n    const { getRandomValues } = window.crypto;\n\n    return typeof getRandomValues === \"function\";\n  }\n\n  return false;\n}\n\nexport function supportsSubtleCrypto(subtle: SubtleCrypto) {\n  return (\n    subtle &&\n    subtleCryptoMethods.every(\n      methodName => typeof subtle[methodName] === \"function\"\n    )\n  );\n}\n\nexport async function supportsZeroByteGCM(subtle: SubtleCrypto) {\n  if (!supportsSubtleCrypto(subtle)) return false;\n  try {\n    const key = await subtle.generateKey(\n      { name: \"AES-GCM\", length: 128 },\n      false,\n      [\"encrypt\"]\n    );\n    const zeroByteAuthTag = await subtle.encrypt(\n      {\n        name: \"AES-GCM\",\n        iv: new Uint8Array(Array(12)),\n        additionalData: new Uint8Array(Array(16)),\n        tagLength: 128\n      },\n      key,\n      new Uint8Array(0)\n    );\n    return zeroByteAuthTag.byteLength === 16;\n  } catch {\n    return false;\n  }\n}\n", "export * from \"./supportsWebCrypto\";\n", "import { Sha256 as WebCryptoSha256 } from \"./webCryptoSha256\";\nimport { Sha256 as JsSha256 } from \"@aws-crypto/sha256-js\";\nimport { Checksum, SourceData } from \"@aws-sdk/types\";\nimport { supportsWebCrypto } from \"@aws-crypto/supports-web-crypto\";\nimport { locateWindow } from \"@aws-sdk/util-locate-window\";\nimport { convertToBuffer } from \"@aws-crypto/util\";\n\nexport class Sha256 implements Checksum {\n  private hash: Checksum;\n\n  constructor(secret?: SourceData) {\n    if (supportsWebCrypto(locateWindow())) {\n      this.hash = new WebCryptoSha256(secret);\n    } else {\n      this.hash = new JsSha256(secret);\n    }\n  }\n\n  update(data: SourceData, encoding?: \"utf8\" | \"ascii\" | \"latin1\"): void {\n    this.hash.update(convertToBuffer(data));\n  }\n\n  digest(): Promise<Uint8Array> {\n    return this.hash.digest();\n  }\n\n  reset(): void {\n    this.hash.reset();\n  }\n}\n", "export * from \"./crossPlatformSha256\";\nexport { Sha256 as WebCryptoSha256 } from \"./webCryptoSha256\";\n", "// NOTE: this list must be up-to-date with browsers listed in\n// test/acceptance/useragentstrings.yml\nexport const BROWSER_ALIASES_MAP = {\n  'Amazon Silk': 'amazon_silk',\n  'Android Browser': 'android',\n  Bada: 'bada',\n  BlackBerry: 'blackberry',\n  Chrome: 'chrome',\n  Chromium: 'chromium',\n  Electron: 'electron',\n  Epiphany: 'epiphany',\n  Firefox: 'firefox',\n  Focus: 'focus',\n  Generic: 'generic',\n  'Google Search': 'google_search',\n  Googlebot: 'googlebot',\n  'Internet Explorer': 'ie',\n  'K-Meleon': 'k_meleon',\n  Maxthon: 'maxthon',\n  'Microsoft Edge': 'edge',\n  'M<PERSON> Browser': 'mz',\n  'NAVER Whale Browser': 'naver',\n  Opera: 'opera',\n  'Opera Coast': 'opera_coast',\n  PhantomJS: 'phantomjs',\n  Puffin: 'puffin',\n  QupZilla: 'qupzilla',\n  QQ: 'qq',\n  QQLite: 'qqlite',\n  Safari: 'safari',\n  Sailfish: 'sailfish',\n  'Samsung Internet for Android': 'samsung_internet',\n  SeaMonkey: 'seamonkey',\n  Sleipnir: 'sleipnir',\n  <PERSON>: 'swing',\n  Tizen: 'tizen',\n  'UC Browser': 'uc',\n  Vivaldi: 'vivaldi',\n  'WebOS Browser': 'webos',\n  WeChat: 'wechat',\n  'Yandex Browser': 'yandex',\n  Roku: 'roku',\n};\n\nexport const BROWSER_MAP = {\n  amazon_silk: 'Amazon Silk',\n  android: 'Android Browser',\n  bada: 'Bada',\n  blackberry: 'BlackBerry',\n  chrome: 'Chrome',\n  chromium: 'Chromium',\n  electron: 'Electron',\n  epiphany: 'Epiphany',\n  firefox: 'Firefox',\n  focus: 'Focus',\n  generic: 'Generic',\n  googlebot: 'Googlebot',\n  google_search: 'Google Search',\n  ie: 'Internet Explorer',\n  k_meleon: 'K-Meleon',\n  maxthon: 'Maxthon',\n  edge: 'Microsoft Edge',\n  mz: 'MZ Browser',\n  naver: 'NAVER Whale Browser',\n  opera: 'Opera',\n  opera_coast: 'Opera Coast',\n  phantomjs: 'PhantomJS',\n  puffin: 'Puffin',\n  qupzilla: 'QupZilla',\n  qq: 'QQ Browser',\n  qqlite: 'QQ Browser Lite',\n  safari: 'Safari',\n  sailfish: 'Sailfish',\n  samsung_internet: 'Samsung Internet for Android',\n  seamonkey: 'SeaMonkey',\n  sleipnir: 'Sleipnir',\n  swing: 'Swing',\n  tizen: 'Tizen',\n  uc: 'UC Browser',\n  vivaldi: 'Vivaldi',\n  webos: 'WebOS Browser',\n  wechat: 'WeChat',\n  yandex: 'Yandex Browser',\n};\n\nexport const PLATFORMS_MAP = {\n  tablet: 'tablet',\n  mobile: 'mobile',\n  desktop: 'desktop',\n  tv: 'tv',\n};\n\nexport const OS_MAP = {\n  WindowsPhone: 'Windows Phone',\n  Windows: 'Windows',\n  MacOS: 'macOS',\n  iOS: 'iOS',\n  Android: 'Android',\n  WebOS: 'WebOS',\n  BlackBerry: 'BlackBerry',\n  Bada: 'Bada',\n  Tizen: 'Tizen',\n  Linux: 'Linux',\n  ChromeOS: 'Chrome OS',\n  PlayStation4: 'PlayStation 4',\n  Roku: 'Roku',\n};\n\nexport const ENGINE_MAP = {\n  EdgeHTML: 'EdgeHTML',\n  Blink: 'Blink',\n  Trident: 'Trident',\n  Presto: 'Presto',\n  Gecko: 'Gecko',\n  WebKit: 'WebKit',\n};\n", "import { BROWSER_MAP, BROWSER_ALIASES_MAP } from './constants.js';\n\nexport default class Utils {\n  /**\n   * Get first matched item for a string\n   * @param {RegExp} regexp\n   * @param {String} ua\n   * @return {Array|{index: number, input: string}|*|boolean|string}\n   */\n  static getFirstMatch(regexp, ua) {\n    const match = ua.match(regexp);\n    return (match && match.length > 0 && match[1]) || '';\n  }\n\n  /**\n   * Get second matched item for a string\n   * @param regexp\n   * @param {String} ua\n   * @return {Array|{index: number, input: string}|*|boolean|string}\n   */\n  static getSecondMatch(regexp, ua) {\n    const match = ua.match(regexp);\n    return (match && match.length > 1 && match[2]) || '';\n  }\n\n  /**\n   * Match a regexp and return a constant or undefined\n   * @param {RegExp} regexp\n   * @param {String} ua\n   * @param {*} _const Any const that will be returned if regexp matches the string\n   * @return {*}\n   */\n  static matchAndReturnConst(regexp, ua, _const) {\n    if (regexp.test(ua)) {\n      return _const;\n    }\n    return void (0);\n  }\n\n  static getWindowsVersionName(version) {\n    switch (version) {\n      case 'NT': return 'NT';\n      case 'XP': return 'XP';\n      case 'NT 5.0': return '2000';\n      case 'NT 5.1': return 'XP';\n      case 'NT 5.2': return '2003';\n      case 'NT 6.0': return 'Vista';\n      case 'NT 6.1': return '7';\n      case 'NT 6.2': return '8';\n      case 'NT 6.3': return '8.1';\n      case 'NT 10.0': return '10';\n      default: return undefined;\n    }\n  }\n\n  /**\n   * Get macOS version name\n   *    10.5 - Leopard\n   *    10.6 - Snow Leopard\n   *    10.7 - Lion\n   *    10.8 - Mountain Lion\n   *    10.9 - Mavericks\n   *    10.10 - Yosemite\n   *    10.11 - El Capitan\n   *    10.12 - Sierra\n   *    10.13 - High Sierra\n   *    10.14 - Mojave\n   *    10.15 - Catalina\n   *\n   * @example\n   *   getMacOSVersionName(\"10.14\") // 'Mojave'\n   *\n   * @param  {string} version\n   * @return {string} versionName\n   */\n  static getMacOSVersionName(version) {\n    const v = version.split('.').splice(0, 2).map(s => parseInt(s, 10) || 0);\n    v.push(0);\n    if (v[0] !== 10) return undefined;\n    switch (v[1]) {\n      case 5: return 'Leopard';\n      case 6: return 'Snow Leopard';\n      case 7: return 'Lion';\n      case 8: return 'Mountain Lion';\n      case 9: return 'Mavericks';\n      case 10: return 'Yosemite';\n      case 11: return 'El Capitan';\n      case 12: return 'Sierra';\n      case 13: return 'High Sierra';\n      case 14: return 'Mojave';\n      case 15: return 'Catalina';\n      default: return undefined;\n    }\n  }\n\n  /**\n   * Get Android version name\n   *    1.5 - Cupcake\n   *    1.6 - Donut\n   *    2.0 - Eclair\n   *    2.1 - Eclair\n   *    2.2 - Froyo\n   *    2.x - Gingerbread\n   *    3.x - Honeycomb\n   *    4.0 - Ice Cream Sandwich\n   *    4.1 - Jelly Bean\n   *    4.4 - KitKat\n   *    5.x - Lollipop\n   *    6.x - Marshmallow\n   *    7.x - Nougat\n   *    8.x - Oreo\n   *    9.x - Pie\n   *\n   * @example\n   *   getAndroidVersionName(\"7.0\") // 'Nougat'\n   *\n   * @param  {string} version\n   * @return {string} versionName\n   */\n  static getAndroidVersionName(version) {\n    const v = version.split('.').splice(0, 2).map(s => parseInt(s, 10) || 0);\n    v.push(0);\n    if (v[0] === 1 && v[1] < 5) return undefined;\n    if (v[0] === 1 && v[1] < 6) return 'Cupcake';\n    if (v[0] === 1 && v[1] >= 6) return 'Donut';\n    if (v[0] === 2 && v[1] < 2) return 'Eclair';\n    if (v[0] === 2 && v[1] === 2) return 'Froyo';\n    if (v[0] === 2 && v[1] > 2) return 'Gingerbread';\n    if (v[0] === 3) return 'Honeycomb';\n    if (v[0] === 4 && v[1] < 1) return 'Ice Cream Sandwich';\n    if (v[0] === 4 && v[1] < 4) return 'Jelly Bean';\n    if (v[0] === 4 && v[1] >= 4) return 'KitKat';\n    if (v[0] === 5) return 'Lollipop';\n    if (v[0] === 6) return 'Marshmallow';\n    if (v[0] === 7) return 'Nougat';\n    if (v[0] === 8) return 'Oreo';\n    if (v[0] === 9) return 'Pie';\n    return undefined;\n  }\n\n  /**\n   * Get version precisions count\n   *\n   * @example\n   *   getVersionPrecision(\"1.10.3\") // 3\n   *\n   * @param  {string} version\n   * @return {number}\n   */\n  static getVersionPrecision(version) {\n    return version.split('.').length;\n  }\n\n  /**\n   * Calculate browser version weight\n   *\n   * @example\n   *   compareVersions('********',  '*******.90')    // 1\n   *   compareVersions('*********', '********.90');  // 1\n   *   compareVersions('********',  '********');     // 0\n   *   compareVersions('********',  '1.0800.2');     // -1\n   *   compareVersions('********',  '1.10',  true);  // 0\n   *\n   * @param {String} versionA versions versions to compare\n   * @param {String} versionB versions versions to compare\n   * @param {boolean} [isLoose] enable loose comparison\n   * @return {Number} comparison result: -1 when versionA is lower,\n   * 1 when versionA is bigger, 0 when both equal\n   */\n  /* eslint consistent-return: 1 */\n  static compareVersions(versionA, versionB, isLoose = false) {\n    // 1) get common precision for both versions, for example for \"10.0\" and \"9\" it should be 2\n    const versionAPrecision = Utils.getVersionPrecision(versionA);\n    const versionBPrecision = Utils.getVersionPrecision(versionB);\n\n    let precision = Math.max(versionAPrecision, versionBPrecision);\n    let lastPrecision = 0;\n\n    const chunks = Utils.map([versionA, versionB], (version) => {\n      const delta = precision - Utils.getVersionPrecision(version);\n\n      // 2) \"9\" -> \"9.0\" (for precision = 2)\n      const _version = version + new Array(delta + 1).join('.0');\n\n      // 3) \"9.0\" -> [\"000000000\"\", \"000000009\"]\n      return Utils.map(_version.split('.'), chunk => new Array(20 - chunk.length).join('0') + chunk).reverse();\n    });\n\n    // adjust precision for loose comparison\n    if (isLoose) {\n      lastPrecision = precision - Math.min(versionAPrecision, versionBPrecision);\n    }\n\n    // iterate in reverse order by reversed chunks array\n    precision -= 1;\n    while (precision >= lastPrecision) {\n      // 4) compare: \"000000009\" > \"000000010\" = false (but \"9\" > \"10\" = true)\n      if (chunks[0][precision] > chunks[1][precision]) {\n        return 1;\n      }\n\n      if (chunks[0][precision] === chunks[1][precision]) {\n        if (precision === lastPrecision) {\n          // all version chunks are same\n          return 0;\n        }\n\n        precision -= 1;\n      } else if (chunks[0][precision] < chunks[1][precision]) {\n        return -1;\n      }\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Array::map polyfill\n   *\n   * @param  {Array} arr\n   * @param  {Function} iterator\n   * @return {Array}\n   */\n  static map(arr, iterator) {\n    const result = [];\n    let i;\n    if (Array.prototype.map) {\n      return Array.prototype.map.call(arr, iterator);\n    }\n    for (i = 0; i < arr.length; i += 1) {\n      result.push(iterator(arr[i]));\n    }\n    return result;\n  }\n\n  /**\n   * Array::find polyfill\n   *\n   * @param  {Array} arr\n   * @param  {Function} predicate\n   * @return {Array}\n   */\n  static find(arr, predicate) {\n    let i;\n    let l;\n    if (Array.prototype.find) {\n      return Array.prototype.find.call(arr, predicate);\n    }\n    for (i = 0, l = arr.length; i < l; i += 1) {\n      const value = arr[i];\n      if (predicate(value, i)) {\n        return value;\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * Object::assign polyfill\n   *\n   * @param  {Object} obj\n   * @param  {Object} ...objs\n   * @return {Object}\n   */\n  static assign(obj, ...assigners) {\n    const result = obj;\n    let i;\n    let l;\n    if (Object.assign) {\n      return Object.assign(obj, ...assigners);\n    }\n    for (i = 0, l = assigners.length; i < l; i += 1) {\n      const assigner = assigners[i];\n      if (typeof assigner === 'object' && assigner !== null) {\n        const keys = Object.keys(assigner);\n        keys.forEach((key) => {\n          result[key] = assigner[key];\n        });\n      }\n    }\n    return obj;\n  }\n\n  /**\n   * Get short version/alias for a browser name\n   *\n   * @example\n   *   getBrowserAlias('Microsoft Edge') // edge\n   *\n   * @param  {string} browserName\n   * @return {string}\n   */\n  static getBrowserAlias(browserName) {\n    return BROWSER_ALIASES_MAP[browserName];\n  }\n\n  /**\n   * Get short version/alias for a browser name\n   *\n   * @example\n   *   getBrowserAlias('edge') // Microsoft Edge\n   *\n   * @param  {string} browserAlias\n   * @return {string}\n   */\n  static getBrowserTypeByAlias(browserAlias) {\n    return BROWSER_MAP[browserAlias] || '';\n  }\n}\n", "/**\n * <PERSON><PERSON><PERSON>' descriptors\n *\n * The idea of descriptors is simple. You should know about them two simple things:\n * 1. Every descriptor has a method or property called `test` and a `describe` method.\n * 2. Order of descriptors is important.\n *\n * More details:\n * 1. Method or property `test` serves as a way to detect whether the UA string\n * matches some certain browser or not. The `describe` method helps to make a result\n * object with params that show some browser-specific things: name, version, etc.\n * 2. Order of descriptors is important because a Parser goes through them one by one\n * in course. For example, if you insert Chrome's descriptor as the first one,\n * more then a half of browsers will be described as Chrome, because they will pass\n * the Chrome descriptor's test.\n *\n * Descriptor's `test` could be a property with an array of RegExps, where every RegExp\n * will be applied to a UA string to test it whether it matches or not.\n * If a descriptor has two or more regexps in the `test` array it tests them one by one\n * with a logical sum operation. Parser stops if it has found any RegExp that matches the UA.\n *\n * Or `test` could be a method. In that case it gets a Parser instance and should\n * return true/false to get the Parser know if this browser descriptor matches the UA or not.\n */\n\nimport Utils from './utils.js';\n\nconst commonVersionIdentifier = /version\\/(\\d+(\\.?_?\\d+)+)/i;\n\nconst browsersList = [\n  /* Googlebot */\n  {\n    test: [/googlebot/i],\n    describe(ua) {\n      const browser = {\n        name: 'Googlebot',\n      };\n      const version = Utils.getFirstMatch(/googlebot\\/(\\d+(\\.\\d+))/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Opera < 13.0 */\n  {\n    test: [/opera/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:opera)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Opera > 13.0 */\n  {\n    test: [/opr\\/|opios/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera',\n      };\n      const version = Utils.getFirstMatch(/(?:opr|opios)[\\s/](\\S+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/SamsungBrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'Samsung Internet for Android',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:SamsungBrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/Whale/i],\n    describe(ua) {\n      const browser = {\n        name: 'NAVER Whale Browser',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:whale)[\\s/](\\d+(?:\\.\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/MZBrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'MZ Browser',\n      };\n      const version = Utils.getFirstMatch(/(?:MZBrowser)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/focus/i],\n    describe(ua) {\n      const browser = {\n        name: 'Focus',\n      };\n      const version = Utils.getFirstMatch(/(?:focus)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/swing/i],\n    describe(ua) {\n      const browser = {\n        name: 'Swing',\n      };\n      const version = Utils.getFirstMatch(/(?:swing)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/coast/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera Coast',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:coast)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/opt\\/\\d+(?:.?_?\\d+)+/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera Touch',\n      };\n      const version = Utils.getFirstMatch(/(?:opt)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/yabrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'Yandex Browser',\n      };\n      const version = Utils.getFirstMatch(/(?:yabrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/ucbrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'UC Browser',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:ucbrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/Maxthon|mxios/i],\n    describe(ua) {\n      const browser = {\n        name: 'Maxthon',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:Maxthon|mxios)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/epiphany/i],\n    describe(ua) {\n      const browser = {\n        name: 'Epiphany',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:epiphany)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/puffin/i],\n    describe(ua) {\n      const browser = {\n        name: 'Puffin',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:puffin)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/sleipnir/i],\n    describe(ua) {\n      const browser = {\n        name: 'Sleipnir',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:sleipnir)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/k-meleon/i],\n    describe(ua) {\n      const browser = {\n        name: 'K-Meleon',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/(?:k-meleon)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/micromessenger/i],\n    describe(ua) {\n      const browser = {\n        name: 'WeChat',\n      };\n      const version = Utils.getFirstMatch(/(?:micromessenger)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/qqbrowser/i],\n    describe(ua) {\n      const browser = {\n        name: (/qqbrowserlite/i).test(ua) ? 'QQ Browser Lite' : 'QQ Browser',\n      };\n      const version = Utils.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/msie|trident/i],\n    describe(ua) {\n      const browser = {\n        name: 'Internet Explorer',\n      };\n      const version = Utils.getFirstMatch(/(?:msie |rv:)(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/\\sedg\\//i],\n    describe(ua) {\n      const browser = {\n        name: 'Microsoft Edge',\n      };\n\n      const version = Utils.getFirstMatch(/\\sedg\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/edg([ea]|ios)/i],\n    describe(ua) {\n      const browser = {\n        name: 'Microsoft Edge',\n      };\n\n      const version = Utils.getSecondMatch(/edg([ea]|ios)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/vivaldi/i],\n    describe(ua) {\n      const browser = {\n        name: 'Vivaldi',\n      };\n      const version = Utils.getFirstMatch(/vivaldi\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/seamonkey/i],\n    describe(ua) {\n      const browser = {\n        name: 'SeaMonkey',\n      };\n      const version = Utils.getFirstMatch(/seamonkey\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/sailfish/i],\n    describe(ua) {\n      const browser = {\n        name: 'Sailfish',\n      };\n\n      const version = Utils.getFirstMatch(/sailfish\\s?browser\\/(\\d+(\\.\\d+)?)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/silk/i],\n    describe(ua) {\n      const browser = {\n        name: 'Amazon Silk',\n      };\n      const version = Utils.getFirstMatch(/silk\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/phantom/i],\n    describe(ua) {\n      const browser = {\n        name: 'PhantomJS',\n      };\n      const version = Utils.getFirstMatch(/phantomjs\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/slimerjs/i],\n    describe(ua) {\n      const browser = {\n        name: 'SlimerJS',\n      };\n      const version = Utils.getFirstMatch(/slimerjs\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/blackberry|\\bbb\\d+/i, /rim\\stablet/i],\n    describe(ua) {\n      const browser = {\n        name: 'BlackBerry',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/blackberry[\\d]+\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/(web|hpw)[o0]s/i],\n    describe(ua) {\n      const browser = {\n        name: 'WebOS Browser',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua) || Utils.getFirstMatch(/w(?:eb)?[o0]sbrowser\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/bada/i],\n    describe(ua) {\n      const browser = {\n        name: 'Bada',\n      };\n      const version = Utils.getFirstMatch(/dolfin\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/tizen/i],\n    describe(ua) {\n      const browser = {\n        name: 'Tizen',\n      };\n      const version = Utils.getFirstMatch(/(?:tizen\\s?)?browser\\/(\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/qupzilla/i],\n    describe(ua) {\n      const browser = {\n        name: 'QupZilla',\n      };\n      const version = Utils.getFirstMatch(/(?:qupzilla)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/firefox|iceweasel|fxios/i],\n    describe(ua) {\n      const browser = {\n        name: 'Firefox',\n      };\n      const version = Utils.getFirstMatch(/(?:firefox|iceweasel|fxios)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/electron/i],\n    describe(ua) {\n      const browser = {\n        name: 'Electron',\n      };\n      const version = Utils.getFirstMatch(/(?:electron)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/MiuiBrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'Miui',\n      };\n      const version = Utils.getFirstMatch(/(?:MiuiBrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/chromium/i],\n    describe(ua) {\n      const browser = {\n        name: 'Chromium',\n      };\n      const version = Utils.getFirstMatch(/(?:chromium)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/chrome|crios|crmo/i],\n    describe(ua) {\n      const browser = {\n        name: 'Chrome',\n      };\n      const version = Utils.getFirstMatch(/(?:chrome|crios|crmo)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/GSA/i],\n    describe(ua) {\n      const browser = {\n        name: 'Google Search',\n      };\n      const version = Utils.getFirstMatch(/(?:GSA)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Android Browser */\n  {\n    test(parser) {\n      const notLikeAndroid = !parser.test(/like android/i);\n      const butAndroid = parser.test(/android/i);\n      return notLikeAndroid && butAndroid;\n    },\n    describe(ua) {\n      const browser = {\n        name: 'Android Browser',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* PlayStation 4 */\n  {\n    test: [/playstation 4/i],\n    describe(ua) {\n      const browser = {\n        name: 'PlayStation 4',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Safari */\n  {\n    test: [/safari|applewebkit/i],\n    describe(ua) {\n      const browser = {\n        name: 'Safari',\n      };\n      const version = Utils.getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Something else */\n  {\n    test: [/.*/i],\n    describe(ua) {\n      /* Here we try to make sure that there are explicit details about the device\n       * in order to decide what regexp exactly we want to apply\n       * (as there is a specific decision based on that conclusion)\n       */\n      const regexpWithoutDeviceSpec = /^(.*)\\/(.*) /;\n      const regexpWithDeviceSpec = /^(.*)\\/(.*)[ \\t]\\((.*)/;\n      const hasDeviceSpec = ua.search('\\\\(') !== -1;\n      const regexp = hasDeviceSpec ? regexpWithDeviceSpec : regexpWithoutDeviceSpec;\n      return {\n        name: Utils.getFirstMatch(regexp, ua),\n        version: Utils.getSecondMatch(regexp, ua),\n      };\n    },\n  },\n];\n\nexport default browsersList;\n", "import Utils from './utils.js';\nimport { OS_MAP } from './constants.js';\n\nexport default [\n  /* Roku */\n  {\n    test: [/Roku\\/DVP/],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/Roku\\/DVP-(\\d+\\.\\d+)/i, ua);\n      return {\n        name: OS_MAP.Roku,\n        version,\n      };\n    },\n  },\n\n  /* Windows Phone */\n  {\n    test: [/windows phone/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/windows phone (?:os)?\\s?(\\d+(\\.\\d+)*)/i, ua);\n      return {\n        name: OS_MAP.WindowsPhone,\n        version,\n      };\n    },\n  },\n\n  /* Windows */\n  {\n    test: [/windows /i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/Windows ((NT|XP)( \\d\\d?.\\d)?)/i, ua);\n      const versionName = Utils.getWindowsVersionName(version);\n\n      return {\n        name: OS_MAP.Windows,\n        version,\n        versionName,\n      };\n    },\n  },\n\n  /* Firefox on iPad */\n  {\n    test: [/Macintosh(.*?) FxiOS(.*?)\\//],\n    describe(ua) {\n      const result = {\n        name: OS_MAP.iOS,\n      };\n      const version = Utils.getSecondMatch(/(Version\\/)(\\d[\\d.]+)/, ua);\n      if (version) {\n        result.version = version;\n      }\n      return result;\n    },\n  },\n\n  /* macOS */\n  {\n    test: [/macintosh/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/mac os x (\\d+(\\.?_?\\d+)+)/i, ua).replace(/[_\\s]/g, '.');\n      const versionName = Utils.getMacOSVersionName(version);\n\n      const os = {\n        name: OS_MAP.MacOS,\n        version,\n      };\n      if (versionName) {\n        os.versionName = versionName;\n      }\n      return os;\n    },\n  },\n\n  /* iOS */\n  {\n    test: [/(ipod|iphone|ipad)/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/os (\\d+([_\\s]\\d+)*) like mac os x/i, ua).replace(/[_\\s]/g, '.');\n\n      return {\n        name: OS_MAP.iOS,\n        version,\n      };\n    },\n  },\n\n  /* Android */\n  {\n    test(parser) {\n      const notLikeAndroid = !parser.test(/like android/i);\n      const butAndroid = parser.test(/android/i);\n      return notLikeAndroid && butAndroid;\n    },\n    describe(ua) {\n      const version = Utils.getFirstMatch(/android[\\s/-](\\d+(\\.\\d+)*)/i, ua);\n      const versionName = Utils.getAndroidVersionName(version);\n      const os = {\n        name: OS_MAP.Android,\n        version,\n      };\n      if (versionName) {\n        os.versionName = versionName;\n      }\n      return os;\n    },\n  },\n\n  /* WebOS */\n  {\n    test: [/(web|hpw)[o0]s/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/(?:web|hpw)[o0]s\\/(\\d+(\\.\\d+)*)/i, ua);\n      const os = {\n        name: OS_MAP.WebOS,\n      };\n\n      if (version && version.length) {\n        os.version = version;\n      }\n      return os;\n    },\n  },\n\n  /* BlackBerry */\n  {\n    test: [/blackberry|\\bbb\\d+/i, /rim\\stablet/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/rim\\stablet\\sos\\s(\\d+(\\.\\d+)*)/i, ua)\n        || Utils.getFirstMatch(/blackberry\\d+\\/(\\d+([_\\s]\\d+)*)/i, ua)\n        || Utils.getFirstMatch(/\\bbb(\\d+)/i, ua);\n\n      return {\n        name: OS_MAP.BlackBerry,\n        version,\n      };\n    },\n  },\n\n  /* Bada */\n  {\n    test: [/bada/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/bada\\/(\\d+(\\.\\d+)*)/i, ua);\n\n      return {\n        name: OS_MAP.Bada,\n        version,\n      };\n    },\n  },\n\n  /* Tizen */\n  {\n    test: [/tizen/i],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/tizen[/\\s](\\d+(\\.\\d+)*)/i, ua);\n\n      return {\n        name: OS_MAP.Tizen,\n        version,\n      };\n    },\n  },\n\n  /* Linux */\n  {\n    test: [/linux/i],\n    describe() {\n      return {\n        name: OS_MAP.Linux,\n      };\n    },\n  },\n\n  /* Chrome OS */\n  {\n    test: [/CrOS/],\n    describe() {\n      return {\n        name: OS_MAP.ChromeOS,\n      };\n    },\n  },\n\n  /* Playstation 4 */\n  {\n    test: [/PlayStation 4/],\n    describe(ua) {\n      const version = Utils.getFirstMatch(/PlayStation 4[/\\s](\\d+(\\.\\d+)*)/i, ua);\n      return {\n        name: OS_MAP.PlayStation4,\n        version,\n      };\n    },\n  },\n];\n", "import Utils from './utils.js';\nimport { PLATFORMS_MAP } from './constants.js';\n\n/*\n * Tablets go first since usually they have more specific\n * signs to detect.\n */\n\nexport default [\n  /* Googlebot */\n  {\n    test: [/googlebot/i],\n    describe() {\n      return {\n        type: 'bot',\n        vendor: 'Google',\n      };\n    },\n  },\n\n  /* Huawei */\n  {\n    test: [/huawei/i],\n    describe(ua) {\n      const model = Utils.getFirstMatch(/(can-l01)/i, ua) && 'Nova';\n      const platform = {\n        type: PLATFORMS_MAP.mobile,\n        vendor: 'Huawei',\n      };\n      if (model) {\n        platform.model = model;\n      }\n      return platform;\n    },\n  },\n\n  /* Nexus Tablet */\n  {\n    test: [/nexus\\s*(?:7|8|9|10).*/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n        vendor: 'Nexus',\n      };\n    },\n  },\n\n  /* iPad */\n  {\n    test: [/ipad/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n        vendor: 'Apple',\n        model: 'iPad',\n      };\n    },\n  },\n\n  /* Firefox on iPad */\n  {\n    test: [/Macintosh(.*?) FxiOS(.*?)\\//],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n        vendor: 'Apple',\n        model: 'iPad',\n      };\n    },\n  },\n\n  /* Amazon Kindle Fire */\n  {\n    test: [/kftt build/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n        vendor: 'Amazon',\n        model: 'Kindle Fire HD 7',\n      };\n    },\n  },\n\n  /* Another Amazon Tablet with Silk */\n  {\n    test: [/silk/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n        vendor: 'Amazon',\n      };\n    },\n  },\n\n  /* Tablet */\n  {\n    test: [/tablet(?! pc)/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n      };\n    },\n  },\n\n  /* iPod/iPhone */\n  {\n    test(parser) {\n      const iDevice = parser.test(/ipod|iphone/i);\n      const likeIDevice = parser.test(/like (ipod|iphone)/i);\n      return iDevice && !likeIDevice;\n    },\n    describe(ua) {\n      const model = Utils.getFirstMatch(/(ipod|iphone)/i, ua);\n      return {\n        type: PLATFORMS_MAP.mobile,\n        vendor: 'Apple',\n        model,\n      };\n    },\n  },\n\n  /* Nexus Mobile */\n  {\n    test: [/nexus\\s*[0-6].*/i, /galaxy nexus/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.mobile,\n        vendor: 'Nexus',\n      };\n    },\n  },\n\n  /* Mobile */\n  {\n    test: [/[^-]mobi/i],\n    describe() {\n      return {\n        type: PLATFORMS_MAP.mobile,\n      };\n    },\n  },\n\n  /* BlackBerry */\n  {\n    test(parser) {\n      return parser.getBrowserName(true) === 'blackberry';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.mobile,\n        vendor: 'BlackBerry',\n      };\n    },\n  },\n\n  /* Bada */\n  {\n    test(parser) {\n      return parser.getBrowserName(true) === 'bada';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.mobile,\n      };\n    },\n  },\n\n  /* Windows Phone */\n  {\n    test(parser) {\n      return parser.getBrowserName() === 'windows phone';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.mobile,\n        vendor: 'Microsoft',\n      };\n    },\n  },\n\n  /* Android Tablet */\n  {\n    test(parser) {\n      const osMajorVersion = Number(String(parser.getOSVersion()).split('.')[0]);\n      return parser.getOSName(true) === 'android' && (osMajorVersion >= 3);\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tablet,\n      };\n    },\n  },\n\n  /* Android Mobile */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'android';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.mobile,\n      };\n    },\n  },\n\n  /* desktop */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'macos';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.desktop,\n        vendor: 'Apple',\n      };\n    },\n  },\n\n  /* Windows */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'windows';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.desktop,\n      };\n    },\n  },\n\n  /* Linux */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'linux';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.desktop,\n      };\n    },\n  },\n\n  /* PlayStation 4 */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'playstation 4';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tv,\n      };\n    },\n  },\n\n  /* Roku */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'roku';\n    },\n    describe() {\n      return {\n        type: PLATFORMS_MAP.tv,\n      };\n    },\n  },\n];\n", "import Utils from './utils.js';\nimport { ENGINE_MAP } from './constants.js';\n\n/*\n * More specific goes first\n */\nexport default [\n  /* EdgeHTML */\n  {\n    test(parser) {\n      return parser.getBrowserName(true) === 'microsoft edge';\n    },\n    describe(ua) {\n      const isBlinkBased = /\\sedg\\//i.test(ua);\n\n      // return blink if it's blink-based one\n      if (isBlinkBased) {\n        return {\n          name: ENGINE_MAP.Blink,\n        };\n      }\n\n      // otherwise match the version and return EdgeHTML\n      const version = Utils.getFirstMatch(/edge\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      return {\n        name: ENGINE_MAP.EdgeHTML,\n        version,\n      };\n    },\n  },\n\n  /* Trident */\n  {\n    test: [/trident/i],\n    describe(ua) {\n      const engine = {\n        name: ENGINE_MAP.Trident,\n      };\n\n      const version = Utils.getFirstMatch(/trident\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n\n  /* Presto */\n  {\n    test(parser) {\n      return parser.test(/presto/i);\n    },\n    describe(ua) {\n      const engine = {\n        name: ENGINE_MAP.Presto,\n      };\n\n      const version = Utils.getFirstMatch(/presto\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n\n  /* Gecko */\n  {\n    test(parser) {\n      const isGecko = parser.test(/gecko/i);\n      const likeGecko = parser.test(/like gecko/i);\n      return isGecko && !likeGecko;\n    },\n    describe(ua) {\n      const engine = {\n        name: ENGINE_MAP.Gecko,\n      };\n\n      const version = Utils.getFirstMatch(/gecko\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n\n  /* Blink */\n  {\n    test: [/(apple)?webkit\\/537\\.36/i],\n    describe() {\n      return {\n        name: ENGINE_MAP.Blink,\n      };\n    },\n  },\n\n  /* WebKit */\n  {\n    test: [/(apple)?webkit/i],\n    describe(ua) {\n      const engine = {\n        name: ENGINE_MAP.WebKit,\n      };\n\n      const version = Utils.getFirstMatch(/webkit\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n];\n", "import browserParsersList from './parser-browsers.js';\nimport osParsersList from './parser-os.js';\nimport platformParsersList from './parser-platforms.js';\nimport enginesParsersList from './parser-engines.js';\nimport Utils from './utils.js';\n\n/**\n * The main class that arranges the whole parsing process.\n */\nclass Parser {\n  /**\n   * Create instance of Parser\n   *\n   * @param {String} UA User-Agent string\n   * @param {Boolean} [skipParsing=false] parser can skip parsing in purpose of performance\n   * improvements if you need to make a more particular parsing\n   * like {@link Parser#parseBrowser} or {@link Parser#parsePlatform}\n   *\n   * @throw {Error} in case of empty UA String\n   *\n   * @constructor\n   */\n  constructor(UA, skipParsing = false) {\n    if (UA === void (0) || UA === null || UA === '') {\n      throw new Error(\"UserAgent parameter can't be empty\");\n    }\n\n    this._ua = UA;\n\n    /**\n     * @typedef ParsedResult\n     * @property {Object} browser\n     * @property {String|undefined} [browser.name]\n     * Browser name, like `\"Chrome\"` or `\"Internet Explorer\"`\n     * @property {String|undefined} [browser.version] Browser version as a String `\"12.01.45334.10\"`\n     * @property {Object} os\n     * @property {String|undefined} [os.name] OS name, like `\"Windows\"` or `\"macOS\"`\n     * @property {String|undefined} [os.version] OS version, like `\"NT 5.1\"` or `\"10.11.1\"`\n     * @property {String|undefined} [os.versionName] OS name, like `\"XP\"` or `\"High Sierra\"`\n     * @property {Object} platform\n     * @property {String|undefined} [platform.type]\n     * platform type, can be either `\"desktop\"`, `\"tablet\"` or `\"mobile\"`\n     * @property {String|undefined} [platform.vendor] Vendor of the device,\n     * like `\"Apple\"` or `\"Samsung\"`\n     * @property {String|undefined} [platform.model] Device model,\n     * like `\"iPhone\"` or `\"Kindle Fire HD 7\"`\n     * @property {Object} engine\n     * @property {String|undefined} [engine.name]\n     * Can be any of this: `WebKit`, `Blink`, `Gecko`, `Trident`, `Presto`, `EdgeHTML`\n     * @property {String|undefined} [engine.version] String version of the engine\n     */\n    this.parsedResult = {};\n\n    if (skipParsing !== true) {\n      this.parse();\n    }\n  }\n\n  /**\n   * Get UserAgent string of current Parser instance\n   * @return {String} User-Agent String of the current <Parser> object\n   *\n   * @public\n   */\n  getUA() {\n    return this._ua;\n  }\n\n  /**\n   * Test a UA string for a regexp\n   * @param {RegExp} regex\n   * @return {Boolean}\n   */\n  test(regex) {\n    return regex.test(this._ua);\n  }\n\n  /**\n   * Get parsed browser object\n   * @return {Object}\n   */\n  parseBrowser() {\n    this.parsedResult.browser = {};\n\n    const browserDescriptor = Utils.find(browserParsersList, (_browser) => {\n      if (typeof _browser.test === 'function') {\n        return _browser.test(this);\n      }\n\n      if (_browser.test instanceof Array) {\n        return _browser.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (browserDescriptor) {\n      this.parsedResult.browser = browserDescriptor.describe(this.getUA());\n    }\n\n    return this.parsedResult.browser;\n  }\n\n  /**\n   * Get parsed browser object\n   * @return {Object}\n   *\n   * @public\n   */\n  getBrowser() {\n    if (this.parsedResult.browser) {\n      return this.parsedResult.browser;\n    }\n\n    return this.parseBrowser();\n  }\n\n  /**\n   * Get browser's name\n   * @return {String} Browser's name or an empty string\n   *\n   * @public\n   */\n  getBrowserName(toLowerCase) {\n    if (toLowerCase) {\n      return String(this.getBrowser().name).toLowerCase() || '';\n    }\n    return this.getBrowser().name || '';\n  }\n\n\n  /**\n   * Get browser's version\n   * @return {String} version of browser\n   *\n   * @public\n   */\n  getBrowserVersion() {\n    return this.getBrowser().version;\n  }\n\n  /**\n   * Get OS\n   * @return {Object}\n   *\n   * @example\n   * this.getOS();\n   * {\n   *   name: 'macOS',\n   *   version: '10.11.12'\n   * }\n   */\n  getOS() {\n    if (this.parsedResult.os) {\n      return this.parsedResult.os;\n    }\n\n    return this.parseOS();\n  }\n\n  /**\n   * Parse OS and save it to this.parsedResult.os\n   * @return {*|{}}\n   */\n  parseOS() {\n    this.parsedResult.os = {};\n\n    const os = Utils.find(osParsersList, (_os) => {\n      if (typeof _os.test === 'function') {\n        return _os.test(this);\n      }\n\n      if (_os.test instanceof Array) {\n        return _os.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (os) {\n      this.parsedResult.os = os.describe(this.getUA());\n    }\n\n    return this.parsedResult.os;\n  }\n\n  /**\n   * Get OS name\n   * @param {Boolean} [toLowerCase] return lower-cased value\n   * @return {String} name of the OS — macOS, Windows, Linux, etc.\n   */\n  getOSName(toLowerCase) {\n    const { name } = this.getOS();\n\n    if (toLowerCase) {\n      return String(name).toLowerCase() || '';\n    }\n\n    return name || '';\n  }\n\n  /**\n   * Get OS version\n   * @return {String} full version with dots ('10.11.12', '5.6', etc)\n   */\n  getOSVersion() {\n    return this.getOS().version;\n  }\n\n  /**\n   * Get parsed platform\n   * @return {{}}\n   */\n  getPlatform() {\n    if (this.parsedResult.platform) {\n      return this.parsedResult.platform;\n    }\n\n    return this.parsePlatform();\n  }\n\n  /**\n   * Get platform name\n   * @param {Boolean} [toLowerCase=false]\n   * @return {*}\n   */\n  getPlatformType(toLowerCase = false) {\n    const { type } = this.getPlatform();\n\n    if (toLowerCase) {\n      return String(type).toLowerCase() || '';\n    }\n\n    return type || '';\n  }\n\n  /**\n   * Get parsed platform\n   * @return {{}}\n   */\n  parsePlatform() {\n    this.parsedResult.platform = {};\n\n    const platform = Utils.find(platformParsersList, (_platform) => {\n      if (typeof _platform.test === 'function') {\n        return _platform.test(this);\n      }\n\n      if (_platform.test instanceof Array) {\n        return _platform.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (platform) {\n      this.parsedResult.platform = platform.describe(this.getUA());\n    }\n\n    return this.parsedResult.platform;\n  }\n\n  /**\n   * Get parsed engine\n   * @return {{}}\n   */\n  getEngine() {\n    if (this.parsedResult.engine) {\n      return this.parsedResult.engine;\n    }\n\n    return this.parseEngine();\n  }\n\n  /**\n   * Get engines's name\n   * @return {String} Engines's name or an empty string\n   *\n   * @public\n   */\n  getEngineName(toLowerCase) {\n    if (toLowerCase) {\n      return String(this.getEngine().name).toLowerCase() || '';\n    }\n    return this.getEngine().name || '';\n  }\n\n  /**\n   * Get parsed platform\n   * @return {{}}\n   */\n  parseEngine() {\n    this.parsedResult.engine = {};\n\n    const engine = Utils.find(enginesParsersList, (_engine) => {\n      if (typeof _engine.test === 'function') {\n        return _engine.test(this);\n      }\n\n      if (_engine.test instanceof Array) {\n        return _engine.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (engine) {\n      this.parsedResult.engine = engine.describe(this.getUA());\n    }\n\n    return this.parsedResult.engine;\n  }\n\n  /**\n   * Parse full information about the browser\n   * @returns {Parser}\n   */\n  parse() {\n    this.parseBrowser();\n    this.parseOS();\n    this.parsePlatform();\n    this.parseEngine();\n\n    return this;\n  }\n\n  /**\n   * Get parsed result\n   * @return {ParsedResult}\n   */\n  getResult() {\n    return Utils.assign({}, this.parsedResult);\n  }\n\n  /**\n   * Check if parsed browser matches certain conditions\n   *\n   * @param {Object} checkTree It's one or two layered object,\n   * which can include a platform or an OS on the first layer\n   * and should have browsers specs on the bottom-laying layer\n   *\n   * @returns {Boolean|undefined} Whether the browser satisfies the set conditions or not.\n   * Returns `undefined` when the browser is no described in the checkTree object.\n   *\n   * @example\n   * const browser = Bowser.getParser(window.navigator.userAgent);\n   * if (browser.satisfies({chrome: '>118.01.1322' }))\n   * // or with os\n   * if (browser.satisfies({windows: { chrome: '>118.01.1322' } }))\n   * // or with platforms\n   * if (browser.satisfies({desktop: { chrome: '>118.01.1322' } }))\n   */\n  satisfies(checkTree) {\n    const platformsAndOSes = {};\n    let platformsAndOSCounter = 0;\n    const browsers = {};\n    let browsersCounter = 0;\n\n    const allDefinitions = Object.keys(checkTree);\n\n    allDefinitions.forEach((key) => {\n      const currentDefinition = checkTree[key];\n      if (typeof currentDefinition === 'string') {\n        browsers[key] = currentDefinition;\n        browsersCounter += 1;\n      } else if (typeof currentDefinition === 'object') {\n        platformsAndOSes[key] = currentDefinition;\n        platformsAndOSCounter += 1;\n      }\n    });\n\n    if (platformsAndOSCounter > 0) {\n      const platformsAndOSNames = Object.keys(platformsAndOSes);\n      const OSMatchingDefinition = Utils.find(platformsAndOSNames, name => (this.isOS(name)));\n\n      if (OSMatchingDefinition) {\n        const osResult = this.satisfies(platformsAndOSes[OSMatchingDefinition]);\n\n        if (osResult !== void 0) {\n          return osResult;\n        }\n      }\n\n      const platformMatchingDefinition = Utils.find(\n        platformsAndOSNames,\n        name => (this.isPlatform(name)),\n      );\n      if (platformMatchingDefinition) {\n        const platformResult = this.satisfies(platformsAndOSes[platformMatchingDefinition]);\n\n        if (platformResult !== void 0) {\n          return platformResult;\n        }\n      }\n    }\n\n    if (browsersCounter > 0) {\n      const browserNames = Object.keys(browsers);\n      const matchingDefinition = Utils.find(browserNames, name => (this.isBrowser(name, true)));\n\n      if (matchingDefinition !== void 0) {\n        return this.compareVersion(browsers[matchingDefinition]);\n      }\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Check if the browser name equals the passed string\n   * @param browserName The string to compare with the browser name\n   * @param [includingAlias=false] The flag showing whether alias will be included into comparison\n   * @returns {boolean}\n   */\n  isBrowser(browserName, includingAlias = false) {\n    const defaultBrowserName = this.getBrowserName().toLowerCase();\n    let browserNameLower = browserName.toLowerCase();\n    const alias = Utils.getBrowserTypeByAlias(browserNameLower);\n\n    if (includingAlias && alias) {\n      browserNameLower = alias.toLowerCase();\n    }\n    return browserNameLower === defaultBrowserName;\n  }\n\n  compareVersion(version) {\n    let expectedResults = [0];\n    let comparableVersion = version;\n    let isLoose = false;\n\n    const currentBrowserVersion = this.getBrowserVersion();\n\n    if (typeof currentBrowserVersion !== 'string') {\n      return void 0;\n    }\n\n    if (version[0] === '>' || version[0] === '<') {\n      comparableVersion = version.substr(1);\n      if (version[1] === '=') {\n        isLoose = true;\n        comparableVersion = version.substr(2);\n      } else {\n        expectedResults = [];\n      }\n      if (version[0] === '>') {\n        expectedResults.push(1);\n      } else {\n        expectedResults.push(-1);\n      }\n    } else if (version[0] === '=') {\n      comparableVersion = version.substr(1);\n    } else if (version[0] === '~') {\n      isLoose = true;\n      comparableVersion = version.substr(1);\n    }\n\n    return expectedResults.indexOf(\n      Utils.compareVersions(currentBrowserVersion, comparableVersion, isLoose),\n    ) > -1;\n  }\n\n  isOS(osName) {\n    return this.getOSName(true) === String(osName).toLowerCase();\n  }\n\n  isPlatform(platformType) {\n    return this.getPlatformType(true) === String(platformType).toLowerCase();\n  }\n\n  isEngine(engineName) {\n    return this.getEngineName(true) === String(engineName).toLowerCase();\n  }\n\n  /**\n   * Is anything? Check if the browser is called \"anything\",\n   * the OS called \"anything\" or the platform called \"anything\"\n   * @param {String} anything\n   * @param [includingAlias=false] The flag showing whether alias will be included into comparison\n   * @returns {Boolean}\n   */\n  is(anything, includingAlias = false) {\n    return this.isBrowser(anything, includingAlias) || this.isOS(anything)\n      || this.isPlatform(anything);\n  }\n\n  /**\n   * Check if any of the given values satisfies this.is(anything)\n   * @param {String[]} anythings\n   * @returns {Boolean}\n   */\n  some(anythings = []) {\n    return anythings.some(anything => this.is(anything));\n  }\n}\n\nexport default Parser;\n", "/*!\n * Bowser - a browser detector\n * https://github.com/lancedikson/bowser\n * MIT License | (c) <PERSON> 2012-2015\n * MIT License | (c) <PERSON> 2015-2019\n */\nimport Parser from './parser.js';\nimport {\n  BROWSER_MAP,\n  ENGINE_MAP,\n  OS_MAP,\n  PLATFORMS_MAP,\n} from './constants.js';\n\n/**\n * Bowser class.\n * Keep it simple as much as it can be.\n * It's supposed to work with collections of {@link Parser} instances\n * rather then solve one-instance problems.\n * All the one-instance stuff is located in Parser class.\n *\n * @class\n * @classdesc Bowser is a static object, that provides an API to the Parsers\n * @hideconstructor\n */\nclass Bowser {\n  /**\n   * Creates a {@link Parser} instance\n   *\n   * @param {String} UA UserAgent string\n   * @param {Boolean} [skipParsing=false] Will make the Parser postpone parsing until you ask it\n   * explicitly. Same as `skipParsing` for {@link Parser}.\n   * @returns {Parser}\n   * @throws {Error} when UA is not a String\n   *\n   * @example\n   * const parser = Bowser.getParser(window.navigator.userAgent);\n   * const result = parser.getResult();\n   */\n  static getParser(UA, skipParsing = false) {\n    if (typeof UA !== 'string') {\n      throw new Error('UserAgent should be a string');\n    }\n    return new Parser(UA, skipParsing);\n  }\n\n  /**\n   * Creates a {@link Parser} instance and runs {@link Parser.getResult} immediately\n   *\n   * @param UA\n   * @return {ParsedResult}\n   *\n   * @example\n   * const result = Bowser.parse(window.navigator.userAgent);\n   */\n  static parse(UA) {\n    return (new Parser(UA)).getResult();\n  }\n\n  static get BROWSER_MAP() {\n    return BROWSER_MAP;\n  }\n\n  static get ENGINE_MAP() {\n    return ENGINE_MAP;\n  }\n\n  static get OS_MAP() {\n    return OS_MAP;\n  }\n\n  static get PLATFORMS_MAP() {\n    return PLATFORMS_MAP;\n  }\n}\n\nexport default Bowser;\n", "import bowser from \"bowser\";\nexport const createDefaultUserAgentProvider = ({ serviceId, clientVersion }) => async (config) => {\n    const parsedUA = typeof window !== \"undefined\" && window?.navigator?.userAgent\n        ? bowser.parse(window.navigator.userAgent)\n        : undefined;\n    const sections = [\n        [\"aws-sdk-js\", clientVersion],\n        [\"ua\", \"2.1\"],\n        [`os/${parsedUA?.os?.name || \"other\"}`, parsedUA?.os?.version],\n        [\"lang/js\"],\n        [\"md/browser\", `${parsedUA?.browser?.name ?? \"unknown\"}_${parsedUA?.browser?.version ?? \"unknown\"}`],\n    ];\n    if (serviceId) {\n        sections.push([`api/${serviceId}`, clientVersion]);\n    }\n    const appId = await config?.userAgentAppId?.();\n    if (appId) {\n        sections.push([`app/${appId}`]);\n    }\n    return sections;\n};\nexport const defaultUserAgent = createDefaultUserAgentProvider;\n", "export const invalidProvider = (message) => () => Promise.reject(message);\n", "export const invalidFunction = (message) => () => {\n    throw new Error(message);\n};\n", "export * from \"./invalidFunction\";\nexport * from \"./invalidProvider\";\n", "const TEXT_ENCODER = typeof TextEncoder == \"function\" ? new TextEncoder() : null;\nexport const calculateBodyLength = (body) => {\n    if (typeof body === \"string\") {\n        if (TEXT_ENCODER) {\n            return TEXT_ENCODER.encode(body).byteLength;\n        }\n        let len = body.length;\n        for (let i = len - 1; i >= 0; i--) {\n            const code = body.charCodeAt(i);\n            if (code > 0x7f && code <= 0x7ff)\n                len++;\n            else if (code > 0x7ff && code <= 0xffff)\n                len += 2;\n            if (code >= 0xdc00 && code <= 0xdfff)\n                i--;\n        }\n        return len;\n    }\n    else if (typeof body.byteLength === \"number\") {\n        return body.byteLength;\n    }\n    else if (typeof body.size === \"number\") {\n        return body.size;\n    }\n    throw new Error(`Body Length computation failed for ${body}`);\n};\n", "export * from \"./calculateBodyLength\";\n", "export const DEFAULTS_MODE_OPTIONS = [\"in-region\", \"cross-region\", \"mobile\", \"standard\", \"legacy\"];\n", "import { memoize } from \"@smithy/property-provider\";\nimport bowser from \"bowser\";\nimport { DEFAULTS_MODE_OPTIONS } from \"./constants\";\nexport const resolveDefaultsModeConfig = ({ defaultsMode, } = {}) => memoize(async () => {\n    const mode = typeof defaultsMode === \"function\" ? await defaultsMode() : defaultsMode;\n    switch (mode?.toLowerCase()) {\n        case \"auto\":\n            return Promise.resolve(isMobileBrowser() ? \"mobile\" : \"standard\");\n        case \"mobile\":\n        case \"in-region\":\n        case \"cross-region\":\n        case \"standard\":\n        case \"legacy\":\n            return Promise.resolve(mode?.toLocaleLowerCase());\n        case undefined:\n            return Promise.resolve(\"legacy\");\n        default:\n            throw new Error(`Invalid parameter for \"defaultsMode\", expect ${DEFAULTS_MODE_OPTIONS.join(\", \")}, got ${mode}`);\n    }\n});\nconst isMobileBrowser = () => {\n    const parsedUA = typeof window !== \"undefined\" && window?.navigator?.userAgent\n        ? bowser.parse(window.navigator.userAgent)\n        : undefined;\n    const platform = parsedUA?.platform?.type;\n    return platform === \"tablet\" || platform === \"mobile\";\n};\n", "export * from \"./resolveDefaultsModeConfig\";\n", "export const getAwsRegionExtensionConfiguration = (runtimeConfig) => {\n    return {\n        setRegion(region) {\n            runtimeConfig.region = region;\n        },\n        region() {\n            return runtimeConfig.region;\n        },\n    };\n};\nexport const resolveAwsRegionExtensionConfiguration = (awsRegionExtensionConfiguration) => {\n    return {\n        region: awsRegionExtensionConfiguration.region(),\n    };\n};\n", "export const REGION_ENV_NAME = \"AWS_REGION\";\nexport const REGION_INI_NAME = \"region\";\nexport const NODE_REGION_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[REGION_ENV_NAME],\n    configFileSelector: (profile) => profile[REGION_INI_NAME],\n    default: () => {\n        throw new Error(\"Region is missing\");\n    },\n};\nexport const NODE_REGION_CONFIG_FILE_OPTIONS = {\n    preferredFile: \"credentials\",\n};\n", "export const isFipsRegion = (region) => typeof region === \"string\" && (region.startsWith(\"fips-\") || region.endsWith(\"-fips\"));\n", "import { isFipsRegion } from \"./isFipsRegion\";\nexport const getRealRegion = (region) => isFipsRegion(region)\n    ? [\"fips-aws-global\", \"aws-fips\"].includes(region)\n        ? \"us-east-1\"\n        : region.replace(/fips-(dkr-|prod-)?|-fips/, \"\")\n    : region;\n", "import { getRealRegion } from \"./getRealRegion\";\nimport { isFipsRegion } from \"./isFipsRegion\";\nexport const resolveRegionConfig = (input) => {\n    const { region, useFipsEndpoint } = input;\n    if (!region) {\n        throw new Error(\"Region is missing\");\n    }\n    return Object.assign(input, {\n        region: async () => {\n            if (typeof region === \"string\") {\n                return getRealRegion(region);\n            }\n            const providedRegion = await region();\n            return getRealRegion(providedRegion);\n        },\n        useFipsEndpoint: async () => {\n            const providedRegion = typeof region === \"string\" ? region : await region();\n            if (isFipsRegion(providedRegion)) {\n                return true;\n            }\n            return typeof useFipsEndpoint !== \"function\" ? Promise.resolve(!!useFipsEndpoint) : useFipsEndpoint();\n        },\n    });\n};\n", "export * from \"./config\";\nexport * from \"./resolveRegionConfig\";\n", "export * from \"./extensions\";\nexport * from \"./regionConfig\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,SAAS,wBAAwB,OAAO;AAC3C,SAAO;AACX;AAHA,IAIa,sBAiBA,6BAOA;AA5Bb,IAAAA,iBAAA;AAAA;AAAA,IAAAA;AAIO,IAAM,uBAAuB,CAAC,YAAY,CAAC,SAAS,OAAO,SAAS;AACvE,UAAI,CAAC,YAAY,WAAW,KAAK,OAAO;AACpC,eAAO,KAAK,IAAI;AACpB,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,EAAE,kBAAkB,GAAG,IAAI,QAAQ,eAAe,YAAY,CAAC;AACrE,UAAI,gBAAgB,QAAQ,IAAI,KAAK,KAAK,CAAC,QAAQ,QAAQ,YAAY,GAAG;AACtE,eAAO,QAAQ,QAAQ,MAAM;AAC7B,gBAAQ,QAAQ,YAAY,IAAI,QAAQ,YAAY,QAAQ,OAAO,MAAM,QAAQ,OAAO;AAAA,MAC5F,WACS,CAAC,QAAQ,QAAQ,MAAM,GAAG;AAC/B,YAAI,OAAO,QAAQ;AACnB,YAAI,QAAQ,QAAQ;AAChB,kBAAQ,IAAI,QAAQ,IAAI;AAC5B,gBAAQ,QAAQ,MAAM,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK,IAAI;AAAA,IACpB;AACO,IAAM,8BAA8B;AAAA,MACvC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,MAAM,CAAC,MAAM;AAAA,MACb,UAAU;AAAA,IACd;AACO,IAAM,sBAAsB,CAAC,aAAa;AAAA,MAC7C,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,IAAI,qBAAqB,OAAO,GAAG,2BAA2B;AAAA,MAC9E;AAAA,IACJ;AAAA;AAAA;;;AChCA,IAAa,kBA+BA,yBAMA;AArCb;AAAA;AAAO,IAAM,mBAAmB,MAAM,CAAC,MAAM,YAAY,OAAO,SAAS;AAAzE;AACI,UAAI;AACA,cAAM,WAAW,MAAM,KAAK,IAAI;AAChC,cAAM,EAAE,YAAY,aAAa,QAAQ,gCAAgC,CAAC,EAAE,IAAI;AAChF,cAAM,EAAE,iCAAiC,iCAAiC,IAAI;AAC9E,cAAM,0BAA0B,mCAAmC,QAAQ;AAC3E,cAAM,2BAA2B,oCAAoC,QAAQ;AAC7E,cAAM,EAAE,WAAW,GAAG,sBAAsB,IAAI,SAAS;AACzD,+CAAQ,SAAR,gCAAe;AAAA,UACX;AAAA,UACA;AAAA,UACA,OAAO,wBAAwB,KAAK,KAAK;AAAA,UACzC,QAAQ,yBAAyB,qBAAqB;AAAA,UACtD,UAAU;AAAA,QACd;AACA,eAAO;AAAA,MACX,SACO,OAAO;AACV,cAAM,EAAE,YAAY,aAAa,QAAQ,gCAAgC,CAAC,EAAE,IAAI;AAChF,cAAM,EAAE,gCAAgC,IAAI;AAC5C,cAAM,0BAA0B,mCAAmC,QAAQ;AAC3E,+CAAQ,UAAR,gCAAgB;AAAA,UACZ;AAAA,UACA;AAAA,UACA,OAAO,wBAAwB,KAAK,KAAK;AAAA,UACzC;AAAA,UACA,UAAU,MAAM;AAAA,QACpB;AACA,cAAM;AAAA,MACV;AAAA,IACJ;AACO,IAAM,0BAA0B;AAAA,MACnC,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,MACf,MAAM;AAAA,MACN,UAAU;AAAA,IACd;AACO,IAAM,kBAAkB,CAAC,aAAa;AAAA,MACzC,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,IAAI,iBAAiB,GAAG,uBAAuB;AAAA,MAC/D;AAAA,IACJ;AAAA;AAAA;;;ACzCA,IAAAC,iBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IACM,sBACA,0BACA,cACO,8BAqBA,wCAOA;AAhCb,IAAAC,iBAAA;AAAA;AAAA,IAAAA;AACA,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,IAAM,eAAe;AACd,IAAM,+BAA+B,CAAC,YAAY,CAAC,SAAS,OAAO,SAAS;AAC/E,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,CAAC,YAAY,WAAW,OAAO,KAAK,QAAQ,YAAY,QAAQ;AAChE,eAAO,KAAK,IAAI;AAAA,MACpB;AACA,YAAM,gBAAgB,OAAO,KAAK,QAAQ,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,YAAY,MAAM,qBAAqB,YAAY,CAAC,KACvH;AACJ,UAAI,QAAQ,QAAQ,eAAe,aAAa,GAAG;AAC/C,eAAO,KAAK,IAAI;AAAA,MACpB;AACA,YAAM,eAAe,QAAQ,IAAI,wBAAwB;AACzD,YAAM,UAAU,QAAQ,IAAI,YAAY;AACxC,YAAM,iBAAiB,CAAC,QAAQ,OAAO,QAAQ,YAAY,IAAI,SAAS;AACxE,UAAI,eAAe,YAAY,KAAK,eAAe,OAAO,GAAG;AACzD,gBAAQ,QAAQ,oBAAoB,IAAI;AAAA,MAC5C;AACA,aAAO,KAAK;AAAA,QACR,GAAG;AAAA,QACH;AAAA,MACJ,CAAC;AAAA,IACL;AACO,IAAM,yCAAyC;AAAA,MAClD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,MAC5B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AACO,IAAM,8BAA8B,CAAC,aAAa;AAAA,MACrD,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,IAAI,6BAA6B,OAAO,GAAG,sCAAsC;AAAA,MACjG;AAAA,IACJ;AAAA;AAAA;;;AClCA,SAAS,sBAAsB,OAAO;AAClC,MAAI,UAAU,QAAW;AACrB,WAAO;AAAA,EACX;AACA,SAAO,OAAO,UAAU,YAAY,MAAM,UAAU;AACxD;AACO,SAAS,uBAAuB,OAAO;AAC1C,QAAM,0BAA0BC,mBAAkB,MAAM,kBAAkB,iBAAiB;AAC3F,QAAM,EAAE,gBAAgB,IAAI;AAC5B,SAAO,OAAO,OAAO,OAAO;AAAA,IACxB,iBAAiB,OAAO,oBAAoB,WAAW,CAAC,CAAC,eAAe,CAAC,IAAI;AAAA,IAC7E,gBAAgB,YAAY;AAbpC;AAcY,YAAM,QAAQ,MAAM,wBAAwB;AAC5C,UAAI,CAAC,sBAAsB,KAAK,GAAG;AAC/B,cAAM,WAAS,iBAAM,WAAN,mBAAc,gBAAd,mBAA2B,UAAS,gBAAgB,CAAC,MAAM,SAAS,UAAU,MAAM;AACnG,YAAI,OAAO,UAAU,UAAU;AAC3B,2CAAQ,KAAK;AAAA,QACjB,WACS,MAAM,SAAS,IAAI;AACxB,2CAAQ,KAAK;AAAA,QACjB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AA3BA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,oBAAoB;AAAA;AAAA;;;ACDjC,IAAa;AAAb;AAAA;AAAO,IAAM,gBAAN,MAAoB;AAAA,MACvB,YAAY,EAAE,MAAM,OAAO,GAAG;AAC1B,aAAK,OAAO,oBAAI,IAAI;AACpB,aAAK,aAAa,CAAC;AACnB,aAAK,WAAW,QAAQ;AACxB,YAAI,QAAQ;AACR,eAAK,aAAa;AAAA,QACtB;AAAA,MACJ;AAAA,MACA,IAAI,gBAAgB,UAAU;AAC1B,cAAM,MAAM,KAAK,KAAK,cAAc;AACpC,YAAI,QAAQ,OAAO;AACf,iBAAO,SAAS;AAAA,QACpB;AACA,YAAI,CAAC,KAAK,KAAK,IAAI,GAAG,GAAG;AACrB,cAAI,KAAK,KAAK,OAAO,KAAK,WAAW,IAAI;AACrC,kBAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,gBAAI,IAAI;AACR,mBAAO,MAAM;AACT,oBAAM,EAAE,OAAO,KAAK,IAAI,KAAK,KAAK;AAClC,mBAAK,KAAK,OAAO,KAAK;AACtB,kBAAI,QAAQ,EAAE,IAAI,IAAI;AAClB;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,eAAK,KAAK,IAAI,KAAK,SAAS,CAAC;AAAA,QACjC;AACA,eAAO,KAAK,KAAK,IAAI,GAAG;AAAA,MAC5B;AAAA,MACA,OAAO;AACH,eAAO,KAAK,KAAK;AAAA,MACrB;AAAA,MACA,KAAK,gBAAgB;AACjB,YAAI,SAAS;AACb,cAAM,EAAE,WAAW,IAAI;AACvB,YAAI,WAAW,WAAW,GAAG;AACzB,iBAAO;AAAA,QACX;AACA,mBAAW,SAAS,YAAY;AAC5B,gBAAMC,OAAM,OAAO,eAAe,KAAK,KAAK,EAAE;AAC9C,cAAIA,KAAI,SAAS,IAAI,GAAG;AACpB,mBAAO;AAAA,UACX;AACA,oBAAUA,OAAM;AAAA,QACpB;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA;AAAA;;;AChDA,IAAM,aACO;AADb;AAAA;AAAA,IAAM,cAAc,IAAI,OAAO,kGAAkG;AAC1H,IAAM,cAAc,CAAC,UAAU,YAAY,KAAK,KAAK,KAAM,MAAM,WAAW,GAAG,KAAK,MAAM,SAAS,GAAG;AAAA;AAAA;;;ACD7G,IAAM,wBACO;AADb;AAAA;AAAA,IAAM,yBAAyB,IAAI,OAAO,mCAAmC;AACtE,IAAM,mBAAmB,CAAC,OAAO,kBAAkB,UAAU;AAChE,UAAI,CAAC,iBAAiB;AAClB,eAAO,uBAAuB,KAAK,KAAK;AAAA,MAC5C;AACA,YAAM,SAAS,MAAM,MAAM,GAAG;AAC9B,iBAAW,SAAS,QAAQ;AACxB,YAAI,CAAC,iBAAiB,KAAK,GAAG;AAC1B,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACZA,IAAa;AAAb;AAAA;AAAO,IAAM,0BAA0B,CAAC;AAAA;AAAA;;;ACAxC,IAAa;AAAb;AAAA;AAAO,IAAM,UAAU;AAAA;AAAA;;;ACAhB,SAAS,cAAc,OAAO;AACjC,MAAI,OAAO,UAAU,YAAY,SAAS,MAAM;AAC5C,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO;AAChB,WAAO,IAAI,cAAc,MAAM,GAAG,CAAC;AAAA,EACvC;AACA,MAAI,QAAQ,OAAO;AACf,WAAO,GAAG,MAAM,EAAE,KAAK,MAAM,QAAQ,CAAC,GAAG,IAAI,aAAa,EAAE,KAAK,IAAI,CAAC;AAAA,EAC1E;AACA,SAAO,KAAK,UAAU,OAAO,MAAM,CAAC;AACxC;AAXA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAAa;AAAb;AAAA;AAAO,IAAM,gBAAN,cAA4B,MAAM;AAAA,MACrC,YAAY,SAAS;AACjB,cAAM,OAAO;AACb,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AAAA;AAAA;;;ACLA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACNA,IAAa;AAAb;AAAA;AAAO,IAAM,gBAAgB,CAAC,QAAQ,WAAW,WAAW;AAAA;AAAA;;;ACA5D,IACa;AADb;AAAA;AAAA;AACO,IAAM,kBAAkB,CAAC,SAAS;AACrC,YAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,YAAM,WAAW,CAAC;AAClB,iBAAW,QAAQ,OAAO;AACtB,cAAM,qBAAqB,KAAK,QAAQ,GAAG;AAC3C,YAAI,uBAAuB,IAAI;AAC3B,cAAI,KAAK,QAAQ,GAAG,MAAM,KAAK,SAAS,GAAG;AACvC,kBAAM,IAAI,cAAc,UAAU,IAAI,yBAAyB;AAAA,UACnE;AACA,gBAAM,aAAa,KAAK,MAAM,qBAAqB,GAAG,EAAE;AACxD,cAAI,OAAO,MAAM,SAAS,UAAU,CAAC,GAAG;AACpC,kBAAM,IAAI,cAAc,yBAAyB,UAAU,eAAe,IAAI,GAAG;AAAA,UACrF;AACA,cAAI,uBAAuB,GAAG;AAC1B,qBAAS,KAAK,KAAK,MAAM,GAAG,kBAAkB,CAAC;AAAA,UACnD;AACA,mBAAS,KAAK,UAAU;AAAA,QAC5B,OACK;AACD,mBAAS,KAAK,IAAI;AAAA,QACtB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACxBA,IAEa;AAFb;AAAA;AAAA;AACA;AACO,IAAM,UAAU,CAAC,OAAO,SAAS,gBAAgB,IAAI,EAAE,OAAO,CAAC,KAAK,UAAU;AACjF,UAAI,OAAO,QAAQ,UAAU;AACzB,cAAM,IAAI,cAAc,UAAU,KAAK,SAAS,IAAI,mBAAmB,KAAK,UAAU,KAAK,CAAC,GAAG;AAAA,MACnG,WACS,MAAM,QAAQ,GAAG,GAAG;AACzB,eAAO,IAAI,SAAS,KAAK,CAAC;AAAA,MAC9B;AACA,aAAO,IAAI,KAAK;AAAA,IACpB,GAAG,KAAK;AAAA;AAAA;;;ACVR,IAAa;AAAb;AAAA;AAAO,IAAM,QAAQ,CAAC,UAAU,SAAS;AAAA;AAAA;;;ACAzC,IAAa;AAAb;AAAA;AAAO,IAAM,MAAM,CAAC,UAAU,CAAC;AAAA;AAAA;;;ACA/B,IAEM,eAIO;AANb;AAAA;AAAA,IAAAC;AACA;AACA,IAAM,gBAAgB;AAAA,MAClB,CAAC,kBAAkB,IAAI,GAAG;AAAA,MAC1B,CAAC,kBAAkB,KAAK,GAAG;AAAA,IAC/B;AACO,IAAM,WAAW,CAAC,UAAU;AAC/B,YAAM,aAAa,MAAM;AACrB,YAAI;AACA,cAAI,iBAAiB,KAAK;AACtB,mBAAO;AAAA,UACX;AACA,cAAI,OAAO,UAAU,YAAY,cAAc,OAAO;AAClD,kBAAM,EAAE,UAAAC,WAAU,MAAM,UAAAC,YAAW,IAAI,OAAO,IAAI,QAAQ,CAAC,EAAE,IAAI;AACjE,kBAAM,MAAM,IAAI,IAAI,GAAGA,SAAQ,KAAKD,SAAQ,GAAG,OAAO,IAAI,IAAI,KAAK,EAAE,GAAG,IAAI,EAAE;AAC9E,gBAAI,SAAS,OAAO,QAAQ,KAAK,EAC5B,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,EAC3B,KAAK,GAAG;AACb,mBAAO;AAAA,UACX;AACA,iBAAO,IAAI,IAAI,KAAK;AAAA,QACxB,SACO,OAAO;AACV,iBAAO;AAAA,QACX;AAAA,MACJ,GAAG;AACH,UAAI,CAAC,WAAW;AACZ,gBAAQ,MAAM,mBAAmB,KAAK,UAAU,KAAK,CAAC,mBAAmB;AACzE,eAAO;AAAA,MACX;AACA,YAAM,YAAY,UAAU;AAC5B,YAAM,EAAE,MAAM,UAAU,UAAU,UAAU,OAAO,IAAI;AACvD,UAAI,QAAQ;AACR,eAAO;AAAA,MACX;AACA,YAAM,SAAS,SAAS,MAAM,GAAG,EAAE;AACnC,UAAI,CAAC,OAAO,OAAO,iBAAiB,EAAE,SAAS,MAAM,GAAG;AACpD,eAAO;AAAA,MACX;AACA,YAAM,OAAO,YAAY,QAAQ;AACjC,YAAM,2BAA2B,UAAU,SAAS,GAAG,IAAI,IAAI,cAAc,MAAM,CAAC,EAAE,KACjF,OAAO,UAAU,YAAY,MAAM,SAAS,GAAG,IAAI,IAAI,cAAc,MAAM,CAAC,EAAE;AACnF,YAAM,YAAY,GAAG,IAAI,GAAG,2BAA2B,IAAI,cAAc,MAAM,CAAC,KAAK,EAAE;AACvF,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,gBAAgB,SAAS,SAAS,GAAG,IAAI,WAAW,GAAG,QAAQ;AAAA,QAC/D;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AClDA,IAAa;AAAb;AAAA;AAAO,IAAM,eAAe,CAAC,QAAQ,WAAW,WAAW;AAAA;AAAA;;;ACA3D,IAAa;AAAb;AAAA;AAAO,IAAM,YAAY,CAAC,OAAO,OAAO,MAAM,YAAY;AACtD,UAAI,SAAS,QAAQ,MAAM,SAAS,MAAM;AACtC,eAAO;AAAA,MACX;AACA,UAAI,CAAC,SAAS;AACV,eAAO,MAAM,UAAU,OAAO,IAAI;AAAA,MACtC;AACA,aAAO,MAAM,UAAU,MAAM,SAAS,MAAM,MAAM,SAAS,KAAK;AAAA,IACpE;AAAA;AAAA;;;ACRA,IAAa;AAAb;AAAA;AAAO,IAAM,YAAY,CAAC,UAAU,mBAAmB,KAAK,EAAE,QAAQ,YAAY,CAAC,MAAM,IAAI,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC,EAAE;AAAA;AAAA;;;ACAzI;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACRA,IACa;AADb;AAAA;AAAA;AACO,IAAM,oBAAoB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA;AAAA;;;ACXA,IACa;AADb;AAAA;AAAA;AACO,IAAM,mBAAmB,CAAC,UAAU,YAAY;AACnD,YAAM,uBAAuB,CAAC;AAC9B,YAAM,kBAAkB;AAAA,QACpB,GAAG,QAAQ;AAAA,QACX,GAAG,QAAQ;AAAA,MACf;AACA,UAAI,eAAe;AACnB,aAAO,eAAe,SAAS,QAAQ;AACnC,cAAM,oBAAoB,SAAS,QAAQ,KAAK,YAAY;AAC5D,YAAI,sBAAsB,IAAI;AAC1B,+BAAqB,KAAK,SAAS,MAAM,YAAY,CAAC;AACtD;AAAA,QACJ;AACA,6BAAqB,KAAK,SAAS,MAAM,cAAc,iBAAiB,CAAC;AACzE,cAAM,oBAAoB,SAAS,QAAQ,KAAK,iBAAiB;AACjE,YAAI,sBAAsB,IAAI;AAC1B,+BAAqB,KAAK,SAAS,MAAM,iBAAiB,CAAC;AAC3D;AAAA,QACJ;AACA,YAAI,SAAS,oBAAoB,CAAC,MAAM,OAAO,SAAS,oBAAoB,CAAC,MAAM,KAAK;AACpF,+BAAqB,KAAK,SAAS,MAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAClF,yBAAe,oBAAoB;AAAA,QACvC;AACA,cAAM,gBAAgB,SAAS,UAAU,oBAAoB,GAAG,iBAAiB;AACjF,YAAI,cAAc,SAAS,GAAG,GAAG;AAC7B,gBAAM,CAAC,SAAS,QAAQ,IAAI,cAAc,MAAM,GAAG;AACnD,+BAAqB,KAAK,QAAQ,gBAAgB,OAAO,GAAG,QAAQ,CAAC;AAAA,QACzE,OACK;AACD,+BAAqB,KAAK,gBAAgB,aAAa,CAAC;AAAA,QAC5D;AACA,uBAAe,oBAAoB;AAAA,MACvC;AACA,aAAO,qBAAqB,KAAK,EAAE;AAAA,IACvC;AAAA;AAAA;;;ACnCA,IAAa;AAAb;AAAA;AAAO,IAAM,oBAAoB,CAAC,EAAE,IAAI,GAAG,YAAY;AACnD,YAAM,kBAAkB;AAAA,QACpB,GAAG,QAAQ;AAAA,QACX,GAAG,QAAQ;AAAA,MACf;AACA,aAAO,gBAAgB,GAAG;AAAA,IAC9B;AAAA;AAAA;;;ACNA,IAIa;AAJb;AAAA;AAAA;AACA;AACA;AACA;AACO,IAAM,qBAAqB,CAAC,KAAK,SAAS,YAAY;AACzD,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,iBAAiB,KAAK,OAAO;AAAA,MACxC,WACS,IAAI,IAAI,GAAG;AAChB,eAAO,aAAa,KAAK,OAAO;AAAA,MACpC,WACS,IAAI,KAAK,GAAG;AACjB,eAAO,kBAAkB,KAAK,OAAO;AAAA,MACzC;AACA,YAAM,IAAI,cAAc,IAAI,OAAO,MAAM,OAAO,GAAG,CAAC,0CAA0C;AAAA,IAClG;AAAA;AAAA;;;ACfA,IAGa;AAHb;AAAA;AAAA;AACA;AACA;AACO,IAAM,eAAe,CAAC,EAAE,IAAI,KAAK,GAAG,YAAY;AACnD,YAAM,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,QAAQ,EAAE,SAAS,OAAO,GAAG,IAAI,MAAM,mBAAmB,KAAK,OAAO,OAAO,CAAC;AAClI,YAAM,aAAa,GAAG,MAAM,GAAG;AAC/B,UAAI,WAAW,CAAC,KAAK,2BAA2B,WAAW,CAAC,KAAK,MAAM;AACnE,eAAO,wBAAwB,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,aAAa;AAAA,MACjF;AACA,aAAO,kBAAkB,EAAE,EAAE,GAAG,aAAa;AAAA,IACjD;AAAA;AAAA;;;ACVA,IAGa;AAHb;AAAA;AAAA;AACA;AACA;AACO,IAAM,oBAAoB,CAAC,EAAE,QAAQ,GAAG,OAAO,GAAG,YAAY;AAHrE;AAII,UAAI,UAAU,UAAU,QAAQ,iBAAiB;AAC7C,cAAM,IAAI,cAAc,IAAI,MAAM,2CAA2C;AAAA,MACjF;AACA,YAAM,QAAQ,aAAa,QAAQ,OAAO;AAC1C,0BAAQ,WAAR,mBAAgB,UAAhB,4BAAwB,GAAG,OAAO,uBAAuB,cAAc,MAAM,CAAC,MAAM,cAAc,KAAK,CAAC;AACxG,aAAO;AAAA,QACH,QAAQ,UAAU,KAAK,OAAO,CAAC,CAAC;AAAA,QAChC,GAAI,UAAU,QAAQ,EAAE,UAAU,EAAE,MAAM,QAAQ,MAAM,EAAE;AAAA,MAC9D;AAAA,IACJ;AAAA;AAAA;;;ACbA,IAEa;AAFb;AAAA;AAAA;AACA;AACO,IAAM,qBAAqB,CAAC,aAAa,CAAC,GAAG,YAAY;AAFhE;AAGI,YAAM,4BAA4B,CAAC;AACnC,iBAAW,aAAa,YAAY;AAChC,cAAM,EAAE,QAAQ,SAAS,IAAI,kBAAkB,WAAW;AAAA,UACtD,GAAG;AAAA,UACH,iBAAiB;AAAA,YACb,GAAG,QAAQ;AAAA,YACX,GAAG;AAAA,UACP;AAAA,QACJ,CAAC;AACD,YAAI,CAAC,QAAQ;AACT,iBAAO,EAAE,OAAO;AAAA,QACpB;AACA,YAAI,UAAU;AACV,oCAA0B,SAAS,IAAI,IAAI,SAAS;AACpD,8BAAQ,WAAR,mBAAgB,UAAhB,4BAAwB,GAAG,OAAO,YAAY,SAAS,IAAI,OAAO,cAAc,SAAS,KAAK,CAAC;AAAA,QACnG;AAAA,MACJ;AACA,aAAO,EAAE,QAAQ,MAAM,iBAAiB,0BAA0B;AAAA,IACtE;AAAA;AAAA;;;ACrBA,IAEa;AAFb;AAAA;AAAA;AACA;AACO,IAAM,qBAAqB,CAAC,SAAS,YAAY,OAAO,QAAQ,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,WAAW,SAAS,OAAO;AAAA,MACrH,GAAG;AAAA,MACH,CAAC,SAAS,GAAG,UAAU,IAAI,CAAC,mBAAmB;AAC3C,cAAM,gBAAgB,mBAAmB,gBAAgB,sBAAsB,OAAO;AACtF,YAAI,OAAO,kBAAkB,UAAU;AACnC,gBAAM,IAAI,cAAc,WAAW,SAAS,YAAY,aAAa,mBAAmB;AAAA,QAC5F;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL,IAAI,CAAC,CAAC;AAAA;AAAA;;;ACXN,IAGa;AAHb;AAAA;AAAA;AACA;AACA;AACO,IAAM,sBAAsB,CAAC,UAAU,YAAY;AACtD,UAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,eAAO,SAAS,IAAI,CAAC,kBAAkB,oBAAoB,eAAe,OAAO,CAAC;AAAA,MACtF;AACA,cAAQ,OAAO,UAAU;AAAA,QACrB,KAAK;AACD,iBAAO,iBAAiB,UAAU,OAAO;AAAA,QAC7C,KAAK;AACD,cAAI,aAAa,MAAM;AACnB,kBAAM,IAAI,cAAc,iCAAiC,QAAQ,EAAE;AAAA,UACvE;AACA,iBAAO,sBAAsB,UAAU,OAAO;AAAA,QAClD,KAAK;AACD,iBAAO;AAAA,QACX;AACI,gBAAM,IAAI,cAAc,sCAAsC,OAAO,QAAQ,EAAE;AAAA,MACvF;AAAA,IACJ;AAAA;AAAA;;;ACpBA,IACa;AADb;AAAA;AAAA;AACO,IAAM,wBAAwB,CAAC,YAAY,YAAY,OAAO,QAAQ,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,aAAa,WAAW,OAAO;AAAA,MAClI,GAAG;AAAA,MACH,CAAC,WAAW,GAAG,oBAAoB,aAAa,OAAO;AAAA,IAC3D,IAAI,CAAC,CAAC;AAAA;AAAA;;;ACJN,IAEa;AAFb;AAAA;AAAA;AACA;AACO,IAAM,iBAAiB,CAAC,aAAa,YAAY;AACpD,YAAM,aAAa,mBAAmB,aAAa,gBAAgB,OAAO;AAC1E,UAAI,OAAO,eAAe,UAAU;AAChC,YAAI;AACA,iBAAO,IAAI,IAAI,UAAU;AAAA,QAC7B,SACO,OAAO;AACV,kBAAQ,MAAM,gCAAgC,UAAU,IAAI,KAAK;AACjE,gBAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,IAAI,cAAc,sCAAsC,OAAO,UAAU,EAAE;AAAA,IACrF;AAAA;AAAA;;;ACdA,IAKa;AALb;AAAA;AAAA;AACA;AACA;AACA;AACA;AACO,IAAM,uBAAuB,CAAC,cAAc,YAAY;AAL/D;AAMI,YAAM,EAAE,YAAY,SAAS,IAAI;AACjC,YAAM,EAAE,QAAQ,gBAAgB,IAAI,mBAAmB,YAAY,OAAO;AAC1E,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,YAAM,sBAAsB;AAAA,QACxB,GAAG;AAAA,QACH,iBAAiB,EAAE,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB;AAAA,MACtE;AACA,YAAM,EAAE,KAAK,YAAY,QAAQ,IAAI;AACrC,0BAAQ,WAAR,mBAAgB,UAAhB,4BAAwB,GAAG,OAAO,sCAAsC,cAAc,QAAQ,CAAC;AAC/F,aAAO;AAAA,QACH,GAAI,WAAW,UAAa;AAAA,UACxB,SAAS,mBAAmB,SAAS,mBAAmB;AAAA,QAC5D;AAAA,QACA,GAAI,cAAc,UAAa;AAAA,UAC3B,YAAY,sBAAsB,YAAY,mBAAmB;AAAA,QACrE;AAAA,QACA,KAAK,eAAe,KAAK,mBAAmB;AAAA,MAChD;AAAA,IACJ;AAAA;AAAA;;;AC1BA,IAGa;AAHb;AAAA;AAAA;AACA;AACA;AACO,IAAM,oBAAoB,CAAC,WAAW,YAAY;AACrD,YAAM,EAAE,YAAY,MAAM,IAAI;AAC9B,YAAM,EAAE,QAAQ,gBAAgB,IAAI,mBAAmB,YAAY,OAAO;AAC1E,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,YAAM,IAAI,cAAc,mBAAmB,OAAO,SAAS;AAAA,QACvD,GAAG;AAAA,QACH,iBAAiB,EAAE,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB;AAAA,MACtE,CAAC,CAAC;AAAA,IACN;AAAA;AAAA;;;ACbA,IAEa;AAFb;AAAA;AAAA;AACA;AACO,IAAM,mBAAmB,CAAC,UAAU,YAAY;AACnD,YAAM,EAAE,YAAY,MAAM,IAAI;AAC9B,YAAM,EAAE,QAAQ,gBAAgB,IAAI,mBAAmB,YAAY,OAAO;AAC1E,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,aAAO,cAAc,OAAO;AAAA,QACxB,GAAG;AAAA,QACH,iBAAiB,EAAE,GAAG,QAAQ,iBAAiB,GAAG,gBAAgB;AAAA,MACtE,CAAC;AAAA,IACL;AAAA;AAAA;;;ACZA,IAIa;AAJb;AAAA;AAAA;AACA;AACA;AACA;AACO,IAAM,gBAAgB,CAAC,OAAO,YAAY;AAC7C,iBAAW,QAAQ,OAAO;AACtB,YAAI,KAAK,SAAS,YAAY;AAC1B,gBAAM,sBAAsB,qBAAqB,MAAM,OAAO;AAC9D,cAAI,qBAAqB;AACrB,mBAAO;AAAA,UACX;AAAA,QACJ,WACS,KAAK,SAAS,SAAS;AAC5B,4BAAkB,MAAM,OAAO;AAAA,QACnC,WACS,KAAK,SAAS,QAAQ;AAC3B,gBAAM,sBAAsB,iBAAiB,MAAM,OAAO;AAC1D,cAAI,qBAAqB;AACrB,mBAAO;AAAA,UACX;AAAA,QACJ,OACK;AACD,gBAAM,IAAI,cAAc,0BAA0B,IAAI,EAAE;AAAA,QAC5D;AAAA,MACJ;AACA,YAAM,IAAI,cAAc,yBAAyB;AAAA,IACrD;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAGa;AAHb;AAAA;AAAA;AACA;AACA;AACO,IAAM,kBAAkB,CAAC,eAAe,YAAY;AAH3D;AAII,YAAM,EAAE,gBAAgB,OAAO,IAAI;AACnC,YAAM,EAAE,YAAY,MAAM,IAAI;AAC9B,0BAAQ,WAAR,mBAAgB,UAAhB,4BAAwB,GAAG,OAAO,4BAA4B,cAAc,cAAc,CAAC;AAC3F,YAAM,oBAAoB,OAAO,QAAQ,UAAU,EAC9C,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,WAAW,IAAI,EACnC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC;AACnC,UAAI,kBAAkB,SAAS,GAAG;AAC9B,mBAAW,CAAC,UAAU,iBAAiB,KAAK,mBAAmB;AAC3D,yBAAe,QAAQ,IAAI,eAAe,QAAQ,KAAK;AAAA,QAC3D;AAAA,MACJ;AACA,YAAM,iBAAiB,OAAO,QAAQ,UAAU,EAC3C,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,EAC5B,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AACnB,iBAAW,iBAAiB,gBAAgB;AACxC,YAAI,eAAe,aAAa,KAAK,MAAM;AACvC,gBAAM,IAAI,cAAc,gCAAgC,aAAa,GAAG;AAAA,QAC5E;AAAA,MACJ;AACA,YAAM,WAAW,cAAc,OAAO,EAAE,gBAAgB,QAAQ,iBAAiB,CAAC,EAAE,CAAC;AACrF,0BAAQ,WAAR,mBAAgB,UAAhB,4BAAwB,GAAG,OAAO,uBAAuB,cAAc,QAAQ,CAAC;AAChF,aAAO;AAAA,IACX;AAAA;AAAA;;;AC1BA,IAAAE,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACLA,IAAAC,oBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAEa;AAFb;AAAA;AAAA,IAAAC;AACA,IAAAC;AACO,IAAM,4BAA4B,CAAC,OAAO,kBAAkB,UAAU;AACzE,UAAI,iBAAiB;AACjB,mBAAW,SAAS,MAAM,MAAM,GAAG,GAAG;AAClC,cAAI,CAAC,0BAA0B,KAAK,GAAG;AACnC,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AACA,UAAI,CAAC,iBAAiB,KAAK,GAAG;AAC1B,eAAO;AAAA,MACX;AACA,UAAI,MAAM,SAAS,KAAK,MAAM,SAAS,IAAI;AACvC,eAAO;AAAA,MACX;AACA,UAAI,UAAU,MAAM,YAAY,GAAG;AAC/B,eAAO;AAAA,MACX;AACA,UAAI,YAAY,KAAK,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACxBA,IAAM,eACA,oBACO;AAFb;AAAA;AAAA,IAAM,gBAAgB;AACtB,IAAM,qBAAqB;AACpB,IAAM,WAAW,CAAC,UAAU;AAC/B,YAAM,WAAW,MAAM,MAAM,aAAa;AAC1C,UAAI,SAAS,SAAS;AAClB,eAAO;AACX,YAAM,CAAC,KAAKC,YAAW,SAAS,QAAQ,WAAW,GAAG,YAAY,IAAI;AACtE,UAAI,QAAQ,SAASA,eAAc,MAAM,YAAY,MAAM,aAAa,KAAK,aAAa,MAAM;AAC5F,eAAO;AACX,YAAM,aAAa,aAAa,IAAI,CAAC,aAAa,SAAS,MAAM,kBAAkB,CAAC,EAAE,KAAK;AAC3F,aAAO;AAAA,QACH,WAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAAA;AAAA,MACI,YAAc,CAAC;AAAA,QACP,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,YACZ,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,YACZ,aAAe;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,YACZ,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,YACZ,aAAe;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,YACZ,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,YACZ,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,UACA,aAAa;AAAA,YACT,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,iBAAiB;AAAA,YACb,aAAe;AAAA,UACnB;AAAA,UACA,cAAc;AAAA,YACV,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,qBAAqB;AAAA,YACjB,aAAe;AAAA,UACnB;AAAA,UACA,iBAAiB;AAAA,YACb,aAAe;AAAA,UACnB;AAAA,UACA,iBAAiB;AAAA,YACb,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,iBAAiB;AAAA,YACb,aAAe;AAAA,UACnB;AAAA,UACA,iBAAiB;AAAA,YACb,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,oBAAoB;AAAA,YAChB,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,oBAAoB;AAAA,YAChB,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,oBAAoB;AAAA,YAChB,aAAe;AAAA,UACnB;AAAA,UACA,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,UACA,mBAAmB;AAAA,YACf,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,GAAG;AAAA,QACC,IAAM;AAAA,QACN,SAAW;AAAA,UACP,WAAa;AAAA,UACb,oBAAsB;AAAA,UACtB,sBAAwB;AAAA,UACxB,MAAQ;AAAA,UACR,mBAAqB;AAAA,UACrB,cAAgB;AAAA,QACpB;AAAA,QACA,aAAe;AAAA,QACf,SAAW;AAAA,UACP,kBAAkB;AAAA,YACd,aAAe;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,MACL,SAAW;AAAA,IACf;AAAA;AAAA;;;ACjQA,IACI,wBACA,yBACS,WAqCA;AAxCb;AAAA;AAAA;AACA,IAAI,yBAAyB;AAC7B,IAAI,0BAA0B;AACvB,IAAM,YAAY,CAAC,UAAU;AAChC,YAAM,EAAE,WAAW,IAAI;AACvB,iBAAWC,cAAa,YAAY;AAChC,cAAM,EAAE,SAAS,QAAQ,IAAIA;AAC7B,mBAAW,CAAC,QAAQ,UAAU,KAAK,OAAO,QAAQ,OAAO,GAAG;AACxD,cAAI,WAAW,OAAO;AAClB,mBAAO;AAAA,cACH,GAAG;AAAA,cACH,GAAG;AAAA,YACP;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,iBAAWA,cAAa,YAAY;AAChC,cAAM,EAAE,aAAa,QAAQ,IAAIA;AACjC,YAAI,IAAI,OAAO,WAAW,EAAE,KAAK,KAAK,GAAG;AACrC,iBAAO;AAAA,YACH,GAAG;AAAA,UACP;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,oBAAoB,WAAW,KAAK,CAACA,eAAcA,WAAU,OAAO,KAAK;AAC/E,UAAI,CAAC,mBAAmB;AACpB,cAAM,IAAI,MAAM,mHACyC;AAAA,MAC7D;AACA,aAAO;AAAA,QACH,GAAG,kBAAkB;AAAA,MACzB;AAAA,IACJ;AAQO,IAAM,qBAAqB,MAAM;AAAA;AAAA;;;ACxCxC,IAIa;AAJb;AAAA;AAAA,IAAAC;AACA;AACA;AACA;AACO,IAAM,uBAAuB;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,4BAAwB,MAAM;AAAA;AAAA;;;ACT9B,IAAAC,wBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAAAC,sBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAAAC,2BAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,wBAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,sBAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,uBAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,eAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,cAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACLA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACJA;AAAA;AAAA;AAAA;;;ACAO,SAAS,qBAAqB,aAAa,SAAS,OAAO;AAC9D,MAAI,CAAC,YAAY,SAAS;AACtB,gBAAY,UAAU,CAAC;AAAA,EAC3B;AACA,cAAY,QAAQ,OAAO,IAAI;AAC/B,SAAO;AACX;AANA;AAAA;AAAA;AAAA;;;ACAO,SAASC,YAAW,SAAS,SAAS,OAAO;AAChD,MAAI,CAAC,QAAQ,mBAAmB;AAC5B,YAAQ,oBAAoB;AAAA,MACxB,UAAU,CAAC;AAAA,IACf;AAAA,EACJ,WACS,CAAC,QAAQ,kBAAkB,UAAU;AAC1C,YAAQ,kBAAkB,WAAW,CAAC;AAAA,EAC1C;AACA,UAAQ,kBAAkB,SAAS,OAAO,IAAI;AAClD;AAVA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,gBAAgB,CAAC,aAAU;AADxC;AAC2C,0BAAa,WAAW,QAAQ,MAAI,cAAS,YAAT,mBAAkB,WAAQ,cAAS,YAAT,mBAAkB,QAAO;AAAA;AAAA;AAAA;;;ACDlI,IAAa;AAAb;AAAA;AAAO,IAAM,uBAAuB,CAAC,sBAAsB,IAAI,KAAK,KAAK,IAAI,IAAI,iBAAiB;AAAA;AAAA;;;ACAlG,IACa;AADb;AAAA;AAAA;AACO,IAAM,gBAAgB,CAAC,WAAW,sBAAsB,KAAK,IAAI,qBAAqB,iBAAiB,EAAE,QAAQ,IAAI,SAAS,KAAK;AAAA;AAAA;;;ACD1I,IACa;AADb;AAAA;AAAA;AACO,IAAM,8BAA8B,CAAC,WAAW,6BAA6B;AAChF,YAAM,gBAAgB,KAAK,MAAM,SAAS;AAC1C,UAAI,cAAc,eAAe,wBAAwB,GAAG;AACxD,eAAO,gBAAgB,KAAK,IAAI;AAAA,MACpC;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACPA,IAAAC,cAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA,IAEM,2BAMO,2BAiBA;AAzBb;AAAA;AAAA,IAAAC;AACA,IAAAC;AACA,IAAM,4BAA4B,CAAC,MAAM,aAAa;AAClD,UAAI,CAAC,UAAU;AACX,cAAM,IAAI,MAAM,cAAc,IAAI,0CAA0C;AAAA,MAChF;AACA,aAAO;AAAA,IACX;AACO,IAAM,4BAA4B,OAAO,sBAAsB;AARtE;AASI,YAAM,UAAU,0BAA0B,WAAW,kBAAkB,OAAO;AAC9E,YAAM,SAAS,0BAA0B,UAAU,kBAAkB,MAAM;AAC3E,YAAM,cAAa,yBAAQ,eAAR,mBAAoB,eAApB,mBAAgC,gBAAhC,mBAA8C;AACjE,YAAM,iBAAiB,0BAA0B,UAAU,OAAO,MAAM;AACxE,YAAM,SAAS,MAAM,eAAe,UAAU;AAC9C,YAAM,gBAAgB,uDAAmB;AACzC,YAAM,mBAAmB,uDAAmB;AAC5C,YAAM,cAAc,uDAAmB;AACvC,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACO,IAAM,oBAAN,MAAwB;AAAA,MAC3B,MAAM,KAAK,aAAa,UAAU,mBAAmB;AA1BzD;AA2BQ,YAAI,CAAC,YAAY,WAAW,WAAW,GAAG;AACtC,gBAAM,IAAI,MAAM,sEAAsE;AAAA,QAC1F;AACA,cAAM,iBAAiB,MAAM,0BAA0B,iBAAiB;AACxE,cAAM,EAAE,QAAQ,OAAO,IAAI;AAC3B,YAAI,EAAE,eAAe,YAAY,IAAI;AACrC,cAAM,0BAA0B,kBAAkB;AAClD,cAAI,wEAAyB,gBAAzB,mBAAsC,WAAU,IAAI,GAAG;AACvD,gBAAM,CAAC,OAAO,MAAM,IAAI,wBAAwB;AAChD,eAAI,+BAAO,UAAS,aAAY,iCAAQ,UAAS,SAAS;AACtD,6BAAgB,iCAAQ,kBAAiB;AACzC,2BAAc,iCAAQ,gBAAe;AAAA,UACzC;AAAA,QACJ;AACA,cAAM,gBAAgB,MAAM,OAAO,KAAK,aAAa;AAAA,UACjD,aAAa,qBAAqB,OAAO,iBAAiB;AAAA,UAC1D;AAAA,UACA,gBAAgB;AAAA,QACpB,CAAC;AACD,eAAO;AAAA,MACX;AAAA,MACA,aAAa,mBAAmB;AAC5B,eAAO,CAAC,UAAU;AACd,gBAAM,aAAa,MAAM,cAAc,cAAc,MAAM,SAAS;AACpE,cAAI,YAAY;AACZ,kBAAM,SAAS,0BAA0B,UAAU,kBAAkB,MAAM;AAC3E,kBAAM,2BAA2B,OAAO;AACxC,mBAAO,oBAAoB,4BAA4B,YAAY,OAAO,iBAAiB;AAC3F,kBAAM,qBAAqB,OAAO,sBAAsB;AACxD,gBAAI,sBAAsB,MAAM,WAAW;AACvC,oBAAM,UAAU,qBAAqB;AAAA,YACzC;AAAA,UACJ;AACA,gBAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA,eAAe,cAAc,mBAAmB;AAC5C,cAAM,aAAa,cAAc,YAAY;AAC7C,YAAI,YAAY;AACZ,gBAAM,SAAS,0BAA0B,UAAU,kBAAkB,MAAM;AAC3E,iBAAO,oBAAoB,4BAA4B,YAAY,OAAO,iBAAiB;AAAA,QAC/F;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACtEA;AAAA;AAAA,IAAAC;AACA,IAAAC;AACA;AAAA;AAAA;;;ACFA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AACA;AAAA;AAAA;;;ACDA,IAAa,uBACA,wBACA,sBACA,4BACA,qBACA,uBACA,mBAEA,aACA,iBACA,aACA,mBACA,kBACA,eACA,cAEA,2BAiBA,sBACA,oBAEA,sBAEA,4BACA,kBACA,gBACA,qBACA;AA1Cb;AAAA;AAAO,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,uBAAuB;AAC7B,IAAM,6BAA6B;AACnC,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB;AAE1B,IAAM,cAAc;AACpB,IAAM,kBAAkB,qBAAqB,YAAY;AACzD,IAAM,cAAc;AACpB,IAAM,oBAAoB,CAAC,aAAa,iBAAiB,WAAW;AACpE,IAAM,mBAAmB,sBAAsB,YAAY;AAC3D,IAAM,gBAAgB;AACtB,IAAM,eAAe,kBAAkB,YAAY;AAEnD,IAAM,4BAA4B;AAAA,MACrC,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,qBAAqB;AAAA,MACrB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,mBAAmB;AAAA,IACvB;AACO,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAE3B,IAAM,uBAAuB;AAE7B,IAAM,6BAA6B;AACnC,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB,KAAK,KAAK,KAAK;AAAA;AAAA;;;AC1ChD,IAGM,iBACA,YACO,aACA,eAsBP;AA5BN;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACA,IAAM,kBAAkB,CAAC;AACzB,IAAM,aAAa,CAAC;AACb,IAAM,cAAc,CAAC,WAAW,QAAQ,YAAY,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,IAAI,mBAAmB;AAC5G,IAAM,gBAAgB,OAAO,mBAAmB,aAAa,WAAW,QAAQ,YAAY;AAC/F,YAAM,YAAY,MAAM,KAAK,mBAAmB,YAAY,iBAAiB,YAAY,WAAW;AACpG,YAAM,WAAW,GAAG,SAAS,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,SAAS,CAAC,IAAI,YAAY,YAAY;AAClG,UAAI,YAAY,iBAAiB;AAC7B,eAAO,gBAAgB,QAAQ;AAAA,MACnC;AACA,iBAAW,KAAK,QAAQ;AACxB,aAAO,WAAW,SAAS,gBAAgB;AACvC,eAAO,gBAAgB,WAAW,MAAM,CAAC;AAAA,MAC7C;AACA,UAAI,MAAM,OAAO,YAAY,eAAe;AAC5C,iBAAW,YAAY,CAAC,WAAW,QAAQ,SAAS,mBAAmB,GAAG;AACtE,cAAM,MAAM,KAAK,mBAAmB,KAAK,QAAQ;AAAA,MACrD;AACA,aAAQ,gBAAgB,QAAQ,IAAI;AAAA,IACxC;AAOA,IAAM,OAAO,CAAC,MAAM,QAAQ,SAAS;AACjC,YAAM,OAAO,IAAI,KAAK,MAAM;AAC5B,WAAK,OAAO,aAAa,IAAI,CAAC;AAC9B,aAAO,KAAK,OAAO;AAAA,IACvB;AAAA;AAAA;;;AChCA,IACa;AADb;AAAA;AAAA;AACO,IAAM,sBAAsB,CAAC,EAAE,QAAQ,GAAG,mBAAmB,oBAAoB;AACpF,YAAM,YAAY,CAAC;AACnB,iBAAW,cAAc,OAAO,KAAK,OAAO,EAAE,KAAK,GAAG;AAClD,YAAI,QAAQ,UAAU,KAAK,QAAW;AAClC;AAAA,QACJ;AACA,cAAM,sBAAsB,WAAW,YAAY;AACnD,YAAI,uBAAuB,8BACvB,uDAAmB,IAAI,yBACvB,qBAAqB,KAAK,mBAAmB,KAC7C,mBAAmB,KAAK,mBAAmB,GAAG;AAC9C,cAAI,CAAC,mBAAoB,mBAAmB,CAAC,gBAAgB,IAAI,mBAAmB,GAAI;AACpF;AAAA,UACJ;AAAA,QACJ;AACA,kBAAU,mBAAmB,IAAI,QAAQ,UAAU,EAAE,KAAK,EAAE,QAAQ,QAAQ,GAAG;AAAA,MACnF;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACnBA,IAAa;AAAb,IAAAC,iBAAA;AAAA;AAAO,IAAM,gBAAgB,CAAC,QAAS,OAAO,gBAAgB,cAAc,eAAe,eACvF,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA;AAAA;;;ACD5C,IAIa;AAJb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA;AACO,IAAM,iBAAiB,OAAO,EAAE,SAAS,KAAK,GAAG,oBAAoB;AACxE,iBAAW,cAAc,OAAO,KAAK,OAAO,GAAG;AAC3C,YAAI,WAAW,YAAY,MAAM,eAAe;AAC5C,iBAAO,QAAQ,UAAU;AAAA,QAC7B;AAAA,MACJ;AACA,UAAI,QAAQ,QAAW;AACnB,eAAO;AAAA,MACX,WACS,OAAO,SAAS,YAAY,YAAY,OAAO,IAAI,KAAK,cAAc,IAAI,GAAG;AAClF,cAAM,WAAW,IAAI,gBAAgB;AACrC,iBAAS,OAAO,aAAa,IAAI,CAAC;AAClC,eAAO,MAAM,MAAM,SAAS,OAAO,CAAC;AAAA,MACxC;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACgGA,SAAS,OAAO,OAAO;AACnB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAM,CAAC,KAAK;AAAA,EAChB;AACA,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAM,CAAC;AACP,QAAI,MAAM,CAAC,MAAM;AACb;AAAA,EACR;AACJ;AA5HA,IAEa,iBAmET,mBAaE,cACO;AAnFb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACO,IAAM,kBAAN,MAAsB;AAAA,MACzB,OAAO,SAAS;AACZ,cAAM,SAAS,CAAC;AAChB,mBAAW,cAAc,OAAO,KAAK,OAAO,GAAG;AAC3C,gBAAM,QAAQ,SAAS,UAAU;AACjC,iBAAO,KAAK,WAAW,KAAK,CAAC,MAAM,UAAU,CAAC,GAAG,OAAO,KAAK,kBAAkB,QAAQ,UAAU,CAAC,CAAC;AAAA,QACvG;AACA,cAAM,MAAM,IAAI,WAAW,OAAO,OAAO,CAAC,OAAO,UAAU,QAAQ,MAAM,YAAY,CAAC,CAAC;AACvF,YAAI,WAAW;AACf,mBAAW,SAAS,QAAQ;AACxB,cAAI,IAAI,OAAO,QAAQ;AACvB,sBAAY,MAAM;AAAA,QACtB;AACA,eAAO;AAAA,MACX;AAAA,MACA,kBAAkB,QAAQ;AACtB,gBAAQ,OAAO,MAAM;AAAA,UACjB,KAAK;AACD,mBAAO,WAAW,KAAK,CAAC,OAAO,QAAQ,IAAI,CAAC,CAAC;AAAA,UACjD,KAAK;AACD,mBAAO,WAAW,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC;AAAA,UAC5C,KAAK;AACD,kBAAM,YAAY,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;AACjD,sBAAU,SAAS,GAAG,CAAC;AACvB,sBAAU,SAAS,GAAG,OAAO,OAAO,KAAK;AACzC,mBAAO,IAAI,WAAW,UAAU,MAAM;AAAA,UAC1C,KAAK;AACD,kBAAM,UAAU,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;AAC/C,oBAAQ,SAAS,GAAG,CAAC;AACrB,oBAAQ,SAAS,GAAG,OAAO,OAAO,KAAK;AACvC,mBAAO,IAAI,WAAW,QAAQ,MAAM;AAAA,UACxC,KAAK;AACD,kBAAM,YAAY,IAAI,WAAW,CAAC;AAClC,sBAAU,CAAC,IAAI;AACf,sBAAU,IAAI,OAAO,MAAM,OAAO,CAAC;AACnC,mBAAO;AAAA,UACX,KAAK;AACD,kBAAM,UAAU,IAAI,SAAS,IAAI,YAAY,IAAI,OAAO,MAAM,UAAU,CAAC;AACzE,oBAAQ,SAAS,GAAG,CAAC;AACrB,oBAAQ,UAAU,GAAG,OAAO,MAAM,YAAY,KAAK;AACnD,kBAAM,WAAW,IAAI,WAAW,QAAQ,MAAM;AAC9C,qBAAS,IAAI,OAAO,OAAO,CAAC;AAC5B,mBAAO;AAAA,UACX,KAAK;AACD,kBAAM,YAAY,SAAS,OAAO,KAAK;AACvC,kBAAM,UAAU,IAAI,SAAS,IAAI,YAAY,IAAI,UAAU,UAAU,CAAC;AACtE,oBAAQ,SAAS,GAAG,CAAC;AACrB,oBAAQ,UAAU,GAAG,UAAU,YAAY,KAAK;AAChD,kBAAM,WAAW,IAAI,WAAW,QAAQ,MAAM;AAC9C,qBAAS,IAAI,WAAW,CAAC;AACzB,mBAAO;AAAA,UACX,KAAK;AACD,kBAAM,UAAU,IAAI,WAAW,CAAC;AAChC,oBAAQ,CAAC,IAAI;AACb,oBAAQ,IAAI,MAAM,WAAW,OAAO,MAAM,QAAQ,CAAC,EAAE,OAAO,CAAC;AAC7D,mBAAO;AAAA,UACX,KAAK;AACD,gBAAI,CAAC,aAAa,KAAK,OAAO,KAAK,GAAG;AAClC,oBAAM,IAAI,MAAM,0BAA0B,OAAO,KAAK,EAAE;AAAA,YAC5D;AACA,kBAAM,YAAY,IAAI,WAAW,EAAE;AACnC,sBAAU,CAAC,IAAI;AACf,sBAAU,IAAI,QAAQ,OAAO,MAAM,QAAQ,OAAO,EAAE,CAAC,GAAG,CAAC;AACzD,mBAAO;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AAEA,KAAC,SAAUC,oBAAmB;AAC1B,MAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AACvD,MAAAA,mBAAkBA,mBAAkB,WAAW,IAAI,CAAC,IAAI;AACxD,MAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AACnD,MAAAA,mBAAkBA,mBAAkB,OAAO,IAAI,CAAC,IAAI;AACpD,MAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AACtD,MAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AACnD,MAAAA,mBAAkBA,mBAAkB,WAAW,IAAI,CAAC,IAAI;AACxD,MAAAA,mBAAkBA,mBAAkB,QAAQ,IAAI,CAAC,IAAI;AACrD,MAAAA,mBAAkBA,mBAAkB,WAAW,IAAI,CAAC,IAAI;AACxD,MAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AAAA,IACvD,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAM,eAAe;AACd,IAAM,QAAN,MAAM,OAAM;AAAA,MACf,YAAY,OAAO;AACf,aAAK,QAAQ;AACb,YAAI,MAAM,eAAe,GAAG;AACxB,gBAAM,IAAI,MAAM,uCAAuC;AAAA,QAC3D;AAAA,MACJ;AAAA,MACA,OAAO,WAAW,QAAQ;AACtB,YAAI,SAAS,sBAAuB,SAAS,qBAAsB;AAC/D,gBAAM,IAAI,MAAM,GAAG,MAAM,qEAAqE;AAAA,QAClG;AACA,cAAM,QAAQ,IAAI,WAAW,CAAC;AAC9B,iBAAS,IAAI,GAAG,YAAY,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC,GAAG,IAAI,MAAM,YAAY,GAAG,KAAK,aAAa,KAAK;AACtG,gBAAM,CAAC,IAAI;AAAA,QACf;AACA,YAAI,SAAS,GAAG;AACZ,iBAAO,KAAK;AAAA,QAChB;AACA,eAAO,IAAI,OAAM,KAAK;AAAA,MAC1B;AAAA,MACA,UAAU;AACN,cAAM,QAAQ,KAAK,MAAM,MAAM,CAAC;AAChC,cAAM,WAAW,MAAM,CAAC,IAAI;AAC5B,YAAI,UAAU;AACV,iBAAO,KAAK;AAAA,QAChB;AACA,eAAO,SAAS,MAAM,KAAK,GAAG,EAAE,KAAK,WAAW,KAAK;AAAA,MACzD;AAAA,MACA,WAAW;AACP,eAAO,OAAO,KAAK,QAAQ,CAAC;AAAA,MAChC;AAAA,IACJ;AAAA;AAAA;;;AClHA,IAAa;AAAb;AAAA;AAAO,IAAM,YAAY,CAAC,cAAc,YAAY;AAChD,qBAAe,aAAa,YAAY;AACxC,iBAAW,cAAc,OAAO,KAAK,OAAO,GAAG;AAC3C,YAAI,iBAAiB,WAAW,YAAY,GAAG;AAC3C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACRA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,qBAAqB,CAAC,SAAS,UAAU,CAAC,MAAM;AAD7D;AAEI,YAAM,EAAE,SAAS,QAAQ,CAAC,EAAE,IAAI,YAAY,MAAM,OAAO;AACzD,iBAAW,QAAQ,OAAO,KAAK,OAAO,GAAG;AACrC,cAAM,QAAQ,KAAK,YAAY;AAC/B,YAAK,MAAM,MAAM,GAAG,CAAC,MAAM,YAAY,GAAC,aAAQ,uBAAR,mBAA4B,IAAI,aACpE,aAAQ,qBAAR,mBAA0B,IAAI,SAAQ;AACtC,gBAAM,IAAI,IAAI,QAAQ,IAAI;AAC1B,iBAAO,QAAQ,IAAI;AAAA,QACvB;AAAA,MACJ;AACA,aAAO;AAAA,QACH,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;AChBA,IAEa;AAFb;AAAA;AAAA,IAAAC;AACA;AACO,IAAM,iBAAiB,CAAC,YAAY;AACvC,gBAAU,YAAY,MAAM,OAAO;AACnC,iBAAW,cAAc,OAAO,KAAK,QAAQ,OAAO,GAAG;AACnD,YAAI,kBAAkB,QAAQ,WAAW,YAAY,CAAC,IAAI,IAAI;AAC1D,iBAAO,QAAQ,QAAQ,UAAU;AAAA,QACrC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACVA,IAEa;AAFb;AAAA;AAAA,IAAAC;AACA;AACO,IAAM,oBAAoB,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AACjD,YAAM,OAAO,CAAC;AACd,YAAM,aAAa,CAAC;AACpB,iBAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AAClC,YAAI,IAAI,YAAY,MAAM,kBAAkB;AACxC;AAAA,QACJ;AACA,cAAM,aAAa,UAAU,GAAG;AAChC,aAAK,KAAK,UAAU;AACpB,cAAM,QAAQ,MAAM,GAAG;AACvB,YAAI,OAAO,UAAU,UAAU;AAC3B,qBAAW,UAAU,IAAI,GAAG,UAAU,IAAI,UAAU,KAAK,CAAC;AAAA,QAC9D,WACS,MAAM,QAAQ,KAAK,GAAG;AAC3B,qBAAW,UAAU,IAAI,MACpB,MAAM,CAAC,EACP,OAAO,CAAC,SAASC,WAAU,QAAQ,OAAO,CAAC,GAAG,UAAU,IAAI,UAAUA,MAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EACpF,KAAK,EACL,KAAK,GAAG;AAAA,QACjB;AAAA,MACJ;AACA,aAAO,KACF,KAAK,EACL,IAAI,CAAC,QAAQ,WAAW,GAAG,CAAC,EAC5B,OAAO,CAACC,gBAAeA,WAAU,EACjC,KAAK,GAAG;AAAA,IACjB;AAAA;AAAA;;;AC5BA,IAAa,SAGA;AAHb;AAAA;AAAO,IAAM,UAAU,CAAC,SAAS,OAAO,IAAI,EACvC,YAAY,EACZ,QAAQ,aAAa,GAAG;AACtB,IAAM,SAAS,CAAC,SAAS;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO,IAAI,KAAK,OAAO,GAAI;AAAA,MAC/B;AACA,UAAI,OAAO,SAAS,UAAU;AAC1B,YAAI,OAAO,IAAI,GAAG;AACd,iBAAO,IAAI,KAAK,OAAO,IAAI,IAAI,GAAI;AAAA,QACvC;AACA,eAAO,IAAI,KAAK,IAAI;AAAA,MACxB;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACdA,IAMa;AANb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACO,IAAM,kBAAN,MAAsB;AAAA,MACzB,YAAY,EAAE,eAAe,aAAa,QAAQ,SAAS,QAAQ,gBAAgB,KAAM,GAAG;AACxF,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,gBAAgB;AACrB,aAAK,gBAAgB,OAAO,kBAAkB,YAAY,gBAAgB;AAC1E,aAAK,iBAAiB,kBAAkB,MAAM;AAC9C,aAAK,qBAAqB,kBAAkB,WAAW;AAAA,MAC3D;AAAA,MACA,uBAAuB,SAAS,kBAAkB,aAAa;AAC3D,cAAM,gBAAgB,OAAO,KAAK,gBAAgB,EAAE,KAAK;AACzD,eAAO,GAAG,QAAQ,MAAM;AAAA,EAC9B,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC9B,kBAAkB,OAAO,CAAC;AAAA,EAC1B,cAAc,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,iBAAiB,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC;AAAA;AAAA,EAE3E,cAAc,KAAK,GAAG,CAAC;AAAA,EACvB,WAAW;AAAA,MACT;AAAA,MACA,MAAM,mBAAmB,UAAU,iBAAiB,kBAAkB,qBAAqB;AACvF,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAK,OAAO,aAAa,gBAAgB,CAAC;AAC1C,cAAM,gBAAgB,MAAM,KAAK,OAAO;AACxC,eAAO,GAAG,mBAAmB;AAAA,EACnC,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,MAAM,aAAa,CAAC;AAAA,MAClB;AAAA,MACA,iBAAiB,EAAE,KAAK,GAAG;AACvB,YAAI,KAAK,eAAe;AACpB,gBAAM,yBAAyB,CAAC;AAChC,qBAAW,eAAe,KAAK,MAAM,GAAG,GAAG;AACvC,iBAAI,2CAAa,YAAW;AACxB;AACJ,gBAAI,gBAAgB;AAChB;AACJ,gBAAI,gBAAgB,MAAM;AACtB,qCAAuB,IAAI;AAAA,YAC/B,OACK;AACD,qCAAuB,KAAK,WAAW;AAAA,YAC3C;AAAA,UACJ;AACA,gBAAM,iBAAiB,IAAG,6BAAM,WAAW,QAAO,MAAM,EAAE,GAAG,uBAAuB,KAAK,GAAG,CAAC,GAAG,uBAAuB,SAAS,MAAK,6BAAM,SAAS,QAAO,MAAM,EAAE;AACnK,gBAAM,gBAAgB,UAAU,cAAc;AAC9C,iBAAO,cAAc,QAAQ,QAAQ,GAAG;AAAA,QAC5C;AACA,eAAO;AAAA,MACX;AAAA,MACA,4BAA4B,aAAa;AACrC,YAAI,OAAO,gBAAgB,YACvB,OAAO,YAAY,gBAAgB,YACnC,OAAO,YAAY,oBAAoB,UAAU;AACjD,gBAAM,IAAI,MAAM,yCAAyC;AAAA,QAC7D;AAAA,MACJ;AAAA,MACA,WAAW,KAAK;AACZ,cAAM,WAAW,QAAQ,GAAG,EAAE,QAAQ,UAAU,EAAE;AAClD,eAAO;AAAA,UACH;AAAA,UACA,WAAW,SAAS,MAAM,GAAG,CAAC;AAAA,QAClC;AAAA,MACJ;AAAA,MACA,uBAAuB,SAAS;AAC5B,eAAO,OAAO,KAAK,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG;AAAA,MAC/C;AAAA,IACJ;AAAA;AAAA;;;ACxEA,IAWa;AAXb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAM,cAAN,cAA0B,gBAAgB;AAAA,MAC7C,YAAY,EAAE,eAAe,aAAa,QAAQ,SAAS,QAAQ,gBAAgB,KAAM,GAAG;AACxF,cAAM;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,aAAK,kBAAkB,IAAI,gBAAgB;AAAA,MAC/C;AAAA,MACA,MAAM,QAAQ,iBAAiB,UAAU,CAAC,GAAG;AACzC,cAAM,EAAE,cAAc,oBAAI,KAAK,GAAG,YAAY,MAAM,mBAAmB,oBAAoB,iBAAiB,kBAAkB,eAAe,eAAgB,IAAI;AACjK,cAAM,cAAc,MAAM,KAAK,mBAAmB;AAClD,aAAK,4BAA4B,WAAW;AAC5C,cAAM,SAAS,iBAAkB,MAAM,KAAK,eAAe;AAC3D,cAAM,EAAE,UAAU,UAAU,IAAI,KAAK,WAAW,WAAW;AAC3D,YAAI,YAAY,mBAAmB;AAC/B,iBAAO,QAAQ,OAAO,kGAA4G;AAAA,QACtI;AACA,cAAM,QAAQ,YAAY,WAAW,QAAQ,kBAAkB,KAAK,OAAO;AAC3E,cAAM,UAAU,mBAAmB,eAAe,eAAe,GAAG,EAAE,oBAAoB,iBAAiB,CAAC;AAC5G,YAAI,YAAY,cAAc;AAC1B,kBAAQ,MAAM,iBAAiB,IAAI,YAAY;AAAA,QACnD;AACA,gBAAQ,MAAM,qBAAqB,IAAI;AACvC,gBAAQ,MAAM,sBAAsB,IAAI,GAAG,YAAY,WAAW,IAAI,KAAK;AAC3E,gBAAQ,MAAM,oBAAoB,IAAI;AACtC,gBAAQ,MAAM,mBAAmB,IAAI,UAAU,SAAS,EAAE;AAC1D,cAAM,mBAAmB,oBAAoB,SAAS,mBAAmB,eAAe;AACxF,gBAAQ,MAAM,0BAA0B,IAAI,KAAK,uBAAuB,gBAAgB;AACxF,gBAAQ,MAAM,qBAAqB,IAAI,MAAM,KAAK,aAAa,UAAU,OAAO,KAAK,cAAc,aAAa,QAAQ,WAAW,cAAc,GAAG,KAAK,uBAAuB,SAAS,kBAAkB,MAAM,eAAe,iBAAiB,KAAK,MAAM,CAAC,CAAC;AAC9P,eAAO;AAAA,MACX;AAAA,MACA,MAAM,KAAK,QAAQ,SAAS;AACxB,YAAI,OAAO,WAAW,UAAU;AAC5B,iBAAO,KAAK,WAAW,QAAQ,OAAO;AAAA,QAC1C,WACS,OAAO,WAAW,OAAO,SAAS;AACvC,iBAAO,KAAK,UAAU,QAAQ,OAAO;AAAA,QACzC,WACS,OAAO,SAAS;AACrB,iBAAO,KAAK,YAAY,QAAQ,OAAO;AAAA,QAC3C,OACK;AACD,iBAAO,KAAK,YAAY,QAAQ,OAAO;AAAA,QAC3C;AAAA,MACJ;AAAA,MACA,MAAM,UAAU,EAAE,SAAS,QAAQ,GAAG,EAAE,cAAc,oBAAI,KAAK,GAAG,gBAAgB,eAAe,eAAe,GAAG;AAC/G,cAAM,SAAS,iBAAkB,MAAM,KAAK,eAAe;AAC3D,cAAM,EAAE,WAAW,SAAS,IAAI,KAAK,WAAW,WAAW;AAC3D,cAAM,QAAQ,YAAY,WAAW,QAAQ,kBAAkB,KAAK,OAAO;AAC3E,cAAM,gBAAgB,MAAM,eAAe,EAAE,SAAS,CAAC,GAAG,MAAM,QAAQ,GAAG,KAAK,MAAM;AACtF,cAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAK,OAAO,OAAO;AACnB,cAAM,gBAAgB,MAAM,MAAM,KAAK,OAAO,CAAC;AAC/C,cAAM,eAAe;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,EAAE,KAAK,IAAI;AACX,eAAO,KAAK,WAAW,cAAc,EAAE,aAAa,eAAe,QAAQ,eAAe,CAAC;AAAA,MAC/F;AAAA,MACA,MAAM,YAAY,iBAAiB,EAAE,cAAc,oBAAI,KAAK,GAAG,eAAe,eAAe,GAAG;AAC5F,cAAM,UAAU,KAAK,UAAU;AAAA,UAC3B,SAAS,KAAK,gBAAgB,OAAO,gBAAgB,QAAQ,OAAO;AAAA,UACpE,SAAS,gBAAgB,QAAQ;AAAA,QACrC,GAAG;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA,gBAAgB,gBAAgB;AAAA,QACpC,CAAC;AACD,eAAO,QAAQ,KAAK,CAAC,cAAc;AAC/B,iBAAO,EAAE,SAAS,gBAAgB,SAAS,UAAU;AAAA,QACzD,CAAC;AAAA,MACL;AAAA,MACA,MAAM,WAAW,cAAc,EAAE,cAAc,oBAAI,KAAK,GAAG,eAAe,eAAe,IAAI,CAAC,GAAG;AAC7F,cAAM,cAAc,MAAM,KAAK,mBAAmB;AAClD,aAAK,4BAA4B,WAAW;AAC5C,cAAM,SAAS,iBAAkB,MAAM,KAAK,eAAe;AAC3D,cAAM,EAAE,UAAU,IAAI,KAAK,WAAW,WAAW;AACjD,cAAM,OAAO,IAAI,KAAK,OAAO,MAAM,KAAK,cAAc,aAAa,QAAQ,WAAW,cAAc,CAAC;AACrG,aAAK,OAAO,aAAa,YAAY,CAAC;AACtC,eAAO,MAAM,MAAM,KAAK,OAAO,CAAC;AAAA,MACpC;AAAA,MACA,MAAM,YAAY,eAAe,EAAE,cAAc,oBAAI,KAAK,GAAG,iBAAiB,mBAAmB,eAAe,eAAgB,IAAI,CAAC,GAAG;AACpI,cAAM,cAAc,MAAM,KAAK,mBAAmB;AAClD,aAAK,4BAA4B,WAAW;AAC5C,cAAM,SAAS,iBAAkB,MAAM,KAAK,eAAe;AAC3D,cAAM,UAAU,eAAe,aAAa;AAC5C,cAAM,EAAE,UAAU,UAAU,IAAI,KAAK,WAAW,WAAW;AAC3D,cAAM,QAAQ,YAAY,WAAW,QAAQ,kBAAkB,KAAK,OAAO;AAC3E,gBAAQ,QAAQ,eAAe,IAAI;AACnC,YAAI,YAAY,cAAc;AAC1B,kBAAQ,QAAQ,YAAY,IAAI,YAAY;AAAA,QAChD;AACA,cAAM,cAAc,MAAM,eAAe,SAAS,KAAK,MAAM;AAC7D,YAAI,CAAC,UAAU,eAAe,QAAQ,OAAO,KAAK,KAAK,eAAe;AAClE,kBAAQ,QAAQ,aAAa,IAAI;AAAA,QACrC;AACA,cAAM,mBAAmB,oBAAoB,SAAS,mBAAmB,eAAe;AACxF,cAAM,YAAY,MAAM,KAAK,aAAa,UAAU,OAAO,KAAK,cAAc,aAAa,QAAQ,WAAW,cAAc,GAAG,KAAK,uBAAuB,SAAS,kBAAkB,WAAW,CAAC;AAClM,gBAAQ,QAAQ,WAAW,IACvB,GAAG,oBAAoB,eACL,YAAY,WAAW,IAAI,KAAK,mBAC7B,KAAK,uBAAuB,gBAAgB,CAAC,eACjD,SAAS;AAC9B,eAAO;AAAA,MACX;AAAA,MACA,MAAM,aAAa,UAAU,iBAAiB,YAAY,kBAAkB;AACxE,cAAM,eAAe,MAAM,KAAK,mBAAmB,UAAU,iBAAiB,kBAAkB,oBAAoB;AACpH,cAAM,OAAO,IAAI,KAAK,OAAO,MAAM,UAAU;AAC7C,aAAK,OAAO,aAAa,YAAY,CAAC;AACtC,eAAO,MAAM,MAAM,KAAK,OAAO,CAAC;AAAA,MACpC;AAAA,MACA,cAAc,aAAa,QAAQ,WAAW,SAAS;AACnD,eAAO,cAAc,KAAK,QAAQ,aAAa,WAAW,QAAQ,WAAW,KAAK,OAAO;AAAA,MAC7F;AAAA,IACJ;AAAA;AAAA;;;ACtIA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACuFA,SAAS,4BAA4B,QAAQ,EAAE,aAAa,0BAA2B,GAAG;AACtF,MAAI;AACJ,MAAI,aAAa;AACb,QAAI,EAAC,2CAAa,WAAU;AACxB,4BAAsB,wBAAwB,aAAa,mBAAmB,0BAA0B;AAAA,IAC5G,OACK;AACD,4BAAsB;AAAA,IAC1B;AAAA,EACJ,OACK;AACD,QAAI,2BAA2B;AAC3B,4BAAsBC,mBAAkB,0BAA0B,OAAO,OAAO,CAAC,GAAG,QAAQ;AAAA,QACxF,oBAAoB;AAAA,MACxB,CAAC,CAAC,CAAC;AAAA,IACP,OACK;AACD,4BAAsB,YAAY;AAC9B,cAAM,IAAI,MAAM,uHAAuH;AAAA,MAC3I;AAAA,IACJ;AAAA,EACJ;AACA,sBAAoB,WAAW;AAC/B,SAAO;AACX;AACA,SAAS,iBAAiB,QAAQ,qBAAqB;AACnD,MAAI,oBAAoB,aAAa;AACjC,WAAO;AAAA,EACX;AACA,QAAM,KAAK,OAAO,YAAY,oBAAoB,EAAE,GAAG,SAAS,oBAAoB,OAAO,CAAC;AAC5F,KAAG,WAAW,oBAAoB;AAClC,KAAG,cAAc;AACjB,SAAO;AACX;AAlIA,IAGa;AAHb;AAAA;AAAA;AACA,IAAAC;AACA,IAAAA;AACO,IAAM,2BAA2B,CAAC,WAAW;AAChD,UAAI,mBAAmB,OAAO;AAC9B,UAAI,iBAAiB,CAAC,CAAC,OAAO;AAC9B,UAAI,sBAAsB;AAC1B,aAAO,eAAe,QAAQ,eAAe;AAAA,QACzC,IAAI,aAAa;AACb,cAAI,eAAe,gBAAgB,oBAAoB,gBAAgB,qBAAqB;AACxF,6BAAiB;AAAA,UACrB;AACA,6BAAmB;AACnB,gBAAM,mBAAmB,4BAA4B,QAAQ;AAAA,YACzD,aAAa;AAAA,YACb,2BAA2B,OAAO;AAAA,UACtC,CAAC;AACD,gBAAM,gBAAgB,iBAAiB,QAAQ,gBAAgB;AAC/D,cAAI,kBAAkB,CAAC,cAAc,YAAY;AAC7C,kCAAsB,OAAO,YAAY,cAAc,OAAO,EAAE,KAAK,CAAC,UAAU,qBAAqB,OAAO,oBAAoB,GAAG,CAAC;AACpI,gCAAoB,WAAW,cAAc;AAC7C,gCAAoB,cAAc,cAAc;AAChD,gCAAoB,aAAa;AAAA,UACrC,OACK;AACD,kCAAsB;AAAA,UAC1B;AAAA,QACJ;AAAA,QACA,MAAM;AACF,iBAAO;AAAA,QACX;AAAA,QACA,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AACD,aAAO,cAAc;AACrB,YAAM,EAAE,oBAAoB,MAAM,oBAAoB,OAAO,qBAAqB,GAAG,OAAQ,IAAI;AACjG,UAAI;AACJ,UAAI,OAAO,QAAQ;AACf,iBAASD,mBAAkB,OAAO,MAAM;AAAA,MAC5C,WACS,OAAO,oBAAoB;AAChC,iBAAS,MAAMA,mBAAkB,OAAO,MAAM,EAAE,EAC3C,KAAK,OAAO,WAAW;AAAA,UACvB,MAAM,OAAO,mBAAmB,QAAQ;AAAA,YACrC,iBAAiB,MAAM,OAAO,gBAAgB;AAAA,YAC9C,sBAAsB,MAAM,OAAO,qBAAqB;AAAA,UAC5D,CAAC,KAAM,CAAC;AAAA,UACR;AAAA,QACJ,CAAC,EACI,KAAK,CAAC,CAAC,YAAY,MAAM,MAAM;AAChC,gBAAM,EAAE,eAAe,eAAe,IAAI;AAC1C,iBAAO,gBAAgB,OAAO,iBAAiB,iBAAiB;AAChE,iBAAO,cAAc,OAAO,eAAe,kBAAkB,OAAO;AACpE,gBAAM,SAAS;AAAA,YACX,GAAG;AAAA,YACH,aAAa,OAAO;AAAA,YACpB,QAAQ,OAAO;AAAA,YACf,SAAS,OAAO;AAAA,YAChB;AAAA,YACA,eAAe;AAAA,UACnB;AACA,gBAAM,aAAa,OAAO,qBAAqB;AAC/C,iBAAO,IAAI,WAAW,MAAM;AAAA,QAChC,CAAC;AAAA,MACL,OACK;AACD,iBAAS,OAAO,eAAe;AAC3B,uBAAa,OAAO,OAAO,CAAC,GAAG;AAAA,YAC3B,MAAM;AAAA,YACN,aAAa,OAAO,eAAe,OAAO;AAAA,YAC1C,eAAe,MAAMA,mBAAkB,OAAO,MAAM,EAAE;AAAA,YACtD,YAAY,CAAC;AAAA,UACjB,GAAG,UAAU;AACb,gBAAM,gBAAgB,WAAW;AACjC,gBAAM,iBAAiB,WAAW;AAClC,iBAAO,gBAAgB,OAAO,iBAAiB;AAC/C,iBAAO,cAAc,OAAO,eAAe,kBAAkB,OAAO;AACpE,gBAAM,SAAS;AAAA,YACX,GAAG;AAAA,YACH,aAAa,OAAO;AAAA,YACpB,QAAQ,OAAO;AAAA,YACf,SAAS,OAAO;AAAA,YAChB;AAAA,YACA,eAAe;AAAA,UACnB;AACA,gBAAM,aAAa,OAAO,qBAAqB;AAC/C,iBAAO,IAAI,WAAW,MAAM;AAAA,QAChC;AAAA,MACJ;AACA,YAAM,iBAAiB,OAAO,OAAO,QAAQ;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AAAA;AAAA;;;AC/FA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACJA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAE;AAAA;AAAA;;;ACAA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,oBAAoB,CAAC,YAAY,YAAY,YAAY,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS,QAAQ,YAAY,IAAI,CAAC;AAAA;AAAA;;;ACDnI,IACa,eAgBA,oBAKA;AAtBb;AAAA;AAAA;AACO,IAAM,gBAAgB,CAAC,YAAY,YAAY,kBAAkB,YAAY,OAAO,EAAE,KAAK,CAAC,YAAY;AAC3G,UAAI,QAAQ,QAAQ;AAChB,YAAI;AACA,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC7B,SACO,GAAG;AACN,eAAI,uBAAG,UAAS,eAAe;AAC3B,mBAAO,eAAe,GAAG,qBAAqB;AAAA,cAC1C,OAAO;AAAA,YACX,CAAC;AAAA,UACL;AACA,gBAAM;AAAA,QACV;AAAA,MACJ;AACA,aAAO,CAAC;AAAA,IACZ,CAAC;AACM,IAAM,qBAAqB,OAAO,WAAW,YAAY;AAC5D,YAAM,QAAQ,MAAM,cAAc,WAAW,OAAO;AACpD,YAAM,UAAU,MAAM,WAAW,MAAM;AACvC,aAAO;AAAA,IACX;AACO,IAAM,wBAAwB,CAAC,QAAQ,SAAS;AACnD,YAAM,UAAU,CAAC,QAAQ,QAAQ,OAAO,KAAK,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,YAAY,MAAM,IAAI,YAAY,CAAC;AACtG,YAAM,oBAAoB,CAAC,aAAa;AACpC,YAAI,aAAa;AACjB,YAAI,OAAO,eAAe,UAAU;AAChC,uBAAa,WAAW,SAAS;AAAA,QACrC;AACA,YAAI,WAAW,QAAQ,GAAG,KAAK,GAAG;AAC9B,uBAAa,WAAW,MAAM,GAAG,EAAE,CAAC;AAAA,QACxC;AACA,YAAI,WAAW,QAAQ,GAAG,KAAK,GAAG;AAC9B,uBAAa,WAAW,MAAM,GAAG,EAAE,CAAC;AAAA,QACxC;AACA,YAAI,WAAW,QAAQ,GAAG,KAAK,GAAG;AAC9B,uBAAa,WAAW,MAAM,GAAG,EAAE,CAAC;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,YAAM,YAAY,QAAQ,OAAO,SAAS,kBAAkB;AAC5D,UAAI,cAAc,QAAW;AACzB,eAAO,kBAAkB,OAAO,QAAQ,SAAS,CAAC;AAAA,MACtD;AACA,UAAI,KAAK,SAAS,QAAW;AACzB,eAAO,kBAAkB,KAAK,IAAI;AAAA,MACtC;AACA,UAAI,KAAK,QAAQ,MAAM,QAAW;AAC9B,eAAO,kBAAkB,KAAK,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACJ;AAAA;AAAA;;;AClDA;AAAA;AAAA;AAEA,QAAM,gBAAgB;AACtB,QAAM,WAAW,gBAAgB;AACjC,QAAM,aAAa,MAAM,gBAAgB,OAAO,WAAW;AAC3D,QAAM,YAAY,IAAI,OAAO,MAAM,aAAa,GAAG;AAEnD,QAAM,gBAAgB,SAAS,QAAQ,OAAO;AAC5C,YAAM,UAAU,CAAC;AACjB,UAAI,QAAQ,MAAM,KAAK,MAAM;AAC7B,aAAO,OAAO;AACZ,cAAM,aAAa,CAAC;AACpB,mBAAW,aAAa,MAAM,YAAY,MAAM,CAAC,EAAE;AACnD,cAAM,MAAM,MAAM;AAClB,iBAAS,QAAQ,GAAG,QAAQ,KAAK,SAAS;AACxC,qBAAW,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,gBAAQ,KAAK,UAAU;AACvB,gBAAQ,MAAM,KAAK,MAAM;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AAEA,QAAM,SAAS,SAAS,QAAQ;AAC9B,YAAM,QAAQ,UAAU,KAAK,MAAM;AACnC,aAAO,EAAE,UAAU,QAAQ,OAAO,UAAU;AAAA,IAC9C;AAEA,YAAQ,UAAU,SAAS,GAAG;AAC5B,aAAO,OAAO,MAAM;AAAA,IACtB;AAEA,YAAQ,gBAAgB,SAAS,KAAK;AACpC,aAAO,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,IACrC;AAOA,YAAQ,QAAQ,SAAS,QAAQ,GAAG,WAAW;AAC7C,UAAI,GAAG;AACL,cAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,cAAM,MAAM,KAAK;AACjB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,cAAc,UAAU;AAC1B,mBAAO,KAAK,CAAC,CAAC,IAAI,CAAE,EAAE,KAAK,CAAC,CAAC,CAAE;AAAA,UACjC,OAAO;AACL,mBAAO,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAKA,YAAQ,WAAW,SAAS,GAAG;AAC7B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAKA,YAAQ,SAAS;AACjB,YAAQ,gBAAgB;AACxB,YAAQ,aAAa;AAAA;AAAA;;;ACvErB;AAAA;AAAA;AAEA,QAAM,OAAO;AAEb,QAAM,iBAAiB;AAAA,MACrB,wBAAwB;AAAA;AAAA,MACxB,cAAc,CAAC;AAAA,IACjB;AAGA,YAAQ,WAAW,SAAU,SAAS,SAAS;AAC7C,gBAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAKnD,YAAM,OAAO,CAAC;AACd,UAAI,WAAW;AAGf,UAAI,cAAc;AAElB,UAAI,QAAQ,CAAC,MAAM,UAAU;AAE3B,kBAAU,QAAQ,OAAO,CAAC;AAAA,MAC5B;AAEA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAEvC,YAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,IAAE,CAAC,MAAM,KAAK;AAC9C,eAAG;AACH,cAAI,OAAO,SAAQ,CAAC;AACpB,cAAI,EAAE,IAAK,QAAO;AAAA,QACpB,WAAU,QAAQ,CAAC,MAAM,KAAK;AAG5B,cAAI,cAAc;AAClB;AAEA,cAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,gBAAI,oBAAoB,SAAS,CAAC;AAClC;AAAA,UACF,OAAO;AACL,gBAAI,aAAa;AACjB,gBAAI,QAAQ,CAAC,MAAM,KAAK;AAEtB,2BAAa;AACb;AAAA,YACF;AAEA,gBAAI,UAAU;AACd,mBAAO,IAAI,QAAQ,UACjB,QAAQ,CAAC,MAAM,OACf,QAAQ,CAAC,MAAM,OACf,QAAQ,CAAC,MAAM,OACf,QAAQ,CAAC,MAAM,QACf,QAAQ,CAAC,MAAM,MAAM,KACrB;AACA,yBAAW,QAAQ,CAAC;AAAA,YACtB;AACA,sBAAU,QAAQ,KAAK;AAGvB,gBAAI,QAAQ,QAAQ,SAAS,CAAC,MAAM,KAAK;AAEvC,wBAAU,QAAQ,UAAU,GAAG,QAAQ,SAAS,CAAC;AAEjD;AAAA,YACF;AACA,gBAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,kBAAI;AACJ,kBAAI,QAAQ,KAAK,EAAE,WAAW,GAAG;AAC/B,sBAAM;AAAA,cACR,OAAO;AACL,sBAAM,UAAQ,UAAQ;AAAA,cACxB;AACA,qBAAO,eAAe,cAAc,KAAK,yBAAyB,SAAS,CAAC,CAAC;AAAA,YAC/E;AAEA,kBAAM,SAAS,iBAAiB,SAAS,CAAC;AAC1C,gBAAI,WAAW,OAAO;AACpB,qBAAO,eAAe,eAAe,qBAAmB,UAAQ,sBAAsB,yBAAyB,SAAS,CAAC,CAAC;AAAA,YAC5H;AACA,gBAAI,UAAU,OAAO;AACrB,gBAAI,OAAO;AAEX,gBAAI,QAAQ,QAAQ,SAAS,CAAC,MAAM,KAAK;AAEvC,oBAAM,eAAe,IAAI,QAAQ;AACjC,wBAAU,QAAQ,UAAU,GAAG,QAAQ,SAAS,CAAC;AACjD,oBAAM,UAAU,wBAAwB,SAAS,OAAO;AACxD,kBAAI,YAAY,MAAM;AACpB,2BAAW;AAAA,cAEb,OAAO;AAIL,uBAAO,eAAe,QAAQ,IAAI,MAAM,QAAQ,IAAI,KAAK,yBAAyB,SAAS,eAAe,QAAQ,IAAI,IAAI,CAAC;AAAA,cAC7H;AAAA,YACF,WAAW,YAAY;AACrB,kBAAI,CAAC,OAAO,WAAW;AACrB,uBAAO,eAAe,cAAc,kBAAgB,UAAQ,kCAAkC,yBAAyB,SAAS,CAAC,CAAC;AAAA,cACpI,WAAW,QAAQ,KAAK,EAAE,SAAS,GAAG;AACpC,uBAAO,eAAe,cAAc,kBAAgB,UAAQ,gDAAgD,yBAAyB,SAAS,WAAW,CAAC;AAAA,cAC5J,WAAW,KAAK,WAAW,GAAG;AAC5B,uBAAO,eAAe,cAAc,kBAAgB,UAAQ,0BAA0B,yBAAyB,SAAS,WAAW,CAAC;AAAA,cACtI,OAAO;AACL,sBAAM,MAAM,KAAK,IAAI;AACrB,oBAAI,YAAY,IAAI,SAAS;AAC3B,sBAAI,UAAU,yBAAyB,SAAS,IAAI,WAAW;AAC/D,yBAAO;AAAA,oBAAe;AAAA,oBACpB,2BAAyB,IAAI,UAAQ,uBAAqB,QAAQ,OAAK,WAAS,QAAQ,MAAI,+BAA6B,UAAQ;AAAA,oBACjI,yBAAyB,SAAS,WAAW;AAAA,kBAAC;AAAA,gBAClD;AAGA,oBAAI,KAAK,UAAU,GAAG;AACpB,gCAAc;AAAA,gBAChB;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,UAAU,wBAAwB,SAAS,OAAO;AACxD,kBAAI,YAAY,MAAM;AAIpB,uBAAO,eAAe,QAAQ,IAAI,MAAM,QAAQ,IAAI,KAAK,yBAAyB,SAAS,IAAI,QAAQ,SAAS,QAAQ,IAAI,IAAI,CAAC;AAAA,cACnI;AAGA,kBAAI,gBAAgB,MAAM;AACxB,uBAAO,eAAe,cAAc,uCAAuC,yBAAyB,SAAS,CAAC,CAAC;AAAA,cACjH,WAAU,QAAQ,aAAa,QAAQ,OAAO,MAAM,IAAG;AAAA,cAEvD,OAAO;AACL,qBAAK,KAAK,EAAC,SAAS,YAAW,CAAC;AAAA,cAClC;AACA,yBAAW;AAAA,YACb;AAIA,iBAAK,KAAK,IAAI,QAAQ,QAAQ,KAAK;AACjC,kBAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,oBAAI,QAAQ,IAAI,CAAC,MAAM,KAAK;AAE1B;AACA,sBAAI,oBAAoB,SAAS,CAAC;AAClC;AAAA,gBACF,WAAW,QAAQ,IAAE,CAAC,MAAM,KAAK;AAC/B,sBAAI,OAAO,SAAS,EAAE,CAAC;AACvB,sBAAI,EAAE,IAAK,QAAO;AAAA,gBACpB,OAAM;AACJ;AAAA,gBACF;AAAA,cACF,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC7B,sBAAM,WAAW,kBAAkB,SAAS,CAAC;AAC7C,oBAAI,YAAY;AACd,yBAAO,eAAe,eAAe,6BAA6B,yBAAyB,SAAS,CAAC,CAAC;AACxG,oBAAI;AAAA,cACN,OAAK;AACH,oBAAI,gBAAgB,QAAQ,CAAC,aAAa,QAAQ,CAAC,CAAC,GAAG;AACrD,yBAAO,eAAe,cAAc,yBAAyB,yBAAyB,SAAS,CAAC,CAAC;AAAA,gBACnG;AAAA,cACF;AAAA,YACF;AACA,gBAAI,QAAQ,CAAC,MAAM,KAAK;AACtB;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAK,aAAa,QAAQ,CAAC,CAAC,GAAG;AAC7B;AAAA,UACF;AACA,iBAAO,eAAe,eAAe,WAAS,QAAQ,CAAC,IAAE,sBAAsB,yBAAyB,SAAS,CAAC,CAAC;AAAA,QACrH;AAAA,MACF;AAEA,UAAI,CAAC,UAAU;AACb,eAAO,eAAe,cAAc,uBAAuB,CAAC;AAAA,MAC9D,WAAU,KAAK,UAAU,GAAG;AACxB,eAAO,eAAe,cAAc,mBAAiB,KAAK,CAAC,EAAE,UAAQ,MAAM,yBAAyB,SAAS,KAAK,CAAC,EAAE,WAAW,CAAC;AAAA,MACrI,WAAU,KAAK,SAAS,GAAG;AACvB,eAAO,eAAe,cAAc,cAChC,KAAK,UAAU,KAAK,IAAI,OAAK,EAAE,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,UAAU,EAAE,IACtE,YAAY,EAAC,MAAM,GAAG,KAAK,EAAC,CAAC;AAAA,MACrC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,MAAK;AACzB,aAAO,SAAS,OAAO,SAAS,OAAQ,SAAS,QAAS,SAAS;AAAA,IACrE;AAMA,aAAS,OAAO,SAAS,GAAG;AAC1B,YAAM,QAAQ;AACd,aAAO,IAAI,QAAQ,QAAQ,KAAK;AAC9B,YAAI,QAAQ,CAAC,KAAK,OAAO,QAAQ,CAAC,KAAK,KAAK;AAE1C,gBAAM,UAAU,QAAQ,OAAO,OAAO,IAAI,KAAK;AAC/C,cAAI,IAAI,KAAK,YAAY,OAAO;AAC9B,mBAAO,eAAe,cAAc,8DAA8D,yBAAyB,SAAS,CAAC,CAAC;AAAA,UACxI,WAAW,QAAQ,CAAC,KAAK,OAAO,QAAQ,IAAI,CAAC,KAAK,KAAK;AAErD;AACA;AAAA,UACF,OAAO;AACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,oBAAoB,SAAS,GAAG;AACvC,UAAI,QAAQ,SAAS,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,KAAK;AAE9E,aAAK,KAAK,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACpC,cAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,KAAK;AAC1E,iBAAK;AACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,WACE,QAAQ,SAAS,IAAI,KACrB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,KACnB;AACA,YAAI,qBAAqB;AACzB,aAAK,KAAK,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACpC,cAAI,QAAQ,CAAC,MAAM,KAAK;AACtB;AAAA,UACF,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC7B;AACA,gBAAI,uBAAuB,GAAG;AAC5B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,WACE,QAAQ,SAAS,IAAI,KACrB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,KACnB;AACA,aAAK,KAAK,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACpC,cAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,KAAK;AAC1E,iBAAK;AACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,QAAM,cAAc;AACpB,QAAM,cAAc;AAOpB,aAAS,iBAAiB,SAAS,GAAG;AACpC,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,UAAI,YAAY;AAChB,aAAO,IAAI,QAAQ,QAAQ,KAAK;AAC9B,YAAI,QAAQ,CAAC,MAAM,eAAe,QAAQ,CAAC,MAAM,aAAa;AAC5D,cAAI,cAAc,IAAI;AACpB,wBAAY,QAAQ,CAAC;AAAA,UACvB,WAAW,cAAc,QAAQ,CAAC,GAAG;AAAA,UAErC,OAAO;AACL,wBAAY;AAAA,UACd;AAAA,QACF,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC7B,cAAI,cAAc,IAAI;AACpB,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AACA,mBAAW,QAAQ,CAAC;AAAA,MACtB;AACA,UAAI,cAAc,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAKA,QAAM,oBAAoB,IAAI,OAAO,0DAA2D,GAAG;AAInG,aAAS,wBAAwB,SAAS,SAAS;AAKjD,YAAM,UAAU,KAAK,cAAc,SAAS,iBAAiB;AAC7D,YAAM,YAAY,CAAC;AAEnB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,QAAQ,CAAC,EAAE,CAAC,EAAE,WAAW,GAAG;AAE9B,iBAAO,eAAe,eAAe,gBAAc,QAAQ,CAAC,EAAE,CAAC,IAAE,+BAA+B,qBAAqB,QAAQ,CAAC,CAAC,CAAC;AAAA,QAClI,WAAW,QAAQ,CAAC,EAAE,CAAC,MAAM,UAAa,QAAQ,CAAC,EAAE,CAAC,MAAM,QAAW;AACrE,iBAAO,eAAe,eAAe,gBAAc,QAAQ,CAAC,EAAE,CAAC,IAAE,uBAAuB,qBAAqB,QAAQ,CAAC,CAAC,CAAC;AAAA,QAC1H,WAAW,QAAQ,CAAC,EAAE,CAAC,MAAM,UAAa,CAAC,QAAQ,wBAAwB;AAEzE,iBAAO,eAAe,eAAe,wBAAsB,QAAQ,CAAC,EAAE,CAAC,IAAE,qBAAqB,qBAAqB,QAAQ,CAAC,CAAC,CAAC;AAAA,QAChI;AAIA,cAAM,WAAW,QAAQ,CAAC,EAAE,CAAC;AAC7B,YAAI,CAAC,iBAAiB,QAAQ,GAAG;AAC/B,iBAAO,eAAe,eAAe,gBAAc,WAAS,yBAAyB,qBAAqB,QAAQ,CAAC,CAAC,CAAC;AAAA,QACvH;AACA,YAAI,CAAC,UAAU,eAAe,QAAQ,GAAG;AAEvC,oBAAU,QAAQ,IAAI;AAAA,QACxB,OAAO;AACL,iBAAO,eAAe,eAAe,gBAAc,WAAS,kBAAkB,qBAAqB,QAAQ,CAAC,CAAC,CAAC;AAAA,QAChH;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,wBAAwB,SAAS,GAAG;AAC3C,UAAI,KAAK;AACT,UAAI,QAAQ,CAAC,MAAM,KAAK;AACtB;AACA,aAAK;AAAA,MACP;AACA,aAAO,IAAI,QAAQ,QAAQ,KAAK;AAC9B,YAAI,QAAQ,CAAC,MAAM;AACjB,iBAAO;AACT,YAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE;AACtB;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAEA,aAAS,kBAAkB,SAAS,GAAG;AAErC;AACA,UAAI,QAAQ,CAAC,MAAM;AACjB,eAAO;AACT,UAAI,QAAQ,CAAC,MAAM,KAAK;AACtB;AACA,eAAO,wBAAwB,SAAS,CAAC;AAAA,MAC3C;AACA,UAAI,QAAQ;AACZ,aAAO,IAAI,QAAQ,QAAQ,KAAK,SAAS;AACvC,YAAI,QAAQ,CAAC,EAAE,MAAM,IAAI,KAAK,QAAQ;AACpC;AACF,YAAI,QAAQ,CAAC,MAAM;AACjB;AACF,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,MAAM,SAAS,YAAY;AACjD,aAAO;AAAA,QACL,KAAK;AAAA,UACH;AAAA,UACA,KAAK;AAAA,UACL,MAAM,WAAW,QAAQ;AAAA,UACzB,KAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,iBAAiB,UAAU;AAClC,aAAO,KAAK,OAAO,QAAQ;AAAA,IAC7B;AAIA,aAAS,gBAAgB,SAAS;AAChC,aAAO,KAAK,OAAO,OAAO;AAAA,IAC5B;AAGA,aAAS,yBAAyB,SAAS,OAAO;AAChD,YAAM,QAAQ,QAAQ,UAAU,GAAG,KAAK,EAAE,MAAM,OAAO;AACvD,aAAO;AAAA,QACL,MAAM,MAAM;AAAA;AAAA,QAGZ,KAAK,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS;AAAA,MACxC;AAAA,IACF;AAGA,aAAS,qBAAqB,OAAO;AACnC,aAAO,MAAM,aAAa,MAAM,CAAC,EAAE;AAAA,IACrC;AAAA;AAAA;;;ACxaA;AAAA;AACA,QAAM,iBAAiB;AAAA,MACnB,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,gBAAgB;AAAA;AAAA,MAChB,wBAAwB;AAAA;AAAA;AAAA,MAExB,eAAe;AAAA,MACf,qBAAqB;AAAA,MACrB,YAAY;AAAA;AAAA,MACZ,eAAe;AAAA,MACf,oBAAoB;AAAA,QAClB,KAAK;AAAA,QACL,cAAc;AAAA,QACd,WAAW;AAAA,MACb;AAAA,MACA,mBAAmB,SAAS,SAASC,MAAK;AACxC,eAAOA;AAAA,MACT;AAAA,MACA,yBAAyB,SAAS,UAAUA,MAAK;AAC/C,eAAOA;AAAA,MACT;AAAA,MACA,WAAW,CAAC;AAAA;AAAA,MACZ,sBAAsB;AAAA,MACtB,SAAS,MAAM;AAAA,MACf,iBAAiB;AAAA,MACjB,cAAc,CAAC;AAAA,MACf,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,mBAAmB;AAAA,MACnB,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,wBAAwB;AAAA,MACxB,WAAW,SAAS,SAAS,OAAO,OAAM;AACxC,eAAO;AAAA,MACT;AAAA;AAAA,IAEJ;AAEA,QAAM,eAAe,SAAS,SAAS;AACnC,aAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAAA,IACpD;AAEA,YAAQ,eAAe;AACvB,YAAQ,iBAAiB;AAAA;AAAA;;;AC/CzB;AAAA;AAAA;AAEA,QAAM,UAAN,MAAa;AAAA,MACX,YAAY,SAAS;AACnB,aAAK,UAAU;AACf,aAAK,QAAQ,CAAC;AACd,aAAK,IAAI,IAAI,CAAC;AAAA,MAChB;AAAA,MACA,IAAI,KAAIC,MAAI;AAEV,YAAG,QAAQ,YAAa,OAAM;AAC9B,aAAK,MAAM,KAAM,EAAC,CAAC,GAAG,GAAGA,KAAI,CAAC;AAAA,MAChC;AAAA,MACA,SAAS,MAAM;AACb,YAAG,KAAK,YAAY,YAAa,MAAK,UAAU;AAChD,YAAG,KAAK,IAAI,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC,EAAE,SAAS,GAAE;AAClD,eAAK,MAAM,KAAM,EAAE,CAAC,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;AAAA,QACrE,OAAK;AACH,eAAK,MAAM,KAAM,EAAE,CAAC,KAAK,OAAO,GAAG,KAAK,MAAM,CAAC;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAM,OAAO;AAGb,aAAS,YAAY,SAAS,GAAE;AAE5B,YAAM,WAAW,CAAC;AAClB,UAAI,QAAQ,IAAI,CAAC,MAAM,OAClB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,OACnB,QAAQ,IAAI,CAAC,MAAM,KACxB;AACI,YAAI,IAAE;AACN,YAAI,qBAAqB;AACzB,YAAI,UAAU,OAAO,UAAU;AAC/B,YAAI,MAAM;AACV,eAAK,IAAE,QAAQ,QAAO,KAAI;AACtB,cAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;AAChC,gBAAI,WAAW,SAAS,SAAS,CAAC,GAAE;AAChC,mBAAK;AACL,eAAC,YAAY,KAAI,CAAC,IAAI,cAAc,SAAQ,IAAE,CAAC;AAC/C,kBAAG,IAAI,QAAQ,GAAG,MAAM;AACpB,yBAAU,mBAAmB,UAAU,CAAE,IAAI;AAAA,kBACzC,MAAO,OAAQ,IAAI,UAAU,KAAI,GAAG;AAAA,kBACpC;AAAA,gBACJ;AAAA,YACR,WACS,WAAW,UAAU,SAAS,CAAC,EAAI,MAAK;AAAA,qBACxC,WAAW,UAAU,SAAS,CAAC,EAAI,MAAK;AAAA,qBACxC,WAAW,WAAW,SAAS,CAAC,EAAG,MAAK;AAAA,qBACxC,UAAmC,WAAU;AAAA,gBACV,OAAM,IAAI,MAAM,iBAAiB;AAE7E;AACA,kBAAM;AAAA,UACV,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC3B,gBAAG,SAAQ;AACP,kBAAI,QAAQ,IAAI,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,KAAI;AACjD,0BAAU;AACV;AAAA,cACJ;AAAA,YACJ,OAAK;AACD;AAAA,YACJ;AACA,gBAAI,uBAAuB,GAAG;AAC5B;AAAA,YACF;AAAA,UACJ,WAAU,QAAQ,CAAC,MAAM,KAAI;AACzB,sBAAU;AAAA,UACd,OAAK;AACD,mBAAO,QAAQ,CAAC;AAAA,UACpB;AAAA,QACJ;AACA,YAAG,uBAAuB,GAAE;AACxB,gBAAM,IAAI,MAAM,kBAAkB;AAAA,QACtC;AAAA,MACJ,OAAK;AACD,cAAM,IAAI,MAAM,gCAAgC;AAAA,MACpD;AACA,aAAO,EAAC,UAAU,EAAC;AAAA,IACvB;AAEA,aAAS,cAAc,SAAQ,GAAE;AAW7B,UAAIC,cAAa;AACjB,aAAO,IAAI,QAAQ,WAAW,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM,MAAO,KAAK;AAG3E,QAAAA,eAAc,QAAQ,CAAC;AAAA,MAC3B;AACA,MAAAA,cAAaA,YAAW,KAAK;AAC7B,UAAGA,YAAW,QAAQ,GAAG,MAAM,GAAI,OAAM,IAAI,MAAM,oCAAoC;AAGvF,YAAM,YAAY,QAAQ,GAAG;AAC7B,UAAIC,OAAM;AACV,aAAO,IAAI,QAAQ,UAAU,QAAQ,CAAC,MAAM,WAAY,KAAK;AACzD,QAAAA,QAAO,QAAQ,CAAC;AAAA,MACpB;AACA,aAAO,CAACD,aAAYC,MAAK,CAAC;AAAA,IAC9B;AAEA,aAAS,UAAU,SAAS,GAAE;AAC1B,UAAG,QAAQ,IAAE,CAAC,MAAM,OACpB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,IAAK,QAAO;AAC7B,aAAO;AAAA,IACX;AACA,aAAS,SAAS,SAAS,GAAE;AACzB,UAAG,QAAQ,IAAE,CAAC,MAAM,OACpB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,IAAK,QAAO;AAC7B,aAAO;AAAA,IACX;AACA,aAAS,UAAU,SAAS,GAAE;AAC1B,UAAG,QAAQ,IAAE,CAAC,MAAM,OACpB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,IAAK,QAAO;AAC7B,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,SAAS,GAAE;AAC1B,UAAG,QAAQ,IAAE,CAAC,MAAM,OACpB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,IAAK,QAAO;AAC7B,aAAO;AAAA,IACX;AACA,aAAS,WAAW,SAAS,GAAE;AAC3B,UAAG,QAAQ,IAAE,CAAC,MAAM,OACpB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,OACjB,QAAQ,IAAE,CAAC,MAAM,IAAK,QAAO;AAC7B,aAAO;AAAA,IACX;AAEA,aAAS,mBAAmB,MAAK;AAC7B,UAAI,KAAK,OAAO,IAAI;AACvB,eAAO;AAAA;AAEA,cAAM,IAAI,MAAM,uBAAuB,IAAI,EAAE;AAAA,IACrD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvJjB;AAAA;AAAA,QAAM,WAAW;AACjB,QAAM,WAAW;AAKjB,QAAM,WAAW;AAAA,MACb,KAAO;AAAA;AAAA,MAEP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,WAAW;AAAA;AAAA,IAEf;AAEA,aAAS,SAAS,KAAK,UAAU,CAAC,GAAE;AAChC,gBAAU,OAAO,OAAO,CAAC,GAAG,UAAU,OAAQ;AAC9C,UAAG,CAAC,OAAO,OAAO,QAAQ,SAAW,QAAO;AAE5C,UAAI,aAAc,IAAI,KAAK;AAE3B,UAAG,QAAQ,aAAa,UAAa,QAAQ,SAAS,KAAK,UAAU,EAAG,QAAO;AAAA,eACvE,QAAM,IAAK,QAAO;AAAA,eACjB,QAAQ,OAAO,SAAS,KAAK,UAAU,GAAG;AAC/C,eAAO,UAAU,YAAY,EAAE;AAAA,MAGnC,WAAU,WAAW,OAAO,MAAM,MAAK,IAAI;AACvC,cAAM,WAAW,WAAW,MAAM,mDAAmD;AAErF,YAAG,UAAS;AAER,cAAG,QAAQ,cAAa;AACpB,0BAAc,SAAS,CAAC,KAAK,MAAM,SAAS,CAAC;AAAA,UACjD,OAAK;AACD,gBAAG,SAAS,CAAC,MAAM,OAAO,SAAS,CAAC,EAAE,CAAC,MAAK,KAAI;AAAA,YAChD,OAAK;AACD,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO,QAAQ,YAAY,OAAO,UAAU,IAAI;AAAA,QACpD,OAAK;AACD,iBAAO;AAAA,QACX;AAAA,MAGJ,OAAK;AAED,cAAM,QAAQ,SAAS,KAAK,UAAU;AAEtC,YAAG,OAAM;AACL,gBAAM,OAAO,MAAM,CAAC;AACpB,gBAAM,eAAe,MAAM,CAAC;AAC5B,cAAI,oBAAoB,UAAU,MAAM,CAAC,CAAC;AAG1C,cAAG,CAAC,QAAQ,gBAAgB,aAAa,SAAS,KAAK,QAAQ,WAAW,CAAC,MAAM,IAAK,QAAO;AAAA,mBACrF,CAAC,QAAQ,gBAAgB,aAAa,SAAS,KAAK,CAAC,QAAQ,WAAW,CAAC,MAAM,IAAK,QAAO;AAAA,mBAC3F,QAAQ,gBAAgB,iBAAe,IAAK,QAAO;AAAA,eAEvD;AACA,kBAAM,MAAM,OAAO,UAAU;AAC7B,kBAAM,SAAS,KAAK;AAEpB,gBAAG,OAAO,OAAO,MAAM,MAAM,IAAG;AAC5B,kBAAG,QAAQ,UAAW,QAAO;AAAA,kBACxB,QAAO;AAAA,YAChB,WAAS,WAAW,QAAQ,GAAG,MAAM,IAAG;AACpC,kBAAG,WAAW,OAAQ,sBAAsB,GAAM,QAAO;AAAA,uBACjD,WAAW,kBAAmB,QAAO;AAAA,uBACpC,QAAQ,WAAW,MAAI,kBAAmB,QAAO;AAAA,kBACrD,QAAO;AAAA,YAChB;AAEA,gBAAG,cAAa;AACZ,qBAAQ,sBAAsB,UAAY,OAAK,sBAAsB,SAAU,MAAM;AAAA,YACzF,OAAO;AACH,qBAAQ,eAAe,UAAY,eAAe,OAAK,SAAU,MAAM;AAAA,YAC3E;AAAA,UACJ;AAAA,QACJ,OAAK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAOA,aAAS,UAAU,QAAO;AACtB,UAAG,UAAU,OAAO,QAAQ,GAAG,MAAM,IAAG;AACpC,iBAAS,OAAO,QAAQ,OAAO,EAAE;AACjC,YAAG,WAAW,IAAM,UAAS;AAAA,iBACrB,OAAO,CAAC,MAAM,IAAM,UAAS,MAAI;AAAA,iBACjC,OAAO,OAAO,SAAO,CAAC,MAAM,IAAM,UAAS,OAAO,OAAO,GAAE,OAAO,SAAO,CAAC;AAClF,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,QAAQ,MAAK;AAE5B,UAAG,SAAU,QAAO,SAAS,QAAQ,IAAI;AAAA,eACjC,OAAO,SAAU,QAAO,OAAO,SAAS,QAAQ,IAAI;AAAA,eACpD,UAAU,OAAO,SAAU,QAAO,OAAO,SAAS,QAAQ,IAAI;AAAA,UACjE,OAAM,IAAI,MAAM,8DAA8D;AAAA,IACvF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9GjB;AAAA;AAAA;AAGA,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,QAAM,cAAc;AACpB,QAAM,WAAW;AASjB,QAAM,mBAAN,MAAsB;AAAA,MACpB,YAAY,SAAQ;AAClB,aAAK,UAAU;AACf,aAAK,cAAc;AACnB,aAAK,gBAAgB,CAAC;AACtB,aAAK,kBAAkB,CAAC;AACxB,aAAK,eAAe;AAAA,UAClB,QAAS,EAAE,OAAO,sBAAsB,KAAM,IAAG;AAAA,UACjD,MAAO,EAAE,OAAO,oBAAoB,KAAM,IAAG;AAAA,UAC7C,MAAO,EAAE,OAAO,oBAAoB,KAAM,IAAG;AAAA,UAC7C,QAAS,EAAE,OAAO,sBAAsB,KAAM,IAAI;AAAA,QACpD;AACA,aAAK,YAAY,EAAE,OAAO,qBAAqB,KAAM,IAAG;AACxD,aAAK,eAAe;AAAA,UAClB,SAAS,EAAE,OAAO,kBAAkB,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAM7C,QAAS,EAAE,OAAO,kBAAkB,KAAK,IAAI;AAAA,UAC7C,SAAU,EAAE,OAAO,mBAAmB,KAAK,IAAI;AAAA,UAC/C,OAAQ,EAAE,OAAO,iBAAiB,KAAK,IAAI;AAAA,UAC3C,QAAS,EAAE,OAAO,mBAAmB,KAAK,IAAI;AAAA,UAC9C,aAAc,EAAE,OAAO,kBAAkB,KAAK,IAAI;AAAA,UAClD,OAAQ,EAAE,OAAO,iBAAiB,KAAK,IAAI;AAAA,UAC3C,OAAQ,EAAE,OAAO,kBAAkB,KAAK,IAAI;AAAA,UAC5C,WAAW,EAAE,OAAO,oBAAoB,KAAM,CAAC,GAAG,QAAQ,OAAO,aAAa,OAAO,SAAS,KAAK,EAAE,CAAC,EAAE;AAAA,UACxG,WAAW,EAAE,OAAO,2BAA2B,KAAM,CAAC,GAAG,QAAQ,OAAO,aAAa,OAAO,SAAS,KAAK,EAAE,CAAC,EAAE;AAAA,QACjH;AACA,aAAK,sBAAsB;AAC3B,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,aAAK,mBAAmB;AACxB,aAAK,qBAAqB;AAC1B,aAAK,eAAe;AACpB,aAAK,uBAAuB;AAC5B,aAAK,mBAAmB;AACxB,aAAK,sBAAsB;AAC3B,aAAK,WAAW;AAAA,MAClB;AAAA,IAEF;AAEA,aAAS,oBAAoB,kBAAiB;AAC5C,YAAM,UAAU,OAAO,KAAK,gBAAgB;AAC5C,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,MAAM,QAAQ,CAAC;AACrB,aAAK,aAAa,GAAG,IAAI;AAAA,UACtB,OAAO,IAAI,OAAO,MAAI,MAAI,KAAI,GAAG;AAAA,UACjC,KAAM,iBAAiB,GAAG;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAWA,aAAS,cAAcC,MAAK,SAAS,OAAO,UAAU,eAAe,YAAY,gBAAgB;AAC/F,UAAIA,SAAQ,QAAW;AACrB,YAAI,KAAK,QAAQ,cAAc,CAAC,UAAU;AACxC,UAAAA,OAAMA,KAAI,KAAK;AAAA,QACjB;AACA,YAAGA,KAAI,SAAS,GAAE;AAChB,cAAG,CAAC,eAAgB,CAAAA,OAAM,KAAK,qBAAqBA,IAAG;AAEvD,gBAAM,SAAS,KAAK,QAAQ,kBAAkB,SAASA,MAAK,OAAO,eAAe,UAAU;AAC5F,cAAG,WAAW,QAAQ,WAAW,QAAU;AAEzC,mBAAOA;AAAA,UACT,WAAS,OAAO,WAAW,OAAOA,QAAO,WAAWA,MAAI;AAEtD,mBAAO;AAAA,UACT,WAAS,KAAK,QAAQ,YAAW;AAC/B,mBAAO,WAAWA,MAAK,KAAK,QAAQ,eAAe,KAAK,QAAQ,kBAAkB;AAAA,UACpF,OAAK;AACH,kBAAM,aAAaA,KAAI,KAAK;AAC5B,gBAAG,eAAeA,MAAI;AACpB,qBAAO,WAAWA,MAAK,KAAK,QAAQ,eAAe,KAAK,QAAQ,kBAAkB;AAAA,YACpF,OAAK;AACH,qBAAOA;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,iBAAiB,SAAS;AACjC,UAAI,KAAK,QAAQ,gBAAgB;AAC/B,cAAM,OAAO,QAAQ,MAAM,GAAG;AAC9B,cAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,MAAM,MAAM;AACjD,YAAI,KAAK,CAAC,MAAM,SAAS;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,GAAG;AACrB,oBAAU,SAAS,KAAK,CAAC;AAAA,QAC3B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,QAAM,YAAY,IAAI,OAAO,+CAAgD,IAAI;AAEjF,aAAS,mBAAmB,SAAS,OAAO,SAAS;AACnD,UAAI,CAAC,KAAK,QAAQ,oBAAoB,OAAO,YAAY,UAAU;AAIjE,cAAM,UAAU,KAAK,cAAc,SAAS,SAAS;AACrD,cAAM,MAAM,QAAQ;AACpB,cAAM,QAAQ,CAAC;AACf,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,WAAW,KAAK,iBAAiB,QAAQ,CAAC,EAAE,CAAC,CAAC;AACpD,cAAI,SAAS,QAAQ,CAAC,EAAE,CAAC;AACzB,cAAI,QAAQ,KAAK,QAAQ,sBAAsB;AAC/C,cAAI,SAAS,QAAQ;AACnB,gBAAI,KAAK,QAAQ,wBAAwB;AACvC,sBAAQ,KAAK,QAAQ,uBAAuB,KAAK;AAAA,YACnD;AACA,gBAAG,UAAU,YAAa,SAAS;AACnC,gBAAI,WAAW,QAAW;AACxB,kBAAI,KAAK,QAAQ,YAAY;AAC3B,yBAAS,OAAO,KAAK;AAAA,cACvB;AACA,uBAAS,KAAK,qBAAqB,MAAM;AACzC,oBAAM,SAAS,KAAK,QAAQ,wBAAwB,UAAU,QAAQ,KAAK;AAC3E,kBAAG,WAAW,QAAQ,WAAW,QAAU;AAEzC,sBAAM,KAAK,IAAI;AAAA,cACjB,WAAS,OAAO,WAAW,OAAO,UAAU,WAAW,QAAO;AAE5D,sBAAM,KAAK,IAAI;AAAA,cACjB,OAAK;AAEH,sBAAM,KAAK,IAAI;AAAA,kBACb;AAAA,kBACA,KAAK,QAAQ;AAAA,kBACb,KAAK,QAAQ;AAAA,gBACf;AAAA,cACF;AAAA,YACF,WAAW,KAAK,QAAQ,wBAAwB;AAC9C,oBAAM,KAAK,IAAI;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,OAAO,KAAK,KAAK,EAAE,QAAQ;AAC9B;AAAA,QACF;AACA,YAAI,KAAK,QAAQ,qBAAqB;AACpC,gBAAM,iBAAiB,CAAC;AACxB,yBAAe,KAAK,QAAQ,mBAAmB,IAAI;AACnD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAM,WAAW,SAAS,SAAS;AACjC,gBAAU,QAAQ,QAAQ,UAAU,IAAI;AACxC,YAAM,SAAS,IAAI,QAAQ,MAAM;AACjC,UAAI,cAAc;AAClB,UAAI,WAAW;AACf,UAAI,QAAQ;AACZ,eAAQ,IAAE,GAAG,IAAG,QAAQ,QAAQ,KAAI;AAClC,cAAM,KAAK,QAAQ,CAAC;AACpB,YAAG,OAAO,KAAI;AAGZ,cAAI,QAAQ,IAAE,CAAC,MAAM,KAAK;AACxB,kBAAM,aAAa,iBAAiB,SAAS,KAAK,GAAG,4BAA4B;AACjF,gBAAI,UAAU,QAAQ,UAAU,IAAE,GAAE,UAAU,EAAE,KAAK;AAErD,gBAAG,KAAK,QAAQ,gBAAe;AAC7B,oBAAM,aAAa,QAAQ,QAAQ,GAAG;AACtC,kBAAG,eAAe,IAAG;AACnB,0BAAU,QAAQ,OAAO,aAAW,CAAC;AAAA,cACvC;AAAA,YACF;AAEA,gBAAG,KAAK,QAAQ,kBAAkB;AAChC,wBAAU,KAAK,QAAQ,iBAAiB,OAAO;AAAA,YACjD;AAEA,gBAAG,aAAY;AACb,yBAAW,KAAK,oBAAoB,UAAU,aAAa,KAAK;AAAA,YAClE;AAGA,kBAAM,cAAc,MAAM,UAAU,MAAM,YAAY,GAAG,IAAE,CAAC;AAC5D,gBAAG,WAAW,KAAK,QAAQ,aAAa,QAAQ,OAAO,MAAM,IAAI;AAC/D,oBAAM,IAAI,MAAM,kDAAkD,OAAO,GAAG;AAAA,YAC9E;AACA,gBAAI,YAAY;AAChB,gBAAG,eAAe,KAAK,QAAQ,aAAa,QAAQ,WAAW,MAAM,IAAI;AACvE,0BAAY,MAAM,YAAY,KAAK,MAAM,YAAY,GAAG,IAAE,CAAC;AAC3D,mBAAK,cAAc,IAAI;AAAA,YACzB,OAAK;AACH,0BAAY,MAAM,YAAY,GAAG;AAAA,YACnC;AACA,oBAAQ,MAAM,UAAU,GAAG,SAAS;AAEpC,0BAAc,KAAK,cAAc,IAAI;AACrC,uBAAW;AACX,gBAAI;AAAA,UACN,WAAW,QAAQ,IAAE,CAAC,MAAM,KAAK;AAE/B,gBAAI,UAAU,WAAW,SAAQ,GAAG,OAAO,IAAI;AAC/C,gBAAG,CAAC,QAAS,OAAM,IAAI,MAAM,uBAAuB;AAEpD,uBAAW,KAAK,oBAAoB,UAAU,aAAa,KAAK;AAChE,gBAAK,KAAK,QAAQ,qBAAqB,QAAQ,YAAY,UAAW,KAAK,QAAQ,cAAa;AAAA,YAEhG,OAAK;AAEH,oBAAM,YAAY,IAAI,QAAQ,QAAQ,OAAO;AAC7C,wBAAU,IAAI,KAAK,QAAQ,cAAc,EAAE;AAE3C,kBAAG,QAAQ,YAAY,QAAQ,UAAU,QAAQ,gBAAe;AAC9D,0BAAU,IAAI,IAAI,KAAK,mBAAmB,QAAQ,QAAQ,OAAO,QAAQ,OAAO;AAAA,cAClF;AACA,mBAAK,SAAS,aAAa,WAAW,KAAK;AAAA,YAE7C;AAGA,gBAAI,QAAQ,aAAa;AAAA,UAC3B,WAAU,QAAQ,OAAO,IAAI,GAAG,CAAC,MAAM,OAAO;AAC5C,kBAAM,WAAW,iBAAiB,SAAS,OAAO,IAAE,GAAG,wBAAwB;AAC/E,gBAAG,KAAK,QAAQ,iBAAgB;AAC9B,oBAAM,UAAU,QAAQ,UAAU,IAAI,GAAG,WAAW,CAAC;AAErD,yBAAW,KAAK,oBAAoB,UAAU,aAAa,KAAK;AAEhE,0BAAY,IAAI,KAAK,QAAQ,iBAAiB,CAAE,EAAE,CAAC,KAAK,QAAQ,YAAY,GAAI,QAAQ,CAAE,CAAC;AAAA,YAC7F;AACA,gBAAI;AAAA,UACN,WAAW,QAAQ,OAAO,IAAI,GAAG,CAAC,MAAM,MAAM;AAC5C,kBAAM,SAAS,YAAY,SAAS,CAAC;AACrC,iBAAK,kBAAkB,OAAO;AAC9B,gBAAI,OAAO;AAAA,UACb,WAAS,QAAQ,OAAO,IAAI,GAAG,CAAC,MAAM,MAAM;AAC1C,kBAAM,aAAa,iBAAiB,SAAS,OAAO,GAAG,sBAAsB,IAAI;AACjF,kBAAM,SAAS,QAAQ,UAAU,IAAI,GAAE,UAAU;AAEjD,uBAAW,KAAK,oBAAoB,UAAU,aAAa,KAAK;AAEhE,gBAAIA,OAAM,KAAK,cAAc,QAAQ,YAAY,SAAS,OAAO,MAAM,OAAO,MAAM,IAAI;AACxF,gBAAGA,QAAO,OAAW,CAAAA,OAAM;AAG3B,gBAAG,KAAK,QAAQ,eAAc;AAC5B,0BAAY,IAAI,KAAK,QAAQ,eAAe,CAAE,EAAE,CAAC,KAAK,QAAQ,YAAY,GAAI,OAAO,CAAE,CAAC;AAAA,YAC1F,OAAK;AACH,0BAAY,IAAI,KAAK,QAAQ,cAAcA,IAAG;AAAA,YAChD;AAEA,gBAAI,aAAa;AAAA,UACnB,OAAM;AACJ,gBAAI,SAAS,WAAW,SAAQ,GAAG,KAAK,QAAQ,cAAc;AAC9D,gBAAI,UAAS,OAAO;AACpB,kBAAM,aAAa,OAAO;AAC1B,gBAAI,SAAS,OAAO;AACpB,gBAAI,iBAAiB,OAAO;AAC5B,gBAAI,aAAa,OAAO;AAExB,gBAAI,KAAK,QAAQ,kBAAkB;AACjC,wBAAU,KAAK,QAAQ,iBAAiB,OAAO;AAAA,YACjD;AAGA,gBAAI,eAAe,UAAU;AAC3B,kBAAG,YAAY,YAAY,QAAO;AAEhC,2BAAW,KAAK,oBAAoB,UAAU,aAAa,OAAO,KAAK;AAAA,cACzE;AAAA,YACF;AAGA,kBAAM,UAAU;AAChB,gBAAG,WAAW,KAAK,QAAQ,aAAa,QAAQ,QAAQ,OAAO,MAAM,IAAI;AACvE,4BAAc,KAAK,cAAc,IAAI;AACrC,sBAAQ,MAAM,UAAU,GAAG,MAAM,YAAY,GAAG,CAAC;AAAA,YACnD;AACA,gBAAG,YAAY,OAAO,SAAQ;AAC5B,uBAAS,QAAQ,MAAM,UAAU;AAAA,YACnC;AACA,gBAAI,KAAK,aAAa,KAAK,QAAQ,WAAW,OAAO,OAAO,GAAG;AAC7D,kBAAI,aAAa;AAEjB,kBAAG,OAAO,SAAS,KAAK,OAAO,YAAY,GAAG,MAAM,OAAO,SAAS,GAAE;AACpE,oBAAG,QAAQ,QAAQ,SAAS,CAAC,MAAM,KAAI;AACrC,4BAAU,QAAQ,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC9C,0BAAQ,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC;AACxC,2BAAS;AAAA,gBACX,OAAK;AACH,2BAAS,OAAO,OAAO,GAAG,OAAO,SAAS,CAAC;AAAA,gBAC7C;AACA,oBAAI,OAAO;AAAA,cACb,WAEQ,KAAK,QAAQ,aAAa,QAAQ,OAAO,MAAM,IAAG;AAExD,oBAAI,OAAO;AAAA,cACb,OAEI;AAEF,sBAAMC,UAAS,KAAK,iBAAiB,SAAS,YAAY,aAAa,CAAC;AACxE,oBAAG,CAACA,QAAQ,OAAM,IAAI,MAAM,qBAAqB,UAAU,EAAE;AAC7D,oBAAIA,QAAO;AACX,6BAAaA,QAAO;AAAA,cACtB;AAEA,oBAAM,YAAY,IAAI,QAAQ,OAAO;AACrC,kBAAG,YAAY,UAAU,gBAAe;AACtC,0BAAU,IAAI,IAAI,KAAK,mBAAmB,QAAQ,OAAO,OAAO;AAAA,cAClE;AACA,kBAAG,YAAY;AACb,6BAAa,KAAK,cAAc,YAAY,SAAS,OAAO,MAAM,gBAAgB,MAAM,IAAI;AAAA,cAC9F;AAEA,sBAAQ,MAAM,OAAO,GAAG,MAAM,YAAY,GAAG,CAAC;AAC9C,wBAAU,IAAI,KAAK,QAAQ,cAAc,UAAU;AAEnD,mBAAK,SAAS,aAAa,WAAW,KAAK;AAAA,YAC7C,OAAK;AAEH,kBAAG,OAAO,SAAS,KAAK,OAAO,YAAY,GAAG,MAAM,OAAO,SAAS,GAAE;AACpE,oBAAG,QAAQ,QAAQ,SAAS,CAAC,MAAM,KAAI;AACrC,4BAAU,QAAQ,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC9C,0BAAQ,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC;AACxC,2BAAS;AAAA,gBACX,OAAK;AACH,2BAAS,OAAO,OAAO,GAAG,OAAO,SAAS,CAAC;AAAA,gBAC7C;AAEA,oBAAG,KAAK,QAAQ,kBAAkB;AAChC,4BAAU,KAAK,QAAQ,iBAAiB,OAAO;AAAA,gBACjD;AAEA,sBAAM,YAAY,IAAI,QAAQ,OAAO;AACrC,oBAAG,YAAY,UAAU,gBAAe;AACtC,4BAAU,IAAI,IAAI,KAAK,mBAAmB,QAAQ,OAAO,OAAO;AAAA,gBAClE;AACA,qBAAK,SAAS,aAAa,WAAW,KAAK;AAC3C,wBAAQ,MAAM,OAAO,GAAG,MAAM,YAAY,GAAG,CAAC;AAAA,cAChD,OAEI;AACF,sBAAM,YAAY,IAAI,QAAS,OAAO;AACtC,qBAAK,cAAc,KAAK,WAAW;AAEnC,oBAAG,YAAY,UAAU,gBAAe;AACtC,4BAAU,IAAI,IAAI,KAAK,mBAAmB,QAAQ,OAAO,OAAO;AAAA,gBAClE;AACA,qBAAK,SAAS,aAAa,WAAW,KAAK;AAC3C,8BAAc;AAAA,cAChB;AACA,yBAAW;AACX,kBAAI;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAK;AACH,sBAAY,QAAQ,CAAC;AAAA,QACvB;AAAA,MACF;AACA,aAAO,OAAO;AAAA,IAChB;AAEA,aAAS,SAAS,aAAa,WAAW,OAAM;AAC9C,YAAM,SAAS,KAAK,QAAQ,UAAU,UAAU,SAAS,OAAO,UAAU,IAAI,CAAC;AAC/E,UAAG,WAAW,OAAM;AAAA,MACpB,WAAS,OAAO,WAAW,UAAS;AAClC,kBAAU,UAAU;AACpB,oBAAY,SAAS,SAAS;AAAA,MAChC,OAAK;AACH,oBAAY,SAAS,SAAS;AAAA,MAChC;AAAA,IACF;AAEA,QAAM,uBAAuB,SAASD,MAAI;AAExC,UAAG,KAAK,QAAQ,iBAAgB;AAC9B,iBAAQE,eAAc,KAAK,iBAAgB;AACzC,gBAAM,SAAS,KAAK,gBAAgBA,WAAU;AAC9C,UAAAF,OAAMA,KAAI,QAAS,OAAO,MAAM,OAAO,GAAG;AAAA,QAC5C;AACA,iBAAQE,eAAc,KAAK,cAAa;AACtC,gBAAM,SAAS,KAAK,aAAaA,WAAU;AAC3C,UAAAF,OAAMA,KAAI,QAAS,OAAO,OAAO,OAAO,GAAG;AAAA,QAC7C;AACA,YAAG,KAAK,QAAQ,cAAa;AAC3B,mBAAQE,eAAc,KAAK,cAAa;AACtC,kBAAM,SAAS,KAAK,aAAaA,WAAU;AAC3C,YAAAF,OAAMA,KAAI,QAAS,OAAO,OAAO,OAAO,GAAG;AAAA,UAC7C;AAAA,QACF;AACA,QAAAA,OAAMA,KAAI,QAAS,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG;AAAA,MAC7D;AACA,aAAOA;AAAA,IACT;AACA,aAAS,oBAAoB,UAAU,aAAa,OAAO,YAAY;AACrE,UAAI,UAAU;AACZ,YAAG,eAAe,OAAW,cAAa,OAAO,KAAK,YAAY,KAAK,EAAE,WAAW;AAEpF,mBAAW,KAAK;AAAA,UAAc;AAAA,UAC5B,YAAY;AAAA,UACZ;AAAA,UACA;AAAA,UACA,YAAY,IAAI,IAAI,OAAO,KAAK,YAAY,IAAI,CAAC,EAAE,WAAW,IAAI;AAAA,UAClE;AAAA,QAAU;AAEZ,YAAI,aAAa,UAAa,aAAa;AACzC,sBAAY,IAAI,KAAK,QAAQ,cAAc,QAAQ;AACrD,mBAAW;AAAA,MACb;AACA,aAAO;AAAA,IACT;AASA,aAAS,aAAa,WAAW,OAAO,gBAAe;AACrD,YAAM,cAAc,OAAO;AAC3B,iBAAW,gBAAgB,WAAW;AACpC,cAAM,cAAc,UAAU,YAAY;AAC1C,YAAI,gBAAgB,eAAe,UAAU,YAAe,QAAO;AAAA,MACrE;AACA,aAAO;AAAA,IACT;AAQA,aAAS,uBAAuB,SAAS,GAAG,cAAc,KAAI;AAC5D,UAAI;AACJ,UAAI,SAAS;AACb,eAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACnD,YAAI,KAAK,QAAQ,KAAK;AACtB,YAAI,cAAc;AACd,cAAI,OAAO,aAAc,gBAAe;AAAA,QAC5C,WAAW,OAAO,OAAO,OAAO,KAAK;AACjC,yBAAe;AAAA,QACnB,WAAW,OAAO,YAAY,CAAC,GAAG;AAChC,cAAG,YAAY,CAAC,GAAE;AAChB,gBAAG,QAAQ,QAAQ,CAAC,MAAM,YAAY,CAAC,GAAE;AACvC,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF,OAAK;AACH,mBAAO;AAAA,cACL,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,OAAO,KAAM;AACtB,eAAK;AAAA,QACP;AACA,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,aAAS,iBAAiB,SAAS,KAAK,GAAG,QAAO;AAChD,YAAM,eAAe,QAAQ,QAAQ,KAAK,CAAC;AAC3C,UAAG,iBAAiB,IAAG;AACrB,cAAM,IAAI,MAAM,MAAM;AAAA,MACxB,OAAK;AACH,eAAO,eAAe,IAAI,SAAS;AAAA,MACrC;AAAA,IACF;AAEA,aAAS,WAAW,SAAQ,GAAG,gBAAgB,cAAc,KAAI;AAC/D,YAAM,SAAS,uBAAuB,SAAS,IAAE,GAAG,WAAW;AAC/D,UAAG,CAAC,OAAQ;AACZ,UAAI,SAAS,OAAO;AACpB,YAAM,aAAa,OAAO;AAC1B,YAAM,iBAAiB,OAAO,OAAO,IAAI;AACzC,UAAI,UAAU;AACd,UAAI,iBAAiB;AACrB,UAAG,mBAAmB,IAAG;AACvB,kBAAU,OAAO,UAAU,GAAG,cAAc;AAC5C,iBAAS,OAAO,UAAU,iBAAiB,CAAC,EAAE,UAAU;AAAA,MAC1D;AAEA,YAAM,aAAa;AACnB,UAAG,gBAAe;AAChB,cAAM,aAAa,QAAQ,QAAQ,GAAG;AACtC,YAAG,eAAe,IAAG;AACnB,oBAAU,QAAQ,OAAO,aAAW,CAAC;AACrC,2BAAiB,YAAY,OAAO,KAAK,OAAO,aAAa,CAAC;AAAA,QAChE;AAAA,MACF;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAOA,aAAS,iBAAiB,SAAS,SAAS,GAAE;AAC5C,YAAM,aAAa;AAEnB,UAAI,eAAe;AAEnB,aAAO,IAAI,QAAQ,QAAQ,KAAK;AAC9B,YAAI,QAAQ,CAAC,MAAM,KAAI;AACrB,cAAI,QAAQ,IAAE,CAAC,MAAM,KAAK;AACtB,kBAAM,aAAa,iBAAiB,SAAS,KAAK,GAAG,GAAG,OAAO,gBAAgB;AAC/E,gBAAI,eAAe,QAAQ,UAAU,IAAE,GAAE,UAAU,EAAE,KAAK;AAC1D,gBAAG,iBAAiB,SAAQ;AAC1B;AACA,kBAAI,iBAAiB,GAAG;AACtB,uBAAO;AAAA,kBACL,YAAY,QAAQ,UAAU,YAAY,CAAC;AAAA,kBAC3C,GAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AACA,gBAAE;AAAA,UACJ,WAAU,QAAQ,IAAE,CAAC,MAAM,KAAK;AAC9B,kBAAM,aAAa,iBAAiB,SAAS,MAAM,IAAE,GAAG,yBAAyB;AACjF,gBAAE;AAAA,UACJ,WAAU,QAAQ,OAAO,IAAI,GAAG,CAAC,MAAM,OAAO;AAC5C,kBAAM,aAAa,iBAAiB,SAAS,OAAO,IAAE,GAAG,yBAAyB;AAClF,gBAAE;AAAA,UACJ,WAAU,QAAQ,OAAO,IAAI,GAAG,CAAC,MAAM,MAAM;AAC3C,kBAAM,aAAa,iBAAiB,SAAS,OAAO,GAAG,yBAAyB,IAAI;AACpF,gBAAE;AAAA,UACJ,OAAO;AACL,kBAAM,UAAU,WAAW,SAAS,GAAG,GAAG;AAE1C,gBAAI,SAAS;AACX,oBAAM,cAAc,WAAW,QAAQ;AACvC,kBAAI,gBAAgB,WAAW,QAAQ,OAAO,QAAQ,OAAO,SAAO,CAAC,MAAM,KAAK;AAC9E;AAAA,cACF;AACA,kBAAE,QAAQ;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AAEA,aAAS,WAAWA,MAAK,aAAa,SAAS;AAC7C,UAAI,eAAe,OAAOA,SAAQ,UAAU;AAE1C,cAAM,SAASA,KAAI,KAAK;AACxB,YAAG,WAAW,OAAS,QAAO;AAAA,iBACtB,WAAW,QAAU,QAAO;AAAA,YAC/B,QAAO,SAASA,MAAK,OAAO;AAAA,MACnC,OAAO;AACL,YAAI,KAAK,QAAQA,IAAG,GAAG;AACrB,iBAAOA;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACxlBjB;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM,SAAQ;AAC9B,aAAO,SAAU,MAAM,OAAO;AAAA,IAChC;AASA,aAAS,SAAS,KAAK,SAAS,OAAM;AACpC,UAAI;AACJ,YAAM,gBAAgB,CAAC;AACvB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,SAAS,IAAI,CAAC;AACpB,cAAM,WAAW,SAAS,MAAM;AAChC,YAAI,WAAW;AACf,YAAG,UAAU,OAAW,YAAW;AAAA,YAC9B,YAAW,QAAQ,MAAM;AAE9B,YAAG,aAAa,QAAQ,cAAa;AACnC,cAAG,SAAS,OAAW,QAAO,OAAO,QAAQ;AAAA,cACxC,SAAQ,KAAK,OAAO,QAAQ;AAAA,QACnC,WAAS,aAAa,QAAU;AAC9B;AAAA,QACF,WAAS,OAAO,QAAQ,GAAE;AAExB,cAAIG,OAAM,SAAS,OAAO,QAAQ,GAAG,SAAS,QAAQ;AACtD,gBAAM,SAAS,UAAUA,MAAK,OAAO;AAErC,cAAG,OAAO,IAAI,GAAE;AACd,6BAAkBA,MAAK,OAAO,IAAI,GAAG,UAAU,OAAO;AAAA,UACxD,WAAS,OAAO,KAAKA,IAAG,EAAE,WAAW,KAAKA,KAAI,QAAQ,YAAY,MAAM,UAAa,CAAC,QAAQ,sBAAqB;AACjH,YAAAA,OAAMA,KAAI,QAAQ,YAAY;AAAA,UAChC,WAAS,OAAO,KAAKA,IAAG,EAAE,WAAW,GAAE;AACrC,gBAAG,QAAQ,qBAAsB,CAAAA,KAAI,QAAQ,YAAY,IAAI;AAAA,gBACxD,CAAAA,OAAM;AAAA,UACb;AAEA,cAAG,cAAc,QAAQ,MAAM,UAAa,cAAc,eAAe,QAAQ,GAAG;AAClF,gBAAG,CAAC,MAAM,QAAQ,cAAc,QAAQ,CAAC,GAAG;AACxC,4BAAc,QAAQ,IAAI,CAAE,cAAc,QAAQ,CAAE;AAAA,YACxD;AACA,0BAAc,QAAQ,EAAE,KAAKA,IAAG;AAAA,UAClC,OAAK;AAGH,gBAAI,QAAQ,QAAQ,UAAU,UAAU,MAAO,GAAG;AAChD,4BAAc,QAAQ,IAAI,CAACA,IAAG;AAAA,YAChC,OAAK;AACH,4BAAc,QAAQ,IAAIA;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AAAA,MAEF;AAEA,UAAG,OAAO,SAAS,UAAS;AAC1B,YAAG,KAAK,SAAS,EAAG,eAAc,QAAQ,YAAY,IAAI;AAAA,MAC5D,WAAS,SAAS,OAAW,eAAc,QAAQ,YAAY,IAAI;AACnE,aAAO;AAAA,IACT;AAEA,aAAS,SAAS,KAAI;AACpB,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAG,QAAQ,KAAM,QAAO;AAAA,MAC1B;AAAA,IACF;AAEA,aAAS,iBAAiB,KAAK,SAAS,OAAO,SAAQ;AACrD,UAAI,SAAS;AACX,cAAM,OAAO,OAAO,KAAK,OAAO;AAChC,cAAM,MAAM,KAAK;AACjB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,WAAW,KAAK,CAAC;AACvB,cAAI,QAAQ,QAAQ,UAAU,QAAQ,MAAM,UAAU,MAAM,IAAI,GAAG;AACjE,gBAAI,QAAQ,IAAI,CAAE,QAAQ,QAAQ,CAAE;AAAA,UACtC,OAAO;AACL,gBAAI,QAAQ,IAAI,QAAQ,QAAQ;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,UAAU,KAAK,SAAQ;AAC9B,YAAM,EAAE,aAAa,IAAI;AACzB,YAAM,YAAY,OAAO,KAAK,GAAG,EAAE;AAEnC,UAAI,cAAc,GAAG;AACnB,eAAO;AAAA,MACT;AAEA,UACE,cAAc,MACb,IAAI,YAAY,KAAK,OAAO,IAAI,YAAY,MAAM,aAAa,IAAI,YAAY,MAAM,IACtF;AACA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AACA,YAAQ,WAAW;AAAA;AAAA;;;AChHnB;AAAA;AAAA,QAAM,EAAE,aAAY,IAAI;AACxB,QAAM,mBAAmB;AACzB,QAAM,EAAE,SAAQ,IAAI;AACpB,QAAM,YAAY;AAElB,QAAMC,aAAN,MAAe;AAAA,MAEX,YAAY,SAAQ;AAChB,aAAK,mBAAmB,CAAC;AACzB,aAAK,UAAU,aAAa,OAAO;AAAA,MAEvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAM,SAAQ,kBAAiB;AAC3B,YAAG,OAAO,YAAY,UAAS;AAAA,QAC/B,WAAU,QAAQ,UAAS;AACvB,oBAAU,QAAQ,SAAS;AAAA,QAC/B,OAAK;AACD,gBAAM,IAAI,MAAM,iDAAiD;AAAA,QACrE;AACA,YAAI,kBAAiB;AACjB,cAAG,qBAAqB,KAAM,oBAAmB,CAAC;AAElD,gBAAM,SAAS,UAAU,SAAS,SAAS,gBAAgB;AAC3D,cAAI,WAAW,MAAM;AACnB,kBAAM,MAAO,GAAG,OAAO,IAAI,GAAG,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,IAAI,GAAG,EAAG;AAAA,UACxE;AAAA,QACF;AACF,cAAM,mBAAmB,IAAI,iBAAiB,KAAK,OAAO;AAC1D,yBAAiB,oBAAoB,KAAK,gBAAgB;AAC1D,cAAM,gBAAgB,iBAAiB,SAAS,OAAO;AACvD,YAAG,KAAK,QAAQ,iBAAiB,kBAAkB,OAAW,QAAO;AAAA,YAChE,QAAO,SAAS,eAAe,KAAK,OAAO;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,KAAK,OAAM;AACjB,YAAG,MAAM,QAAQ,GAAG,MAAM,IAAG;AACzB,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QACjD,WAAS,IAAI,QAAQ,GAAG,MAAM,MAAM,IAAI,QAAQ,GAAG,MAAM,IAAG;AACxD,gBAAM,IAAI,MAAM,sEAAsE;AAAA,QAC1F,WAAS,UAAU,KAAI;AACnB,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC/D,OAAK;AACD,eAAK,iBAAiB,GAAG,IAAI;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,UAAUA;AAAA;AAAA;;;ACzDjB;AAAA;AAAA,QAAM,MAAM;AAQZ,aAAS,MAAM,QAAQ,SAAS;AAC5B,UAAI,cAAc;AAClB,UAAI,QAAQ,UAAU,QAAQ,SAAS,SAAS,GAAG;AAC/C,sBAAc;AAAA,MAClB;AACA,aAAO,SAAS,QAAQ,SAAS,IAAI,WAAW;AAAA,IACpD;AAEA,aAAS,SAAS,KAAK,SAAS,OAAO,aAAa;AAChD,UAAI,SAAS;AACb,UAAI,uBAAuB;AAE3B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,cAAM,SAAS,IAAI,CAAC;AACpB,cAAM,UAAU,SAAS,MAAM;AAC/B,YAAG,YAAY,OAAW;AAE1B,YAAI,WAAW;AACf,YAAI,MAAM,WAAW,EAAG,YAAW;AAAA,YAC9B,YAAW,GAAG,KAAK,IAAI,OAAO;AAEnC,YAAI,YAAY,QAAQ,cAAc;AAClC,cAAI,UAAU,OAAO,OAAO;AAC5B,cAAI,CAAC,WAAW,UAAU,OAAO,GAAG;AAChC,sBAAU,QAAQ,kBAAkB,SAAS,OAAO;AACpD,sBAAU,qBAAqB,SAAS,OAAO;AAAA,UACnD;AACA,cAAI,sBAAsB;AACtB,sBAAU;AAAA,UACd;AACA,oBAAU;AACV,iCAAuB;AACvB;AAAA,QACJ,WAAW,YAAY,QAAQ,eAAe;AAC1C,cAAI,sBAAsB;AACtB,sBAAU;AAAA,UACd;AACA,oBAAU,YAAY,OAAO,OAAO,EAAE,CAAC,EAAE,QAAQ,YAAY,CAAC;AAC9D,iCAAuB;AACvB;AAAA,QACJ,WAAW,YAAY,QAAQ,iBAAiB;AAC5C,oBAAU,cAAc,OAAO,OAAO,OAAO,EAAE,CAAC,EAAE,QAAQ,YAAY,CAAC;AACvE,iCAAuB;AACvB;AAAA,QACJ,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC3B,gBAAMC,UAAS,YAAY,OAAO,IAAI,GAAG,OAAO;AAChD,gBAAM,UAAU,YAAY,SAAS,KAAK;AAC1C,cAAI,iBAAiB,OAAO,OAAO,EAAE,CAAC,EAAE,QAAQ,YAAY;AAC5D,2BAAiB,eAAe,WAAW,IAAI,MAAM,iBAAiB;AACtE,oBAAU,UAAU,IAAI,OAAO,GAAG,cAAc,GAAGA,OAAM;AACzD,iCAAuB;AACvB;AAAA,QACJ;AACA,YAAI,gBAAgB;AACpB,YAAI,kBAAkB,IAAI;AACtB,2BAAiB,QAAQ;AAAA,QAC7B;AACA,cAAM,SAAS,YAAY,OAAO,IAAI,GAAG,OAAO;AAChD,cAAM,WAAW,cAAc,IAAI,OAAO,GAAG,MAAM;AACnD,cAAM,WAAW,SAAS,OAAO,OAAO,GAAG,SAAS,UAAU,aAAa;AAC3E,YAAI,QAAQ,aAAa,QAAQ,OAAO,MAAM,IAAI;AAC9C,cAAI,QAAQ,qBAAsB,WAAU,WAAW;AAAA,cAClD,WAAU,WAAW;AAAA,QAC9B,YAAY,CAAC,YAAY,SAAS,WAAW,MAAM,QAAQ,mBAAmB;AAC1E,oBAAU,WAAW;AAAA,QACzB,WAAW,YAAY,SAAS,SAAS,GAAG,GAAG;AAC3C,oBAAU,WAAW,IAAI,QAAQ,GAAG,WAAW,KAAK,OAAO;AAAA,QAC/D,OAAO;AACH,oBAAU,WAAW;AACrB,cAAI,YAAY,gBAAgB,OAAO,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,IAAI,IAAI;AACxF,sBAAU,cAAc,QAAQ,WAAW,WAAW;AAAA,UAC1D,OAAO;AACH,sBAAU;AAAA,UACd;AACA,oBAAU,KAAK,OAAO;AAAA,QAC1B;AACA,+BAAuB;AAAA,MAC3B;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,SAAS,KAAK;AACnB,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAG,CAAC,IAAI,eAAe,GAAG,EAAG;AAC7B,YAAI,QAAQ,KAAM,QAAO;AAAA,MAC7B;AAAA,IACJ;AAEA,aAAS,YAAY,SAAS,SAAS;AACnC,UAAI,UAAU;AACd,UAAI,WAAW,CAAC,QAAQ,kBAAkB;AACtC,iBAAS,QAAQ,SAAS;AACtB,cAAG,CAAC,QAAQ,eAAe,IAAI,EAAG;AAClC,cAAI,UAAU,QAAQ,wBAAwB,MAAM,QAAQ,IAAI,CAAC;AACjE,oBAAU,qBAAqB,SAAS,OAAO;AAC/C,cAAI,YAAY,QAAQ,QAAQ,2BAA2B;AACvD,uBAAW,IAAI,KAAK,OAAO,QAAQ,oBAAoB,MAAM,CAAC;AAAA,UAClE,OAAO;AACH,uBAAW,IAAI,KAAK,OAAO,QAAQ,oBAAoB,MAAM,CAAC,KAAK,OAAO;AAAA,UAC9E;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,aAAS,WAAW,OAAO,SAAS;AAChC,cAAQ,MAAM,OAAO,GAAG,MAAM,SAAS,QAAQ,aAAa,SAAS,CAAC;AACtE,UAAI,UAAU,MAAM,OAAO,MAAM,YAAY,GAAG,IAAI,CAAC;AACrD,eAAS,SAAS,QAAQ,WAAW;AACjC,YAAI,QAAQ,UAAU,KAAK,MAAM,SAAS,QAAQ,UAAU,KAAK,MAAM,OAAO,QAAS,QAAO;AAAA,MAClG;AACA,aAAO;AAAA,IACX;AAEA,aAAS,qBAAqB,WAAW,SAAS;AAC9C,UAAI,aAAa,UAAU,SAAS,KAAK,QAAQ,iBAAiB;AAC9D,iBAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,QAAQ,KAAK;AAC9C,gBAAM,SAAS,QAAQ,SAAS,CAAC;AACjC,sBAAY,UAAU,QAAQ,OAAO,OAAO,OAAO,GAAG;AAAA,QAC1D;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,UAAU;AAAA;AAAA;;;ACtIjB;AAAA;AAAA;AAEA,QAAM,qBAAqB;AAE3B,QAAM,iBAAiB;AAAA,MACrB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,sBAAsB;AAAA,MACtB,2BAA2B;AAAA,MAC3B,mBAAmB,SAAS,KAAK,GAAG;AAClC,eAAO;AAAA,MACT;AAAA,MACA,yBAAyB,SAAS,UAAU,GAAG;AAC7C,eAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,cAAc,CAAC;AAAA,MACf,UAAU;AAAA,QACR,EAAE,OAAO,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,QAAQ;AAAA;AAAA,QAC5C,EAAE,OAAO,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,OAAO;AAAA,QAC3C,EAAE,OAAO,IAAI,OAAO,KAAK,GAAG,GAAG,KAAK,OAAO;AAAA,QAC3C,EAAE,OAAO,IAAI,OAAO,KAAM,GAAG,GAAG,KAAK,SAAS;AAAA,QAC9C,EAAE,OAAO,IAAI,OAAO,KAAM,GAAG,GAAG,KAAK,SAAS;AAAA,MAChD;AAAA,MACA,iBAAiB;AAAA,MACjB,WAAW,CAAC;AAAA;AAAA;AAAA,MAGZ,cAAc;AAAA,IAChB;AAEA,aAAS,QAAQ,SAAS;AACxB,WAAK,UAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,OAAO;AACxD,UAAI,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,qBAAqB;AACrE,aAAK,cAAc,WAAgB;AACjC,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,aAAK,gBAAgB,KAAK,QAAQ,oBAAoB;AACtD,aAAK,cAAc;AAAA,MACrB;AAEA,WAAK,uBAAuB;AAE5B,UAAI,KAAK,QAAQ,QAAQ;AACvB,aAAK,YAAY;AACjB,aAAK,aAAa;AAClB,aAAK,UAAU;AAAA,MACjB,OAAO;AACL,aAAK,YAAY,WAAW;AAC1B,iBAAO;AAAA,QACT;AACA,aAAK,aAAa;AAClB,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAEA,YAAQ,UAAU,QAAQ,SAAS,MAAM;AACvC,UAAG,KAAK,QAAQ,eAAc;AAC5B,eAAO,mBAAmB,MAAM,KAAK,OAAO;AAAA,MAC9C,OAAM;AACJ,YAAG,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,cAAc,SAAS,GAAE;AAC5F,iBAAO;AAAA,YACL,CAAC,KAAK,QAAQ,aAAa,GAAI;AAAA,UACjC;AAAA,QACF;AACA,eAAO,KAAK,IAAI,MAAM,CAAC,EAAE;AAAA,MAC3B;AAAA,IACF;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM,OAAO;AAC5C,UAAI,UAAU;AACd,UAAIC,OAAM;AACV,eAAS,OAAO,MAAM;AACpB,YAAG,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,EAAG;AACrD,YAAI,OAAO,KAAK,GAAG,MAAM,aAAa;AAEpC,cAAI,KAAK,YAAY,GAAG,GAAG;AACzB,YAAAA,QAAO;AAAA,UACT;AAAA,QACF,WAAW,KAAK,GAAG,MAAM,MAAM;AAE7B,cAAI,KAAK,YAAY,GAAG,GAAG;AACzB,YAAAA,QAAO;AAAA,UACT,WAAW,IAAI,CAAC,MAAM,KAAK;AACzB,YAAAA,QAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK;AAAA,UACxD,OAAO;AACL,YAAAA,QAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK;AAAA,UACxD;AAAA,QAEF,WAAW,KAAK,GAAG,aAAa,MAAM;AACpC,UAAAA,QAAO,KAAK,iBAAiB,KAAK,GAAG,GAAG,KAAK,IAAI,KAAK;AAAA,QACxD,WAAW,OAAO,KAAK,GAAG,MAAM,UAAU;AAExC,gBAAM,OAAO,KAAK,YAAY,GAAG;AACjC,cAAI,MAAM;AACR,uBAAW,KAAK,iBAAiB,MAAM,KAAK,KAAK,GAAG,CAAC;AAAA,UACvD,OAAM;AAEJ,gBAAI,QAAQ,KAAK,QAAQ,cAAc;AACrC,kBAAI,SAAS,KAAK,QAAQ,kBAAkB,KAAK,KAAK,KAAK,GAAG,CAAC;AAC/D,cAAAA,QAAO,KAAK,qBAAqB,MAAM;AAAA,YACzC,OAAO;AACL,cAAAA,QAAO,KAAK,iBAAiB,KAAK,GAAG,GAAG,KAAK,IAAI,KAAK;AAAA,YACxD;AAAA,UACF;AAAA,QACF,WAAW,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AAEnC,gBAAM,SAAS,KAAK,GAAG,EAAE;AACzB,cAAI,aAAa;AACjB,cAAI,cAAc;AAClB,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,kBAAM,OAAO,KAAK,GAAG,EAAE,CAAC;AACxB,gBAAI,OAAO,SAAS,aAAa;AAAA,YAEjC,WAAW,SAAS,MAAM;AACxB,kBAAG,IAAI,CAAC,MAAM,IAAK,CAAAA,QAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK;AAAA,kBACpE,CAAAA,QAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK;AAAA,YAE7D,WAAW,OAAO,SAAS,UAAU;AACnC,kBAAG,KAAK,QAAQ,cAAa;AAC3B,sBAAM,SAAS,KAAK,IAAI,MAAM,QAAQ,CAAC;AACvC,8BAAc,OAAO;AACrB,oBAAI,KAAK,QAAQ,uBAAuB,KAAK,eAAe,KAAK,QAAQ,mBAAmB,GAAG;AAC7F,iCAAe,OAAO;AAAA,gBACxB;AAAA,cACF,OAAK;AACH,8BAAc,KAAK,qBAAqB,MAAM,KAAK,KAAK;AAAA,cAC1D;AAAA,YACF,OAAO;AACL,kBAAI,KAAK,QAAQ,cAAc;AAC7B,oBAAI,YAAY,KAAK,QAAQ,kBAAkB,KAAK,IAAI;AACxD,4BAAY,KAAK,qBAAqB,SAAS;AAC/C,8BAAc;AAAA,cAChB,OAAO;AACL,8BAAc,KAAK,iBAAiB,MAAM,KAAK,IAAI,KAAK;AAAA,cAC1D;AAAA,YACF;AAAA,UACF;AACA,cAAG,KAAK,QAAQ,cAAa;AAC3B,yBAAa,KAAK,gBAAgB,YAAY,KAAK,aAAa,KAAK;AAAA,UACvE;AACA,UAAAA,QAAO;AAAA,QACT,OAAO;AAEL,cAAI,KAAK,QAAQ,uBAAuB,QAAQ,KAAK,QAAQ,qBAAqB;AAChF,kBAAM,KAAK,OAAO,KAAK,KAAK,GAAG,CAAC;AAChC,kBAAM,IAAI,GAAG;AACb,qBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,yBAAW,KAAK,iBAAiB,GAAG,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAAA,YAC/D;AAAA,UACF,OAAO;AACL,YAAAA,QAAO,KAAK,qBAAqB,KAAK,GAAG,GAAG,KAAK,KAAK;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AACA,aAAO,EAAC,SAAkB,KAAKA,KAAG;AAAA,IACpC;AAEA,YAAQ,UAAU,mBAAmB,SAAS,UAAUA,MAAI;AAC1D,MAAAA,OAAM,KAAK,QAAQ,wBAAwB,UAAU,KAAKA,IAAG;AAC7D,MAAAA,OAAM,KAAK,qBAAqBA,IAAG;AACnC,UAAI,KAAK,QAAQ,6BAA6BA,SAAQ,QAAQ;AAC5D,eAAO,MAAM;AAAA,MACf,MAAO,QAAO,MAAM,WAAW,OAAOA,OAAM;AAAA,IAC9C;AAEA,aAAS,qBAAsB,QAAQ,KAAK,OAAO;AACjD,YAAM,SAAS,KAAK,IAAI,QAAQ,QAAQ,CAAC;AACzC,UAAI,OAAO,KAAK,QAAQ,YAAY,MAAM,UAAa,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AACvF,eAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ,YAAY,GAAG,KAAK,OAAO,SAAS,KAAK;AAAA,MAC5F,OAAO;AACL,eAAO,KAAK,gBAAgB,OAAO,KAAK,KAAK,OAAO,SAAS,KAAK;AAAA,MACpE;AAAA,IACF;AAEA,YAAQ,UAAU,kBAAkB,SAASA,MAAK,KAAK,SAAS,OAAO;AACrE,UAAGA,SAAQ,IAAG;AACZ,YAAG,IAAI,CAAC,MAAM,IAAK,QAAQ,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,UAAS,MAAM,KAAK;AAAA,aAC9E;AACH,iBAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,UAAU,KAAK,SAAS,GAAG,IAAI,KAAK;AAAA,QACjF;AAAA,MACF,OAAK;AAEH,YAAI,YAAY,OAAO,MAAM,KAAK;AAClC,YAAI,gBAAgB;AAEpB,YAAG,IAAI,CAAC,MAAM,KAAK;AACjB,0BAAgB;AAChB,sBAAY;AAAA,QACd;AAGA,aAAK,WAAW,YAAY,OAAOA,KAAI,QAAQ,GAAG,MAAM,IAAI;AAC1D,iBAAS,KAAK,UAAU,KAAK,IAAI,MAAO,MAAM,UAAU,gBAAgB,MAAMA,OAAM;AAAA,QACtF,WAAW,KAAK,QAAQ,oBAAoB,SAAS,QAAQ,KAAK,QAAQ,mBAAmB,cAAc,WAAW,GAAG;AACvH,iBAAO,KAAK,UAAU,KAAK,IAAI,OAAOA,IAAG,QAAQ,KAAK;AAAA,QACxD,OAAM;AACJ,iBACE,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,UAAU,gBAAgB,KAAK,aACnEA,OACA,KAAK,UAAU,KAAK,IAAI;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,UAAU,WAAW,SAAS,KAAI;AACxC,UAAI,WAAW;AACf,UAAG,KAAK,QAAQ,aAAa,QAAQ,GAAG,MAAM,IAAG;AAC/C,YAAG,CAAC,KAAK,QAAQ,qBAAsB,YAAW;AAAA,MACpD,WAAS,KAAK,QAAQ,mBAAkB;AACtC,mBAAW;AAAA,MACb,OAAK;AACH,mBAAW,MAAM,GAAG;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAcA,YAAQ,UAAU,mBAAmB,SAASA,MAAK,KAAK,SAAS,OAAO;AACtE,UAAI,KAAK,QAAQ,kBAAkB,SAAS,QAAQ,KAAK,QAAQ,eAAe;AAC9E,eAAO,KAAK,UAAU,KAAK,IAAI,YAAYA,IAAG,QAAS,KAAK;AAAA,MAC9D,WAAU,KAAK,QAAQ,oBAAoB,SAAS,QAAQ,KAAK,QAAQ,iBAAiB;AACxF,eAAO,KAAK,UAAU,KAAK,IAAI,OAAOA,IAAG,QAAS,KAAK;AAAA,MACzD,WAAS,IAAI,CAAC,MAAM,KAAK;AACvB,eAAQ,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,UAAS,MAAM,KAAK;AAAA,MAClE,OAAK;AACH,YAAI,YAAY,KAAK,QAAQ,kBAAkB,KAAKA,IAAG;AACvD,oBAAY,KAAK,qBAAqB,SAAS;AAE/C,YAAI,cAAc,IAAG;AACnB,iBAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,UAAU,KAAK,SAAS,GAAG,IAAI,KAAK;AAAA,QACjF,OAAK;AACH,iBAAO,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,UAAU,MAClD,YACD,OAAO,MAAM,KAAK;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,UAAU,uBAAuB,SAAS,WAAU;AAC1D,UAAG,aAAa,UAAU,SAAS,KAAK,KAAK,QAAQ,iBAAgB;AACnE,iBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,SAAS,QAAQ,KAAK;AACjD,gBAAM,SAAS,KAAK,QAAQ,SAAS,CAAC;AACtC,sBAAY,UAAU,QAAQ,OAAO,OAAO,OAAO,GAAG;AAAA,QACxD;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,OAAO;AACxB,aAAO,KAAK,QAAQ,SAAS,OAAO,KAAK;AAAA,IAC3C;AAEA,aAAS,YAAY,MAAoB;AACvC,UAAI,KAAK,WAAW,KAAK,QAAQ,mBAAmB,KAAK,SAAS,KAAK,QAAQ,cAAc;AAC3F,eAAO,KAAK,OAAO,KAAK,aAAa;AAAA,MACvC,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxRjB;AAAA;AAAA;AAEA,QAAM,YAAY;AAClB,QAAMC,aAAY;AAClB,QAAM,aAAa;AAEnB,WAAO,UAAU;AAAA,MACf,WAAWA;AAAA,MACX,cAAc;AAAA,MACd;AAAA,IACF;AAAA;AAAA;;;ACVA,IACA,wBAEa,cAoCA;AAvCb;AAAA;AAAA,IAAAC;AACA,6BAA0B;AAC1B;AACO,IAAM,eAAe,CAAC,YAAY,YAAY,kBAAkB,YAAY,OAAO,EAAE,KAAK,CAAC,YAAY;AAC1G,UAAI,QAAQ,QAAQ;AAChB,cAAM,SAAS,IAAI,iCAAU;AAAA,UACzB,qBAAqB;AAAA,UACrB,cAAc;AAAA,UACd,kBAAkB;AAAA,UAClB,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,mBAAmB,CAAC,GAAGC,SAASA,KAAI,KAAK,MAAM,MAAMA,KAAI,SAAS,IAAI,IAAI,KAAK;AAAA,QACnF,CAAC;AACD,eAAO,UAAU,OAAO,IAAI;AAC5B,eAAO,UAAU,OAAO,IAAI;AAC5B,YAAI;AACJ,YAAI;AACA,sBAAY,OAAO,MAAM,SAAS,IAAI;AAAA,QAC1C,SACO,GAAG;AACN,cAAI,KAAK,OAAO,MAAM,UAAU;AAC5B,mBAAO,eAAe,GAAG,qBAAqB;AAAA,cAC1C,OAAO;AAAA,YACX,CAAC;AAAA,UACL;AACA,gBAAM;AAAA,QACV;AACA,cAAM,eAAe;AACrB,cAAM,MAAM,OAAO,KAAK,SAAS,EAAE,CAAC;AACpC,cAAM,oBAAoB,UAAU,GAAG;AACvC,YAAI,kBAAkB,YAAY,GAAG;AACjC,4BAAkB,GAAG,IAAI,kBAAkB,YAAY;AACvD,iBAAO,kBAAkB,YAAY;AAAA,QACzC;AACA,eAAO,qBAAqB,iBAAiB;AAAA,MACjD;AACA,aAAO,CAAC;AAAA,IACZ,CAAC;AACM,IAAM,oBAAoB,OAAO,WAAW,YAAY;AAC3D,YAAM,QAAQ,MAAM,aAAa,WAAW,OAAO;AACnD,UAAI,MAAM,OAAO;AACb,cAAM,MAAM,UAAU,MAAM,MAAM,WAAW,MAAM,MAAM;AAAA,MAC7D;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC7CA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACHA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACAA,eAAsB,cAAc,SAAS,QAAQ,MAAM;AAF3D;AAGI,QAAM,UAAU,KAAK;AACrB,QAAI,wCAAS,YAAT,mBAAmB,wBAAuB,eAAe;AACzD,IAAAC,YAAW,SAAS,wBAAwB,GAAG;AAAA,EACnD;AACA,MAAI,OAAO,OAAO,kBAAkB,YAAY;AAC5C,UAAM,gBAAgB,MAAM,OAAO,cAAc;AACjD,QAAI,OAAO,cAAc,6BAA6B,YAAY;AAC9D,WAAI,yBAAc,gBAAd,mBAA2B,SAA3B,mBAAiC,SAAS,aAAa;AACvD,QAAAA,YAAW,SAAS,uBAAuB,GAAG;AAAA,MAClD,OACK;AACD,QAAAA,YAAW,SAAS,uBAAuB,GAAG;AAAA,MAClD;AAAA,IACJ,OACK;AACD,MAAAA,YAAW,SAAS,qBAAqB,GAAG;AAAA,IAChD;AAAA,EACJ;AACA,MAAI,OAAO,OAAO,0BAA0B,YAAY;AACpD,UAAM,aAAa,QAAQ;AAC3B,QAAI,QAAO,8CAAY,QAAZ,mBAAiB,QAAQ,EAAE,MAAM,yBAAyB,GAAG;AACpE,MAAAA,YAAW,SAAS,uBAAuB,GAAG;AAAA,IAClD;AACA,YAAQ,QAAM,YAAO,0BAAP,kCAAkC;AAAA,MAC5C,KAAK;AACD,QAAAA,YAAW,SAAS,4BAA4B,GAAG;AACnD;AAAA,MACJ,KAAK;AACD,QAAAA,YAAW,SAAS,6BAA6B,GAAG;AACpD;AAAA,MACJ,KAAK;AACD,QAAAA,YAAW,SAAS,4BAA4B,GAAG;AACnD;AAAA,IACR;AAAA,EACJ;AACA,QAAM,YAAW,mBAAQ,qBAAR,mBAA0B,2BAA1B,mBAAkD;AACnE,MAAI,qCAAU,SAAS;AACnB,UAAM,cAAc;AACpB,QAAI,YAAY,WAAW;AACvB,MAAAA,YAAW,SAAS,uBAAuB,GAAG;AAAA,IAClD;AACA,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,YAAY,WAAW,CAAC,CAAC,GAAG;AAClE,MAAAA,YAAW,SAAS,KAAK,KAAK;AAAA,IAClC;AAAA,EACJ;AACJ;AAhDA,IACM;AADN;AAAA;AAAA,IAAAC;AACA,IAAM,4BAA4B;AAAA;AAAA;;;ACDlC,IAAa,YACA,kBACA,OACA,mBACA,sBACA,uBACA;AANb,IAAAC,kBAAA;AAAA;AAAO,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,QAAQ;AACd,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAC7B,IAAM,wBAAwB;AAC9B,IAAM,iBAAiB;AAAA;AAAA;;;ACLvB,SAAS,eAAe,UAAU;AACrC,MAAI,SAAS;AACb,aAAW,OAAO,UAAU;AACxB,UAAMC,OAAM,SAAS,GAAG;AACxB,QAAI,OAAO,SAASA,KAAI,SAAS,KAAK,YAAY;AAC9C,UAAI,OAAO,QAAQ;AACf,kBAAU,MAAMA;AAAA,MACpB,OACK;AACD,kBAAUA;AAAA,MACd;AACA;AAAA,IACJ;AACA;AAAA,EACJ;AACA,SAAO;AACX;AAjBA,IAAM;AAAN;AAAA;AAAA,IAAM,aAAa;AAAA;AAAA;;;ACAnB,IAKa,qBAwCP,iBAyBO,+BAOA;AA7Eb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACA,IAAAC;AACA;AACO,IAAM,sBAAsB,CAAC,YAAY,CAAC,MAAM,YAAY,OAAO,SAAS;AALnF;AAMI,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,CAAC,YAAY,WAAW,OAAO,GAAG;AAClC,eAAO,KAAK,IAAI;AAAA,MACpB;AACA,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,cAAY,wCAAS,cAAT,mBAAoB,IAAI,qBAAoB,CAAC;AAC/D,YAAM,oBAAoB,MAAM,QAAQ,yBAAyB,GAAG,IAAI,eAAe;AACvF,YAAM,cAAc,SAAS,SAAS,IAAI;AAC1C,YAAM,aAAa;AACnB,uBAAiB,KAAK,KAAK,eAAe,OAAO,OAAO,CAAC,IAAG,aAAQ,qBAAR,mBAA0B,WAAU,gBAAW,sBAAX,mBAA8B,QAAQ,CAAC,CAAC,EAAE;AAC1I,YAAM,oBAAkB,wCAAS,oBAAT,mBAA0B,IAAI,qBAAoB,CAAC;AAC3E,YAAM,QAAQ,MAAM,QAAQ,eAAe;AAC3C,UAAI,OAAO;AACP,yBAAiB,KAAK,gBAAgB,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC;AAAA,MAC3D;AACA,YAAM,SAAS,mBAAmB;AAClC,YAAM,qBAAqB,SAAS,CAAC,MAAM,IAAI,CAAC,GAC3C,OAAO,CAAC,GAAG,kBAAkB,GAAG,WAAW,GAAG,eAAe,CAAC,EAC9D,KAAK,KAAK;AACf,YAAM,gBAAgB;AAAA,QAClB,GAAG,iBAAiB,OAAO,CAAC,YAAY,QAAQ,WAAW,UAAU,CAAC;AAAA,QACtE,GAAG;AAAA,MACP,EAAE,KAAK,KAAK;AACZ,UAAI,QAAQ,YAAY,WAAW;AAC/B,YAAI,eAAe;AACf,kBAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,IAC9C,GAAG,QAAQ,UAAU,CAAC,IAAI,aAAa,KACvC;AAAA,QACV;AACA,gBAAQ,UAAU,IAAI;AAAA,MAC1B,OACK;AACD,gBAAQ,gBAAgB,IAAI;AAAA,MAChC;AACA,aAAO,KAAK;AAAA,QACR,GAAG;AAAA,QACH;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAM,kBAAkB,CAAC,kBAAkB;AA7C3C;AA8CI,YAAM,OAAO,cAAc,CAAC,EACvB,MAAM,iBAAiB,EACvB,IAAI,CAAC,SAAS,KAAK,QAAQ,sBAAsB,cAAc,CAAC,EAChE,KAAK,iBAAiB;AAC3B,YAAM,WAAU,mBAAc,CAAC,MAAf,mBAAkB,QAAQ,uBAAuB;AACjE,YAAM,uBAAuB,KAAK,QAAQ,iBAAiB;AAC3D,YAAM,SAAS,KAAK,UAAU,GAAG,oBAAoB;AACrD,UAAI,SAAS,KAAK,UAAU,uBAAuB,CAAC;AACpD,UAAI,WAAW,OAAO;AAClB,iBAAS,OAAO,YAAY;AAAA,MAChC;AACA,aAAO,CAAC,QAAQ,QAAQ,OAAO,EAC1B,OAAO,CAAC,SAAS,QAAQ,KAAK,SAAS,CAAC,EACxC,OAAO,CAAC,KAAK,MAAM,UAAU;AAC9B,gBAAQ,OAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO,GAAG,GAAG,IAAI,IAAI;AAAA,UACzB;AACI,mBAAO,GAAG,GAAG,IAAI,IAAI;AAAA,QAC7B;AAAA,MACJ,GAAG,EAAE;AAAA,IACT;AACO,IAAM,gCAAgC;AAAA,MACzC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,MAAM,CAAC,kBAAkB,YAAY;AAAA,MACrC,UAAU;AAAA,IACd;AACO,IAAM,qBAAqB,CAAC,YAAY;AAAA,MAC3C,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,IAAI,oBAAoB,MAAM,GAAG,6BAA6B;AAAA,MAC9E;AAAA,IACJ;AAAA;AAAA;;;ACjFA,IAAAC,iBAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAAa;AAAb;AAAA;AAAO,IAAM,eAAe,CAAC,WAAW,OAAO,WAAW,aAAa,OAAO,WAAW,OAAO,KAAK,OAAO,SAAS,OAAO;AAAA;AAAA;;;ACA5H,IACa;AADb;AAAA;AAAA;AACO,IAAM,gBAAgB,CAAC,WAAW,aAAa,MAAM,IACtD,CAAC,mBAAmB,UAAU,EAAE,SAAS,MAAM,IAC3C,cACA,OAAO,QAAQ,4BAA4B,EAAE,IACjD;AAAA;AAAA;;;ACLN,IAEa;AAFb;AAAA;AAAA;AACA;AACO,IAAM,sBAAsB,CAAC,UAAU;AAC1C,YAAM,EAAE,QAAQ,gBAAgB,IAAI;AACpC,UAAI,CAAC,QAAQ;AACT,cAAM,IAAI,MAAM,mBAAmB;AAAA,MACvC;AACA,aAAO,OAAO,OAAO,OAAO;AAAA,QACxB,QAAQ,YAAY;AAChB,cAAI,OAAO,WAAW,UAAU;AAC5B,mBAAO,cAAc,MAAM;AAAA,UAC/B;AACA,gBAAM,iBAAiB,MAAM,OAAO;AACpC,iBAAO,cAAc,cAAc;AAAA,QACvC;AAAA,QACA,iBAAiB,YAAY;AACzB,gBAAM,iBAAiB,OAAO,WAAW,WAAW,SAAS,MAAM,OAAO;AAC1E,cAAI,aAAa,cAAc,GAAG;AAC9B,mBAAO;AAAA,UACX;AACA,iBAAO,OAAO,oBAAoB,aAAa,QAAQ,QAAQ,CAAC,CAAC,eAAe,IAAI,gBAAgB;AAAA,QACxG;AAAA,MACJ,CAAC;AAAA,IACL;AAAA;AAAA;;;ACvBA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA,IAAW;AAAX,IAAAC,cAAA;AAAA;AACA,KAAC,SAAUC,eAAc;AACrB,MAAAA,cAAa,KAAK,IAAI;AACtB,MAAAA,cAAa,QAAQ,IAAI;AAAA,IAC7B,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAAA;AAAA;;;ACJtC,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA,IAAAC;AAAA;AAAA;;;ACFA,IAGa;AAHb;AAAA;AAAA,IAAAC;AAGO,IAAM,iCAAiC;AAAA;AAAA;;;ACH9C,IAGa;AAHb;AAAA;AAAA,IAAAC;AAGO,IAAM,4BAA4B;AAAA;AAAA;;;ACHzC;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACHA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;ACHA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACFA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;;ACAO,SAAS,wBAAwB,mBAAmB;AACvD,SAAO,CAAC,SAAS,OAAO,SAAS;AAC7B,UAAM,UAAU,KAAK;AACrB,QAAI,YAAY,WAAW,OAAO,GAAG;AACjC,YAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAI,QACA,OAAO,KAAK,OAAO,EACd,IAAI,CAAC,QAAQ,IAAI,YAAY,CAAC,EAC9B,QAAQ,qBAAqB,MAAM,IAAI;AAC5C,YAAI;AACA,gBAAM,SAAS,kBAAkB,IAAI;AACrC,kBAAQ,UAAU;AAAA,YACd,GAAG,QAAQ;AAAA,YACX,CAAC,qBAAqB,GAAG,OAAO,MAAM;AAAA,UAC1C;AAAA,QACJ,SACO,OAAO;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK;AAAA,MACR,GAAG;AAAA,MACH;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AA3BA,IACM,uBA2BO,gCAMA;AAlCb,IAAAC,iBAAA;AAAA;AAAA,IAAAA;AACA,IAAM,wBAAwB;AA2BvB,IAAM,iCAAiC;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB,gBAAgB;AAAA,MAC7C,MAAM;AAAA,MACN,UAAU;AAAA,IACd;AACO,IAAM,yBAAyB,CAAC,aAAa;AAAA,MAChD,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,IAAI,wBAAwB,QAAQ,iBAAiB,GAAG,8BAA8B;AAAA,MACtG;AAAA,IACJ;AAAA;AAAA;;;ACtCA,IAAa,oBAsBP,gBACA,oBACA,cAGO,2BACA;AA5Bb;AAAA;AAAO,IAAM,qBAAqB,OAAO,mBAAmB;AACxD,YAAM,UAAS,iDAAgB,WAAU;AACzC,UAAI,OAAO,eAAe,WAAW,UAAU;AAC3C,uBAAe,SAAS,OAAO,QAAQ,MAAM,mBAAmB,GAAG,CAAC,EAAE,QAAQ,OAAO,mBAAmB,GAAG,CAAC;AAAA,MAChH;AACA,UAAI,gBAAgB,MAAM,GAAG;AACzB,YAAI,eAAe,mBAAmB,MAAM;AACxC,gBAAM,IAAI,MAAM,uDAAuD;AAAA,QAC3E;AAAA,MACJ,WACS,CAAC,0BAA0B,MAAM,KACrC,OAAO,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,eAAe,QAAQ,EAAE,WAAW,OAAO,KAClF,OAAO,YAAY,MAAM,UACzB,OAAO,SAAS,GAAG;AACnB,uBAAe,iBAAiB;AAAA,MACpC;AACA,UAAI,eAAe,gCAAgC;AAC/C,uBAAe,iCAAiC;AAChD,uBAAe,cAAc;AAAA,MACjC;AACA,aAAO;AAAA,IACX;AACA,IAAM,iBAAiB;AACvB,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AAGd,IAAM,4BAA4B,CAAC,eAAe,eAAe,KAAK,UAAU,KAAK,CAAC,mBAAmB,KAAK,UAAU,KAAK,CAAC,aAAa,KAAK,UAAU;AAC1J,IAAM,kBAAkB,CAAC,eAAe;AAC3C,YAAM,CAAC,KAAKC,YAAW,SAAS,EAAE,EAAE,MAAM,IAAI,WAAW,MAAM,GAAG;AAClE,YAAM,QAAQ,QAAQ,SAAS,WAAW,MAAM,GAAG,EAAE,UAAU;AAC/D,YAAM,aAAa,QAAQ,SAASA,cAAa,WAAW,MAAM;AAClE,UAAI,SAAS,CAAC,YAAY;AACtB,cAAM,IAAI,MAAM,gBAAgB,UAAU,sBAAsB;AAAA,MACpE;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACpCA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb;AAAA;AAAO,IAAM,4BAA4B,CAAC,WAAW,2BAA2B,WAAW;AACvF,YAAM,iBAAiB,YAAY;AAC/B,cAAM,cAAc,OAAO,SAAS,KAAK,OAAO,yBAAyB;AACzE,YAAI,OAAO,gBAAgB,YAAY;AACnC,iBAAO,YAAY;AAAA,QACvB;AACA,eAAO;AAAA,MACX;AACA,UAAI,cAAc,qBAAqB,8BAA8B,mBAAmB;AACpF,eAAO,YAAY;AACf,gBAAM,cAAc,OAAO,OAAO,gBAAgB,aAAa,MAAM,OAAO,YAAY,IAAI,OAAO;AACnG,gBAAM,eAAc,2CAAa,qBAAmB,2CAAa;AACjE,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,cAAc,eAAe,8BAA8B,aAAa;AACxE,eAAO,YAAY;AACf,gBAAM,cAAc,OAAO,OAAO,gBAAgB,aAAa,MAAM,OAAO,YAAY,IAAI,OAAO;AACnG,gBAAM,eAAc,2CAAa,eAAa,2CAAa;AAC3D,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,UAAI,cAAc,cAAc,8BAA8B,YAAY;AACtE,eAAO,YAAY;AACf,gBAAM,WAAW,MAAM,eAAe;AACtC,cAAI,YAAY,OAAO,aAAa,UAAU;AAC1C,gBAAI,SAAS,UAAU;AACnB,qBAAO,SAAS,IAAI;AAAA,YACxB;AACA,gBAAI,cAAc,UAAU;AACxB,oBAAM,EAAE,UAAU,UAAU,MAAM,KAAK,IAAI;AAC3C,qBAAO,GAAG,QAAQ,KAAK,QAAQ,GAAG,OAAO,MAAM,OAAO,EAAE,GAAG,IAAI;AAAA,YACnE;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACtCA,IAAa;AAAb;AAAA;AAAO,IAAM,wBAAwB,OAAO,cAAc;AAAA;AAAA;;;ACAnD,SAAS,iBAAiB,aAAa;AAC1C,QAAM,QAAQ,CAAC;AACf,gBAAc,YAAY,QAAQ,OAAO,EAAE;AAC3C,MAAI,aAAa;AACb,eAAW,QAAQ,YAAY,MAAM,GAAG,GAAG;AACvC,UAAI,CAAC,KAAK,QAAQ,IAAI,IAAI,KAAK,MAAM,GAAG;AACxC,YAAM,mBAAmB,GAAG;AAC5B,UAAI,OAAO;AACP,gBAAQ,mBAAmB,KAAK;AAAA,MACpC;AACA,UAAI,EAAE,OAAO,QAAQ;AACjB,cAAM,GAAG,IAAI;AAAA,MACjB,WACS,MAAM,QAAQ,MAAM,GAAG,CAAC,GAAG;AAChC,cAAM,GAAG,EAAE,KAAK,KAAK;AAAA,MACzB,OACK;AACD,cAAM,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAtBA,IAAAC,iBAAA;AAAA;AAAA;AAAA;;;ACAA,IACa;AADb,IAAAC,iBAAA;AAAA;AAAA,IAAAA;AACO,IAAM,WAAW,CAAC,QAAQ;AAC7B,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,SAAS,IAAI,IAAI,GAAG,CAAC;AAAA,MAChC;AACA,YAAM,EAAE,UAAU,UAAU,MAAM,UAAU,OAAO,IAAI;AACvD,UAAI;AACJ,UAAI,QAAQ;AACR,gBAAQ,iBAAiB,MAAM;AAAA,MACnC;AACA,aAAO;AAAA,QACH;AAAA,QACA,MAAM,OAAO,SAAS,IAAI,IAAI;AAAA,QAC9B;AAAA,QACA,MAAM;AAAA,QACN;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACjBA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,eAAe,CAAC,aAAa;AACtC,UAAI,OAAO,aAAa,UAAU;AAC9B,YAAI,SAAS,UAAU;AACnB,iBAAO,SAAS,SAAS,GAAG;AAAA,QAChC;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,QAAQ;AAAA,IAC5B;AAAA;AAAA;;;ACTA,IAIa,6BAoBA;AAxBb;AAAA;AAAA;AACA;AACA;AACA;AACO,IAAM,8BAA8B,OAAO,cAAc,sBAAsB,cAAc,YAAY;AAC5G,UAAI,CAAC,aAAa,UAAU;AACxB,YAAI;AACJ,YAAI,aAAa,2BAA2B;AACxC,+BAAqB,MAAM,aAAa,0BAA0B;AAAA,QACtE,OACK;AACD,+BAAqB,MAAM,sBAAsB,aAAa,SAAS;AAAA,QAC3E;AACA,YAAI,oBAAoB;AACpB,uBAAa,WAAW,MAAM,QAAQ,QAAQ,aAAa,kBAAkB,CAAC;AAAA,QAClF;AAAA,MACJ;AACA,YAAM,iBAAiB,MAAM,cAAc,cAAc,sBAAsB,YAAY;AAC3F,UAAI,OAAO,aAAa,qBAAqB,YAAY;AACrD,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACzD;AACA,YAAM,WAAW,aAAa,iBAAiB,gBAAgB,OAAO;AACtE,aAAO;AAAA,IACX;AACO,IAAM,gBAAgB,OAAO,cAAc,sBAAsB,iBAAiB;AAxBzF;AAyBI,YAAM,iBAAiB,CAAC;AACxB,YAAM,iBAAe,kEAAsB,qCAAtB,kDAA8D,CAAC;AACpF,iBAAW,CAAC,MAAM,WAAW,KAAK,OAAO,QAAQ,YAAY,GAAG;AAC5D,gBAAQ,YAAY,MAAM;AAAA,UACtB,KAAK;AACD,2BAAe,IAAI,IAAI,YAAY;AACnC;AAAA,UACJ,KAAK;AACD,2BAAe,IAAI,IAAI,aAAa,YAAY,IAAI;AACpD;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,2BAAe,IAAI,IAAI,MAAM,0BAA0B,YAAY,MAAM,MAAM,YAAY,EAAE;AAC7F;AAAA,UACJ,KAAK;AACD,2BAAe,IAAI,IAAI,YAAY,IAAI,YAAY;AACnD;AAAA,UACJ;AACI,kBAAM,IAAI,MAAM,kDAAkD,KAAK,UAAU,WAAW,CAAC;AAAA,QACrG;AAAA,MACJ;AACA,UAAI,OAAO,KAAK,YAAY,EAAE,WAAW,GAAG;AACxC,eAAO,OAAO,gBAAgB,YAAY;AAAA,MAC9C;AACA,UAAI,OAAO,aAAa,SAAS,EAAE,YAAY,MAAM,MAAM;AACvD,cAAM,mBAAmB,cAAc;AAAA,MAC3C;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACrDA,IAGa;AAHb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACO,IAAM,qBAAqB,CAAC,EAAE,QAAQ,aAAc,MAAM;AAC7D,aAAO,CAAC,MAAM,YAAY,OAAO,SAAS;AAJ9C;AAKQ,YAAI,OAAO,UAAU;AACjB,qBAAW,SAAS,qBAAqB,GAAG;AAAA,QAChD;AACA,cAAM,WAAW,MAAM,4BAA4B,KAAK,OAAO;AAAA,UAC3D,mCAAmC;AAC/B,mBAAO;AAAA,UACX;AAAA,QACJ,GAAG,EAAE,GAAG,OAAO,GAAG,OAAO;AACzB,gBAAQ,aAAa;AACrB,gBAAQ,eAAc,cAAS,eAAT,mBAAqB;AAC3C,cAAM,cAAa,aAAQ,gBAAR,mBAAsB;AACzC,YAAI,YAAY;AACZ,kBAAQ,gBAAgB,IAAI,WAAW;AACvC,kBAAQ,iBAAiB,IAAI,WAAW;AACxC,gBAAM,gBAAgB,iBAAiB,OAAO;AAC9C,gBAAM,kBAAiB,oDAAe,2BAAf,mBAAuC;AAC9D,cAAI,gBAAgB;AAChB,2BAAe,oBAAoB,OAAO,OAAO,eAAe,qBAAqB,CAAC,GAAG;AAAA,cACrF,gBAAgB,WAAW;AAAA,cAC3B,eAAe,WAAW;AAAA,cAC1B,iBAAiB,WAAW;AAAA,cAC5B,aAAa,WAAW;AAAA,cACxB,kBAAkB,WAAW;AAAA,YACjC,GAAG,WAAW,UAAU;AAAA,UAC5B;AAAA,QACJ;AACA,eAAO,KAAK;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAAA,MACL;AAAA,IACJ;AAAA;AAAA;;;ACnCA,IAEa,2BAQA;AAVb;AAAA;AAAA,IAAAC;AACA;AACO,IAAM,4BAA4B;AAAA,MACrC,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB,eAAe,UAAU;AAAA,MACvD,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,MACV,cAAc,2BAA2B;AAAA,IAC7C;AACO,IAAM,oBAAoB,CAAC,QAAQ,kBAAkB;AAAA,MACxD,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,cAAc,mBAAmB;AAAA,UACzC;AAAA,UACA;AAAA,QACJ,CAAC,GAAG,yBAAyB;AAAA,MACjC;AAAA,IACJ;AAAA;AAAA;;;ACjBA,IAGa;AAHb;AAAA;AAAA,IAAAC;AACA;AACA;AACO,IAAM,wBAAwB,CAAC,UAAU;AAC5C,YAAM,MAAM,MAAM,OAAO;AACzB,YAAM,EAAE,UAAU,sBAAsB,gBAAgB,IAAI;AAC5D,YAAM,yBAAyB,YAAY,OAAO,YAAY,aAAa,MAAM,kBAAkB,QAAQ,EAAE,CAAC,IAAI;AAClH,YAAM,mBAAmB,CAAC,CAAC;AAC3B,YAAM,iBAAiB,OAAO,OAAO,OAAO;AAAA,QACxC,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,sBAAsB,kBAAkB,wBAAwB,KAAK;AAAA,QACrE,iBAAiB,kBAAkB,mBAAmB,KAAK;AAAA,MAC/D,CAAC;AACD,UAAI,4BAA4B;AAChC,qBAAe,4BAA4B,YAAY;AACnD,YAAI,MAAM,aAAa,CAAC,2BAA2B;AAC/C,sCAA4B,sBAAsB,MAAM,SAAS;AAAA,QACrE;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACvBA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAAAC,cAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,IAAAC;AAAA;AAAA;;;ACJA,IAAW,aAKE,sBACA;AANb,IAAAC,eAAA;AAAA;AACA,KAAC,SAAUC,cAAa;AACpB,MAAAA,aAAY,UAAU,IAAI;AAC1B,MAAAA,aAAY,UAAU,IAAI;AAAA,IAC9B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAC7B,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB,YAAY;AAAA;AAAA;;;ACN9C,IAQa,wBAgBA,uBACA,8BACA;AA1Bb,IAAAC,kBAAA;AAAA;AAQO,IAAM,yBAAyB;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACO,IAAM,wBAAwB,CAAC,gBAAgB,kBAAkB,yBAAyB;AAC1F,IAAM,+BAA+B,CAAC,KAAK,KAAK,KAAK,GAAG;AACxD,IAAM,6BAA6B,CAAC,cAAc,gBAAgB,SAAS,WAAW;AAAA;AAAA;;;AC1B7F,IAGa,2BACA,uBAcA,mBAGA,kBAMA;AA3Bb,IAAAC,iBAAA;AAAA;AAAA,IAAAC;AAGO,IAAM,4BAA4B,CAAC,UAAO;AAHjD;AAGoD,yBAAM,cAAN,mBAAiB;AAAA;AAC9D,IAAM,wBAAwB,CAAC,UAAU;AAC5C,YAAM,gBAAgB,oBAAI,IAAI;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,SAAS,iBAAiB;AAC1C,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAO,cAAc,IAAI,MAAM,OAAO;AAAA,IAC1C;AACO,IAAM,oBAAoB,CAAC,UAAO;AAlBzC;AAkB4C,0BAAM,cAAN,mBAAiB,oBAAmB,OAC5E,uBAAuB,SAAS,MAAM,IAAI,OAC1C,WAAM,eAAN,mBAAkB,eAAc;AAAA;AAC7B,IAAM,mBAAmB,CAAC,OAAO,QAAQ,MAAG;AArBnD;AAqBsD,uCAA0B,KAAK,KACjF,sBAAsB,SAAS,MAAM,IAAI,KACzC,2BAA2B,UAAS,+BAAO,SAAQ,EAAE,KACrD,6BAA6B,WAAS,WAAM,cAAN,mBAAiB,mBAAkB,CAAC,KAC1E,sBAAsB,KAAK,KAC1B,MAAM,UAAU,UAAa,SAAS,MAAM,iBAAiB,MAAM,OAAO,QAAQ,CAAC;AAAA;AACjF,IAAM,gBAAgB,CAAC,UAAU;AA3BxC;AA4BI,YAAI,WAAM,cAAN,mBAAiB,oBAAmB,QAAW;AAC/C,cAAM,aAAa,MAAM,UAAU;AACnC,YAAI,OAAO,cAAc,cAAc,OAAO,CAAC,iBAAiB,KAAK,GAAG;AACpE,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACpCA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,qBAAN,MAAM,oBAAmB;AAAA,MAC5B,YAAY,SAAS;AACjB,aAAK,kBAAkB;AACvB,aAAK,UAAU;AACf,aAAK,cAAc;AACnB,aAAK,iBAAiB;AACtB,aAAK,eAAe;AACpB,aAAK,gBAAgB;AACrB,aAAK,aAAa;AAClB,aAAK,QAAO,mCAAS,SAAQ;AAC7B,aAAK,eAAc,mCAAS,gBAAe;AAC3C,aAAK,eAAc,mCAAS,gBAAe;AAC3C,aAAK,iBAAgB,mCAAS,kBAAiB;AAC/C,aAAK,UAAS,mCAAS,WAAU;AACjC,cAAM,uBAAuB,KAAK,wBAAwB;AAC1D,aAAK,mBAAmB;AACxB,aAAK,mBAAmB,KAAK,MAAM,KAAK,wBAAwB,CAAC;AACjE,aAAK,WAAW,KAAK;AACrB,aAAK,cAAc,KAAK;AAAA,MAC5B;AAAA,MACA,0BAA0B;AACtB,eAAO,KAAK,IAAI,IAAI;AAAA,MACxB;AAAA,MACA,MAAM,eAAe;AACjB,eAAO,KAAK,mBAAmB,CAAC;AAAA,MACpC;AAAA,MACA,MAAM,mBAAmB,QAAQ;AAC7B,YAAI,CAAC,KAAK,SAAS;AACf;AAAA,QACJ;AACA,aAAK,kBAAkB;AACvB,YAAI,SAAS,KAAK,iBAAiB;AAC/B,gBAAM,SAAU,SAAS,KAAK,mBAAmB,KAAK,WAAY;AAClE,gBAAM,IAAI,QAAQ,CAAC,YAAY,oBAAmB,aAAa,SAAS,KAAK,CAAC;AAAA,QAClF;AACA,aAAK,kBAAkB,KAAK,kBAAkB;AAAA,MAClD;AAAA,MACA,oBAAoB;AAChB,cAAM,YAAY,KAAK,wBAAwB;AAC/C,YAAI,CAAC,KAAK,eAAe;AACrB,eAAK,gBAAgB;AACrB;AAAA,QACJ;AACA,cAAM,cAAc,YAAY,KAAK,iBAAiB,KAAK;AAC3D,aAAK,kBAAkB,KAAK,IAAI,KAAK,aAAa,KAAK,kBAAkB,UAAU;AACnF,aAAK,gBAAgB;AAAA,MACzB;AAAA,MACA,wBAAwB,UAAU;AAC9B,YAAI;AACJ,aAAK,mBAAmB;AACxB,YAAI,kBAAkB,QAAQ,GAAG;AAC7B,gBAAM,YAAY,CAAC,KAAK,UAAU,KAAK,iBAAiB,KAAK,IAAI,KAAK,gBAAgB,KAAK,QAAQ;AACnG,eAAK,cAAc;AACnB,eAAK,oBAAoB;AACzB,eAAK,mBAAmB,KAAK,wBAAwB;AACrD,2BAAiB,KAAK,cAAc,SAAS;AAC7C,eAAK,kBAAkB;AAAA,QAC3B,OACK;AACD,eAAK,oBAAoB;AACzB,2BAAiB,KAAK,aAAa,KAAK,wBAAwB,CAAC;AAAA,QACrE;AACA,cAAM,UAAU,KAAK,IAAI,gBAAgB,IAAI,KAAK,cAAc;AAChE,aAAK,sBAAsB,OAAO;AAAA,MACtC;AAAA,MACA,sBAAsB;AAClB,aAAK,aAAa,KAAK,WAAW,KAAK,IAAK,KAAK,eAAe,IAAI,KAAK,QAAS,KAAK,eAAe,IAAI,CAAC,CAAC;AAAA,MAChH;AAAA,MACA,cAAc,WAAW;AACrB,eAAO,KAAK,WAAW,YAAY,KAAK,IAAI;AAAA,MAChD;AAAA,MACA,aAAa,WAAW;AACpB,eAAO,KAAK,WAAW,KAAK,gBAAgB,KAAK,IAAI,YAAY,KAAK,mBAAmB,KAAK,YAAY,CAAC,IAAI,KAAK,WAAW;AAAA,MACnI;AAAA,MACA,oBAAoB;AAChB,aAAK,UAAU;AAAA,MACnB;AAAA,MACA,sBAAsB,SAAS;AAC3B,aAAK,kBAAkB;AACvB,aAAK,WAAW,KAAK,IAAI,SAAS,KAAK,WAAW;AAClD,aAAK,cAAc,KAAK,IAAI,SAAS,KAAK,WAAW;AACrD,aAAK,kBAAkB,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW;AAAA,MAC1E;AAAA,MACA,qBAAqB;AACjB,cAAM,IAAI,KAAK,wBAAwB;AACvC,cAAM,aAAa,KAAK,MAAM,IAAI,CAAC,IAAI;AACvC,aAAK;AACL,YAAI,aAAa,KAAK,kBAAkB;AACpC,gBAAM,cAAc,KAAK,gBAAgB,aAAa,KAAK;AAC3D,eAAK,iBAAiB,KAAK,WAAW,cAAc,KAAK,SAAS,KAAK,kBAAkB,IAAI,KAAK,OAAO;AACzG,eAAK,eAAe;AACpB,eAAK,mBAAmB;AAAA,QAC5B;AAAA,MACJ;AAAA,MACA,WAAW,KAAK;AACZ,eAAO,WAAW,IAAI,QAAQ,CAAC,CAAC;AAAA,MACpC;AAAA,IACJ;AACA,uBAAmB,eAAe;AAAA;AAAA;;;ACnGlC,IAAa,0BACA,qBACA,6BACA,sBACA,YACA,oBACA,oBACA,sBACA;AARb,IAAAC,kBAAA;AAAA;AAAO,IAAM,2BAA2B;AACjC,IAAM,sBAAsB,KAAK;AACjC,IAAM,8BAA8B;AACpC,IAAM,uBAAuB;AAC7B,IAAM,aAAa;AACnB,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB;AAC3B,IAAM,uBAAuB;AAC7B,IAAM,iBAAiB;AAAA;AAAA;;;ACR9B,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,iCAAiC,MAAM;AAChD,UAAI,YAAY;AAChB,YAAM,0BAA0B,CAAC,aAAa;AAC1C,eAAO,KAAK,MAAM,KAAK,IAAI,qBAAqB,KAAK,OAAO,IAAI,KAAK,WAAW,SAAS,CAAC;AAAA,MAC9F;AACA,YAAM,eAAe,CAAC,UAAU;AAC5B,oBAAY;AAAA,MAChB;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACbA,IACa;AADb;AAAA;AAAA,IAAAC;AACO,IAAM,0BAA0B,CAAC,EAAE,YAAY,YAAY,UAAW,MAAM;AAC/E,YAAM,gBAAgB,MAAM;AAC5B,YAAM,gBAAgB,MAAM,KAAK,IAAI,qBAAqB,UAAU;AACpE,YAAM,eAAe,MAAM;AAC3B,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACVA,IAIa;AAJb;AAAA;AAAA,IAAAC;AACA,IAAAC;AACA;AACA;AACO,IAAM,wBAAN,MAA4B;AAAA,MAC/B,YAAY,aAAa;AACrB,aAAK,cAAc;AACnB,aAAK,OAAO,YAAY;AACxB,aAAK,WAAW;AAChB,aAAK,uBAAuB,+BAA+B;AAC3D,aAAK,sBAAsB,OAAO,gBAAgB,aAAa,cAAc,YAAY;AAAA,MAC7F;AAAA,MACA,MAAM,yBAAyB,iBAAiB;AAC5C,eAAO,wBAAwB;AAAA,UAC3B,YAAY;AAAA,UACZ,YAAY;AAAA,QAChB,CAAC;AAAA,MACL;AAAA,MACA,MAAM,0BAA0B,OAAO,WAAW;AAC9C,cAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,YAAI,KAAK,YAAY,OAAO,WAAW,WAAW,GAAG;AACjD,gBAAM,YAAY,UAAU;AAC5B,eAAK,qBAAqB,aAAa,cAAc,eAAe,8BAA8B,wBAAwB;AAC1H,gBAAM,qBAAqB,KAAK,qBAAqB,wBAAwB,MAAM,cAAc,CAAC;AAClG,gBAAM,aAAa,UAAU,iBACvB,KAAK,IAAI,UAAU,eAAe,QAAQ,IAAI,KAAK,IAAI,KAAK,GAAG,kBAAkB,IACjF;AACN,gBAAM,eAAe,KAAK,gBAAgB,SAAS;AACnD,eAAK,YAAY;AACjB,iBAAO,wBAAwB;AAAA,YAC3B;AAAA,YACA,YAAY,MAAM,cAAc,IAAI;AAAA,YACpC,WAAW;AAAA,UACf,CAAC;AAAA,QACL;AACA,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AAAA,MACA,cAAc,OAAO;AACjB,aAAK,WAAW,KAAK,IAAI,sBAAsB,KAAK,YAAY,MAAM,aAAa,KAAK,mBAAmB;AAAA,MAC/G;AAAA,MACA,cAAc;AACV,eAAO,KAAK;AAAA,MAChB;AAAA,MACA,MAAM,iBAAiB;AACnB,YAAI;AACA,iBAAO,MAAM,KAAK,oBAAoB;AAAA,QAC1C,SACO,OAAO;AACV,kBAAQ,KAAK,6DAA6D,oBAAoB,EAAE;AAChG,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,YAAY,cAAc,WAAW,aAAa;AAC9C,cAAM,WAAW,aAAa,cAAc,IAAI;AAChD,eAAQ,WAAW,eACf,KAAK,YAAY,KAAK,gBAAgB,UAAU,SAAS,KACzD,KAAK,iBAAiB,UAAU,SAAS;AAAA,MACjD;AAAA,MACA,gBAAgB,WAAW;AACvB,eAAO,cAAc,cAAc,qBAAqB;AAAA,MAC5D;AAAA,MACA,iBAAiB,WAAW;AACxB,eAAO,cAAc,gBAAgB,cAAc;AAAA,MACvD;AAAA,IACJ;AAAA;AAAA;;;AChEA,IAGa;AAHb;AAAA;AAAA,IAAAC;AACA;AACA;AACO,IAAM,wBAAN,MAA4B;AAAA,MAC/B,YAAY,qBAAqB,SAAS;AACtC,aAAK,sBAAsB;AAC3B,aAAK,OAAO,YAAY;AACxB,cAAM,EAAE,YAAY,IAAI,WAAW,CAAC;AACpC,aAAK,cAAc,eAAe,IAAI,mBAAmB;AACzD,aAAK,wBAAwB,IAAI,sBAAsB,mBAAmB;AAAA,MAC9E;AAAA,MACA,MAAM,yBAAyB,iBAAiB;AAC5C,cAAM,KAAK,YAAY,aAAa;AACpC,eAAO,KAAK,sBAAsB,yBAAyB,eAAe;AAAA,MAC9E;AAAA,MACA,MAAM,0BAA0B,cAAc,WAAW;AACrD,aAAK,YAAY,wBAAwB,SAAS;AAClD,eAAO,KAAK,sBAAsB,0BAA0B,cAAc,SAAS;AAAA,MACvF;AAAA,MACA,cAAc,OAAO;AACjB,aAAK,YAAY,wBAAwB,CAAC,CAAC;AAC3C,aAAK,sBAAsB,cAAc,KAAK;AAAA,MAClD;AAAA,IACJ;AAAA;AAAA;;;ACvBA;AAAA;AAAA,IAAAC;AACA;AAAA;AAAA;;;ACDA,IAAAC,cAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACNA,IA2Ba;AA3Bb,IAAAC,uBAAA;AAAA;AAAA,IAAAC;AACA,IAAAA;AA0BO,IAAM,qBAAqB,CAAC,UAAU;AACzC,YAAM,EAAE,eAAe,WAAW,YAAY,aAAa,aAAa,IAAI;AAC5E,YAAM,cAAc,kBAAkB,gBAAgB,oBAAoB;AAC1E,aAAO,OAAO,OAAO,OAAO;AAAA,QACxB;AAAA,QACA,eAAe,YAAY;AACvB,cAAI,eAAe;AACf,mBAAO;AAAA,UACX;AACA,gBAAM,YAAY,MAAM,kBAAkB,UAAU,EAAE;AACtD,cAAI,cAAc,YAAY,UAAU;AACpC,mBAAO,IAAI,sBAAsB,WAAW;AAAA,UAChD;AACA,iBAAO,IAAI,sBAAsB,WAAW;AAAA,QAChD;AAAA,MACJ,CAAC;AAAA,IACL;AAAA;AAAA;;;ACtCe,SAAR,MAAuB;AAE5B,MAAI,CAAC,iBAAiB;AAEpB,sBAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM;AAE/G,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAAA,EACF;AAEA,SAAO,gBAAgB,KAAK;AAC9B;AAjBA,IAGI,iBACE;AAJN;AAAA;AAIA,IAAM,QAAQ,IAAI,WAAW,EAAE;AAAA;AAAA;;;ACJ/B,IAAO;AAAP;AAAA;AAAA,IAAO,gBAAQ;AAAA;AAAA;;;ACEf,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACpD;AAJA,IAMO;AANP;AAAA;AAAA;AAMA,IAAO,mBAAQ;AAAA;AAAA;;;ACMR,SAAS,gBAAgB,KAAK,SAAS,GAAG;AAG/C,SAAO,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;AACnf;AAhBA,IAMM;AANN;AAAA;AAAA;AAMA,IAAM,YAAY,CAAC;AAEnB,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,gBAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAAA,IAClD;AAAA;AAAA;;;ACVA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACCA,SAAS,MAAM,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,cAAc;AAAA,EAChC;AAEA,MAAI;AACJ,QAAM,MAAM,IAAI,WAAW,EAAE;AAE7B,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO;AAClD,MAAI,CAAC,IAAI,MAAM,KAAK;AACpB,MAAI,CAAC,IAAI,MAAM,IAAI;AACnB,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO;AACnD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAEb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,EAAE,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAgB;AACnE,MAAI,EAAE,IAAI,IAAI,aAAc;AAC5B,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,IAAI;AACpB,MAAI,EAAE,IAAI,IAAI;AACd,SAAO;AACT;AAhCA,IAkCO;AAlCP;AAAA;AAAA;AAkCA,IAAO,gBAAQ;AAAA;AAAA;;;AC/Bf,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AAEtC,QAAM,QAAQ,CAAC;AAEf,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,UAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,EAC9B;AAEA,SAAO;AACT;AAIe,SAAR,IAAqB,MAAM,SAAS,UAAU;AACnD,WAAS,aAAa,OAAO,WAAW,KAAK,QAAQ;AACnD,QAAI;AAEJ,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,cAAc,KAAK;AAAA,IAC7B;AAEA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,cAAM,SAAS;AAAA,IAC7B;AAEA,UAAM,aAAa,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY,IAAI;AACpG,YAAM,UAAU,kEAAkE;AAAA,IACpF;AAKA,QAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAC5C,UAAM,IAAI,SAAS;AACnB,UAAM,IAAI,OAAO,UAAU,MAAM;AACjC,YAAQ,SAAS,KAAK;AACtB,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAC7B,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAE7B,QAAI,KAAK;AACP,eAAS,UAAU;AAEnB,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAGA,MAAI;AACF,iBAAa,OAAO;AAAA,EACtB,SAAS,KAAK;AAAA,EAAC;AAGf,eAAa,MAAM;AACnB,eAAa,MAAMC;AACnB,SAAO;AACT;AAjEA,IAea,KACAA;AAhBb;AAAA;AAAA;AACA;AAcO,IAAM,MAAM;AACZ,IAAMA,OAAM;AAAA;AAAA;;;ACInB,SAAS,IAAI,OAAO;AAClB,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,YAAQ,IAAI,WAAW,IAAI,MAAM;AAEjC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,SAAO,qBAAqB,WAAW,aAAa,KAAK,GAAG,MAAM,SAAS,CAAC,CAAC;AAC/E;AAMA,SAAS,qBAAqB,OAAO;AACnC,QAAM,SAAS,CAAC;AAChB,QAAM,WAAW,MAAM,SAAS;AAChC,QAAM,SAAS;AAEf,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,UAAM,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK;AACrC,UAAM,MAAM,SAAS,OAAO,OAAO,MAAM,IAAI,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,GAAG,EAAE;AAChF,WAAO,KAAK,GAAG;AAAA,EACjB;AAEA,SAAO;AACT;AAMA,SAAS,gBAAgB,cAAc;AACrC,UAAQ,eAAe,OAAO,KAAK,KAAK,KAAK;AAC/C;AAMA,SAAS,WAAW,GAAG,KAAK;AAE1B,IAAE,OAAO,CAAC,KAAK,OAAQ,MAAM;AAC7B,IAAE,gBAAgB,GAAG,IAAI,CAAC,IAAI;AAC9B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACrC,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACrB;AAEA,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AAOA,SAAS,aAAa,OAAO;AAC3B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AAEA,QAAM,UAAU,MAAM,SAAS;AAC/B,QAAM,SAAS,IAAI,YAAY,gBAAgB,OAAO,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAS,IAAI;AAAA,EACjD;AAEA,SAAO;AACT;AAOA,SAAS,QAAQ,GAAG,GAAG;AACrB,QAAM,OAAO,IAAI,UAAW,IAAI;AAChC,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC5C,SAAO,OAAO,KAAK,MAAM;AAC3B;AAMA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,OAAO,MAAM,QAAQ,KAAK;AACnC;AAMA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC3E;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AAEA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C;AApNA,IAsNO;AAtNP;AAAA;AAsNA,IAAO,cAAQ;AAAA;AAAA;;;ACtNf,IAEM;AAFN;AAAA;AAAA;AACA;AACA,IAAM,KAAK,IAAI,MAAM,IAAM,WAAG;AAAA;AAAA;;;ACF9B,IAAM,YACC;AADP;AAAA;AAAA,IAAM,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACtG,IAAO,iBAAQ;AAAA,MACb;AAAA,IACF;AAAA;AAAA;;;ACCA,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,MAAI,eAAO,cAAc,CAAC,OAAO,CAAC,SAAS;AACzC,WAAO,eAAO,WAAW;AAAA,EAC3B;AAEA,YAAU,WAAW,CAAC;AACtB,QAAM,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAEpD,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAE3B,MAAI,KAAK;AACP,aAAS,UAAU;AAEnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC1B;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,IAAI;AAC7B;AA1BA,IA4BO;AA5BP;AAAA;AAAA;AACA;AACA;AA0BA,IAAO,aAAQ;AAAA;AAAA;;;AC1Bf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,IAAI,IAAI,CAAC,IAAI;AAAA,IAEtB,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,IAEjB,KAAK;AACH,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IAE7B,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,EACnB;AACF;AAEA,SAAS,KAAK,GAAG,GAAG;AAClB,SAAO,KAAK,IAAI,MAAM,KAAK;AAC7B;AAEA,SAAS,KAAK,OAAO;AACnB,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACzD,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AAErE,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE9C,YAAQ,CAAC;AAET,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEhC,YAAQ,MAAM,UAAU,MAAM,KAAK,KAAK;AAAA,EAC1C;AAEA,QAAM,KAAK,GAAI;AACf,QAAM,IAAI,MAAM,SAAS,IAAI;AAC7B,QAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B,QAAM,IAAI,IAAI,MAAM,CAAC;AAErB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,MAAM,IAAI,YAAY,EAAE;AAE9B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,CAAC,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,IACpI;AAEA,MAAE,CAAC,IAAI;AAAA,EACT;AAEA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;AACtD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI;AAExC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,IAAI,IAAI,YAAY,EAAE;AAE5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,QAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,IACf;AAEA,aAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAC5B,QAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,IAC5D;AAEA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AAEX,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAC3B,YAAM,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACN;AAEA,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,EACtB;AAEA,SAAO,CAAC,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,GAAI;AACjW;AA7FA,IA+FO;AA/FP;AAAA;AA+FA,IAAO,eAAQ;AAAA;AAAA;;;AC/Ff,IAEM;AAFN;AAAA;AAAA;AACA;AACA,IAAM,KAAK,IAAI,MAAM,IAAM,YAAI;AAAA;AAAA;;;ACF/B;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACRA,IAAa;AAAb;AAAA;AAAO,IAAM,qBAAqB,CAAC,aAAY,mCAAS,iBAAgB;AAAA;AAAA;;;ACAxE,IAAa;AAAb;AAAA;AAAO,IAAM,aAAa,CAAC,UAAU;AACjC,UAAI,iBAAiB;AACjB,eAAO;AACX,UAAI,iBAAiB;AACjB,eAAO,OAAO,OAAO,IAAI,MAAM,GAAG,KAAK;AAC3C,UAAI,OAAO,UAAU;AACjB,eAAO,IAAI,MAAM,KAAK;AAC1B,aAAO,IAAI,MAAM,6BAA6B,KAAK,EAAE;AAAA,IACzD;AAAA;AAAA;;;ACRA,IAOa,iBAyDP,mBAGA,mBAWA,mBASO,wBAOA,gBAKA;AAnGb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AACO,IAAM,kBAAkB,CAAC,YAAY,CAAC,MAAM,YAAY,OAAO,SAAS;AAP/E;AAQI,UAAI,gBAAgB,MAAM,QAAQ,cAAc;AAChD,YAAM,cAAc,MAAM,QAAQ,YAAY;AAC9C,UAAI,kBAAkB,aAAa,GAAG;AAClC,wBAAgB;AAChB,YAAI,aAAa,MAAM,cAAc,yBAAyB,QAAQ,cAAc,CAAC;AACrF,YAAI,YAAY,IAAI,MAAM;AAC1B,YAAI,WAAW;AACf,YAAI,kBAAkB;AACtB,cAAM,EAAE,QAAQ,IAAI;AACpB,cAAM,YAAY,YAAY,WAAW,OAAO;AAChD,YAAI,WAAW;AACX,kBAAQ,QAAQ,oBAAoB,IAAI,WAAG;AAAA,QAC/C;AACA,eAAO,MAAM;AACT,cAAI;AACA,gBAAI,WAAW;AACX,sBAAQ,QAAQ,cAAc,IAAI,WAAW,WAAW,CAAC,SAAS,WAAW;AAAA,YACjF;AACA,kBAAM,EAAE,UAAU,OAAO,IAAI,MAAM,KAAK,IAAI;AAC5C,0BAAc,cAAc,UAAU;AACtC,mBAAO,UAAU,WAAW,WAAW;AACvC,mBAAO,UAAU,kBAAkB;AACnC,mBAAO,EAAE,UAAU,OAAO;AAAA,UAC9B,SACO,GAAG;AACN,kBAAM,iBAAiB,kBAAkB,CAAC;AAC1C,wBAAY,WAAW,CAAC;AACxB,gBAAI,aAAa,mBAAmB,OAAO,GAAG;AAC1C,eAAC,aAAQ,kBAAkB,aAAa,UAAU,QAAQ,WAAzD,mBAAkE,KAAK;AACxE,oBAAM;AAAA,YACV;AACA,gBAAI;AACA,2BAAa,MAAM,cAAc,0BAA0B,YAAY,cAAc;AAAA,YACzF,SACO,cAAc;AACjB,kBAAI,CAAC,UAAU,WAAW;AACtB,0BAAU,YAAY,CAAC;AAAA,cAC3B;AACA,wBAAU,UAAU,WAAW,WAAW;AAC1C,wBAAU,UAAU,kBAAkB;AACtC,oBAAM;AAAA,YACV;AACA,uBAAW,WAAW,cAAc;AACpC,kBAAM,QAAQ,WAAW,cAAc;AACvC,+BAAmB;AACnB,kBAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,KAAK,CAAC;AAAA,UAC7D;AAAA,QACJ;AAAA,MACJ,OACK;AACD,wBAAgB;AAChB,YAAI,+CAAe;AACf,kBAAQ,YAAY,CAAC,GAAI,QAAQ,aAAa,CAAC,GAAI,CAAC,kBAAkB,cAAc,IAAI,CAAC;AAC7F,eAAO,cAAc,MAAM,MAAM,IAAI;AAAA,MACzC;AAAA,IACJ;AACA,IAAM,oBAAoB,CAAC,kBAAkB,OAAO,cAAc,6BAA6B,eAC3F,OAAO,cAAc,8BAA8B,eACnD,OAAO,cAAc,kBAAkB;AAC3C,IAAM,oBAAoB,CAAC,UAAU;AACjC,YAAM,YAAY;AAAA,QACd;AAAA,QACA,WAAW,kBAAkB,KAAK;AAAA,MACtC;AACA,YAAM,iBAAiB,kBAAkB,MAAM,SAAS;AACxD,UAAI,gBAAgB;AAChB,kBAAU,iBAAiB;AAAA,MAC/B;AACA,aAAO;AAAA,IACX;AACA,IAAM,oBAAoB,CAAC,UAAU;AACjC,UAAI,kBAAkB,KAAK;AACvB,eAAO;AACX,UAAI,iBAAiB,KAAK;AACtB,eAAO;AACX,UAAI,cAAc,KAAK;AACnB,eAAO;AACX,aAAO;AAAA,IACX;AACO,IAAM,yBAAyB;AAAA,MAClC,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AACO,IAAM,iBAAiB,CAAC,aAAa;AAAA,MACxC,cAAc,CAAC,gBAAgB;AAC3B,oBAAY,IAAI,gBAAgB,OAAO,GAAG,sBAAsB;AAAA,MACpE;AAAA,IACJ;AACO,IAAM,oBAAoB,CAAC,aAAa;AAC3C,UAAI,CAAC,aAAa,WAAW,QAAQ;AACjC;AACJ,YAAM,uBAAuB,OAAO,KAAK,SAAS,OAAO,EAAE,KAAK,CAAC,QAAQ,IAAI,YAAY,MAAM,aAAa;AAC5G,UAAI,CAAC;AACD;AACJ,YAAM,aAAa,SAAS,QAAQ,oBAAoB;AACxD,YAAM,oBAAoB,OAAO,UAAU;AAC3C,UAAI,CAAC,OAAO,MAAM,iBAAiB;AAC/B,eAAO,IAAI,KAAK,oBAAoB,GAAI;AAC5C,YAAM,iBAAiB,IAAI,KAAK,UAAU;AAC1C,aAAO;AAAA,IACX;AAAA;AAAA;;;AC/GA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAAAC,8BAAA;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACPA,IAAAC,8BAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACDA;AAAA;AAAA,IAAAC;AACA,IAAAA;AAAA;AAAA;;;ACDA,IAAAC,iBAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACNA,IAAa,cAEA,mBAKA;AAPb,IAAAC,kBAAA;;AAAO,IAAM,eAAoC,EAAE,MAAM,UAAS;AAE3D,IAAM,oBAAiE;MAC5E,MAAM;MACN,MAAM;;AAGD,IAAM,qBAAqB,IAAI,WAAW;MAC/C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;;;;;ACvCM,SAAS,eAAe;AAC3B,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX,WACS,OAAO,SAAS,aAAa;AAClC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AATA,IAAM;AAAN,IAAAC,iBAAA;AAAA;AAAA,IAAM,iBAAiB,CAAC;AAAA;AAAA;;;ACCxB,IAQAC;AARA;;;AACA,IAAAC;AAKA,IAAAC;AAEA,IAAAF;IAAA,WAAA;AAKE,eAAAA,QAAY,QAAmB;AAFvB,aAAA,SAAqB,IAAI,WAAW,CAAC;AAG3C,aAAK,SAAS;AACd,aAAK,MAAK;MACZ;AAEA,MAAAA,QAAA,UAAA,SAAA,SAAO,MAAgB;AACrB,YAAI,YAAY,IAAI,GAAG;AACrB;;AAGF,YAAM,SAAS,gBAAgB,IAAI;AACnC,YAAM,aAAa,IAAI,WACrB,KAAK,OAAO,aAAa,OAAO,UAAU;AAE5C,mBAAW,IAAI,KAAK,QAAQ,CAAC;AAC7B,mBAAW,IAAI,QAAQ,KAAK,OAAO,UAAU;AAC7C,aAAK,SAAS;MAChB;AAEA,MAAAA,QAAA,UAAA,SAAA,WAAA;AAAA,YAAA,QAAA;AACE,YAAI,KAAK,KAAK;AACZ,iBAAO,KAAK,IAAI,KAAK,SAAC,KAAG;AACvB,mBAAA,aAAY,EACT,OAAO,OAAO,KAAK,mBAAmB,KAAK,MAAK,MAAM,EACtD,KAAK,SAAC,MAAI;AAAK,qBAAA,IAAI,WAAW,IAAI;YAAnB,CAAoB;UAFtC,CAEuC;;AAI3C,YAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,iBAAO,QAAQ,QAAQ,kBAAkB;;AAG3C,eAAO,QAAQ,QAAO,EACnB,KAAK,WAAA;AACJ,iBAAA,aAAY,EAAG,OAAO,OAAO,OAAO,cAAc,MAAK,MAAM;QAA7D,CAA8D,EAE/D,KAAK,SAAC,MAAI;AAAK,iBAAA,QAAQ,QAAQ,IAAI,WAAW,IAAI,CAAC;QAApC,CAAqC;MACzD;AAEA,MAAAA,QAAA,UAAA,QAAA,WAAA;AAAA,YAAA,QAAA;AACE,aAAK,SAAS,IAAI,WAAW,CAAC;AAC9B,YAAI,KAAK,UAAU,KAAK,WAAW,QAAQ;AACzC,eAAK,MAAM,IAAI,QAAQ,SAAC,SAAS,QAAM;AACrC,yBAAY,EACP,OAAO,OAAO,UACf,OACA,gBAAgB,MAAK,MAAoB,GACzC,mBACA,OACA,CAAC,MAAM,CAAC,EAEP,KAAK,SAAS,MAAM;UAC3B,CAAC;AACD,eAAK,IAAI,MAAM,WAAA;UAAO,CAAC;;MAE3B;AACF,aAAAA;IAAA,EA7DA;;;;;ACYM,SAAU,kBAAkBG,SAAc;AAC9C,MACE,qBAAqBA,OAAM,KAC3B,OAAOA,QAAO,OAAO,WAAW,UAChC;AACQ,QAAA,SAAWA,QAAO,OAAM;AAEhC,WAAO,qBAAqB,MAAM;;AAGpC,SAAO;AACT;AAEM,SAAU,qBAAqBA,SAAc;AACjD,MAAI,OAAOA,YAAW,YAAY,OAAOA,QAAO,WAAW,UAAU;AAC3D,QAAAC,mBAAoBD,QAAO,OAAM;AAEzC,WAAO,OAAOC,qBAAoB;;AAGpC,SAAO;AACT;AAEM,SAAU,qBAAqB,QAAoB;AACvD,SACE,UACA,oBAAoB,MAClB,SAAA,YAAU;AAAI,WAAA,OAAO,OAAO,UAAU,MAAM;EAA9B,CAAwC;AAG5D;IAzCM;;;;AAAN,IAAM,sBAAiD;MACrD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;;;;AClBF,IAAAC,eAAA;;;;;;;ACAA,IAOAC;AAPA;;;AACA,IAAAC;AAEA,IAAAA;AACA,IAAAC;AACA;AAEA,IAAAF;IAAA,WAAA;AAGE,eAAAA,QAAY,QAAmB;AAC7B,YAAI,kBAAkB,aAAY,CAAE,GAAG;AACrC,eAAK,OAAO,IAAIA,QAAgB,MAAM;eACjC;AACL,eAAK,OAAO,IAAI,OAAS,MAAM;;MAEnC;AAEA,MAAAA,QAAA,UAAA,SAAA,SAAO,MAAkB,UAAsC;AAC7D,aAAK,KAAK,OAAO,gBAAgB,IAAI,CAAC;MACxC;AAEA,MAAAA,QAAA,UAAA,SAAA,WAAA;AACE,eAAO,KAAK,KAAK,OAAM;MACzB;AAEA,MAAAA,QAAA,UAAA,QAAA,WAAA;AACE,aAAK,KAAK,MAAK;MACjB;AACF,aAAAA;IAAA,EAtBA;;;;;ACPA,IAAAG,eAAA;;;AACA;;;;;ACDA,IAEa,qBA0CA,aAyCA,eAOA,QAgBA;AA5Gb,IAAAC,kBAAA;AAAA;AAEO,IAAM,sBAAsB;AAAA,MACjC,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,OAAO;AAAA,MACP,eAAe;AAAA,MACf,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,cAAc;AAAA,MACd,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,MAAM;AAAA,IACR;AAEO,IAAM,cAAc;AAAA,MACzB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,MACX,eAAe;AAAA,MACf,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAEO,IAAM,gBAAgB;AAAA,MAC3B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,IAAI;AAAA,IACN;AAEO,IAAM,SAAS;AAAA,MACpB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,IACR;AAEO,IAAM,aAAa;AAAA,MACxB,UAAU;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA;AAAA;;;ACnHA,IAEqB;AAFrB,IAAAC,cAAA;AAAA;AAAA,IAAAC;AAEA,IAAqB,QAArB,MAAqB,OAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOzB,OAAO,cAAc,QAAQ,IAAI;AAC/B,cAAM,QAAQ,GAAG,MAAM,MAAM;AAC7B,eAAQ,SAAS,MAAM,SAAS,KAAK,MAAM,CAAC,KAAM;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,OAAO,eAAe,QAAQ,IAAI;AAChC,cAAM,QAAQ,GAAG,MAAM,MAAM;AAC7B,eAAQ,SAAS,MAAM,SAAS,KAAK,MAAM,CAAC,KAAM;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,oBAAoB,QAAQ,IAAI,QAAQ;AAC7C,YAAI,OAAO,KAAK,EAAE,GAAG;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MAEA,OAAO,sBAAsB,SAAS;AACpC,gBAAQ,SAAS;AAAA,UACf,KAAK;AAAM,mBAAO;AAAA,UAClB,KAAK;AAAM,mBAAO;AAAA,UAClB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAU,mBAAO;AAAA,UACtB,KAAK;AAAW,mBAAO;AAAA,UACvB;AAAS,mBAAO;AAAA,QAClB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBA,OAAO,oBAAoB,SAAS;AAClC,cAAM,IAAI,QAAQ,MAAM,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,IAAI,OAAK,SAAS,GAAG,EAAE,KAAK,CAAC;AACvE,UAAE,KAAK,CAAC;AACR,YAAI,EAAE,CAAC,MAAM,GAAI,QAAO;AACxB,gBAAQ,EAAE,CAAC,GAAG;AAAA,UACZ,KAAK;AAAG,mBAAO;AAAA,UACf,KAAK;AAAG,mBAAO;AAAA,UACf,KAAK;AAAG,mBAAO;AAAA,UACf,KAAK;AAAG,mBAAO;AAAA,UACf,KAAK;AAAG,mBAAO;AAAA,UACf,KAAK;AAAI,mBAAO;AAAA,UAChB,KAAK;AAAI,mBAAO;AAAA,UAChB,KAAK;AAAI,mBAAO;AAAA,UAChB,KAAK;AAAI,mBAAO;AAAA,UAChB,KAAK;AAAI,mBAAO;AAAA,UAChB,KAAK;AAAI,mBAAO;AAAA,UAChB;AAAS,mBAAO;AAAA,QAClB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BA,OAAO,sBAAsB,SAAS;AACpC,cAAM,IAAI,QAAQ,MAAM,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,IAAI,OAAK,SAAS,GAAG,EAAE,KAAK,CAAC;AACvE,UAAE,KAAK,CAAC;AACR,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAG,QAAO;AACnC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAG,QAAO;AACnC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,KAAK,EAAG,QAAO;AACpC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAG,QAAO;AACnC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM,EAAG,QAAO;AACrC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAG,QAAO;AACnC,YAAI,EAAE,CAAC,MAAM,EAAG,QAAO;AACvB,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAG,QAAO;AACnC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAG,QAAO;AACnC,YAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,KAAK,EAAG,QAAO;AACpC,YAAI,EAAE,CAAC,MAAM,EAAG,QAAO;AACvB,YAAI,EAAE,CAAC,MAAM,EAAG,QAAO;AACvB,YAAI,EAAE,CAAC,MAAM,EAAG,QAAO;AACvB,YAAI,EAAE,CAAC,MAAM,EAAG,QAAO;AACvB,YAAI,EAAE,CAAC,MAAM,EAAG,QAAO;AACvB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,OAAO,oBAAoB,SAAS;AAClC,eAAO,QAAQ,MAAM,GAAG,EAAE;AAAA,MAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBA,OAAO,gBAAgB,UAAU,UAAU,UAAU,OAAO;AAE1D,cAAM,oBAAoB,OAAM,oBAAoB,QAAQ;AAC5D,cAAM,oBAAoB,OAAM,oBAAoB,QAAQ;AAE5D,YAAI,YAAY,KAAK,IAAI,mBAAmB,iBAAiB;AAC7D,YAAI,gBAAgB;AAEpB,cAAM,SAAS,OAAM,IAAI,CAAC,UAAU,QAAQ,GAAG,CAAC,YAAY;AAC1D,gBAAM,QAAQ,YAAY,OAAM,oBAAoB,OAAO;AAG3D,gBAAM,WAAW,UAAU,IAAI,MAAM,QAAQ,CAAC,EAAE,KAAK,IAAI;AAGzD,iBAAO,OAAM,IAAI,SAAS,MAAM,GAAG,GAAG,WAAS,IAAI,MAAM,KAAK,MAAM,MAAM,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,QAAQ;AAAA,QACzG,CAAC;AAGD,YAAI,SAAS;AACX,0BAAgB,YAAY,KAAK,IAAI,mBAAmB,iBAAiB;AAAA,QAC3E;AAGA,qBAAa;AACb,eAAO,aAAa,eAAe;AAEjC,cAAI,OAAO,CAAC,EAAE,SAAS,IAAI,OAAO,CAAC,EAAE,SAAS,GAAG;AAC/C,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,CAAC,EAAE,SAAS,MAAM,OAAO,CAAC,EAAE,SAAS,GAAG;AACjD,gBAAI,cAAc,eAAe;AAE/B,qBAAO;AAAA,YACT;AAEA,yBAAa;AAAA,UACf,WAAW,OAAO,CAAC,EAAE,SAAS,IAAI,OAAO,CAAC,EAAE,SAAS,GAAG;AACtD,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,IAAI,KAAK,UAAU;AACxB,cAAM,SAAS,CAAC;AAChB,YAAI;AACJ,YAAI,MAAM,UAAU,KAAK;AACvB,iBAAO,MAAM,UAAU,IAAI,KAAK,KAAK,QAAQ;AAAA,QAC/C;AACA,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAClC,iBAAO,KAAK,SAAS,IAAI,CAAC,CAAC,CAAC;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,KAAK,KAAK,WAAW;AAC1B,YAAI;AACJ,YAAI;AACJ,YAAI,MAAM,UAAU,MAAM;AACxB,iBAAO,MAAM,UAAU,KAAK,KAAK,KAAK,SAAS;AAAA,QACjD;AACA,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG;AACzC,gBAAM,QAAQ,IAAI,CAAC;AACnB,cAAI,UAAU,OAAO,CAAC,GAAG;AACvB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,OAAO,QAAQ,WAAW;AAC/B,cAAM,SAAS;AACf,YAAI;AACJ,YAAI;AACJ,YAAI,OAAO,QAAQ;AACjB,iBAAO,OAAO,OAAO,KAAK,GAAG,SAAS;AAAA,QACxC;AACA,aAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AAC/C,gBAAM,WAAW,UAAU,CAAC;AAC5B,cAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,kBAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,iBAAK,QAAQ,CAAC,QAAQ;AACpB,qBAAO,GAAG,IAAI,SAAS,GAAG;AAAA,YAC5B,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,OAAO,gBAAgB,aAAa;AAClC,eAAO,oBAAoB,WAAW;AAAA,MACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,OAAO,sBAAsB,cAAc;AACzC,eAAO,YAAY,YAAY,KAAK;AAAA,MACtC;AAAA,IACF;AAAA;AAAA;;;ACpTA,IA2BM,yBAEA,cA8pBC;AA3rBP;AAAA;AAyBA,IAAAC;AAEA,IAAM,0BAA0B;AAEhC,IAAM,eAAe;AAAA;AAAA,MAEnB;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,4BAA4B,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEtH,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,mCAAmC,EAAE;AAE7H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,cAAc;AAAA,QACrB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,4BAA4B,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEtH,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,iBAAiB;AAAA,QACxB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,4CAA4C,EAAE;AAEtI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,kCAAkC,EAAE;AAE5H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,sCAAsC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,kCAAkC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAE5H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,kCAAkC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAE5H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,mCAAmC,EAAE;AAE7H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,uBAAuB;AAAA,QAC9B,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,iCAAiC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAE3H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,uCAAuC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEjI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,uCAAuC,EAAE;AAEjI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,gBAAgB;AAAA,QACvB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,2CAA2C,EAAE;AAErI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,sCAAsC,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,SAAS;AAAA,QAChB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,oCAAoC,EAAE;AAE9H,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,sCAAsC,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,sCAAsC,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,iBAAiB;AAAA,QACxB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,4CAA4C,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEtI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAO,iBAAkB,KAAK,EAAE,IAAI,oBAAoB;AAAA,UAC1D;AACA,gBAAM,UAAU,MAAM,cAAc,mDAAmD,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAE7I,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,eAAe;AAAA,QACtB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,kCAAkC,EAAE;AAExE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,UAAU;AAAA,QACjB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AAEA,gBAAM,UAAU,MAAM,cAAc,4BAA4B,EAAE;AAElE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,gBAAgB;AAAA,QACvB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AAEA,gBAAM,UAAU,MAAM,eAAe,oCAAoC,EAAE;AAE3E,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,UAAU;AAAA,QACjB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,8BAA8B,EAAE;AAEpE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,gCAAgC,EAAE;AAEtE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AAEA,gBAAM,UAAU,MAAM,cAAc,sCAAsC,EAAE;AAE5E,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,OAAO;AAAA,QACd,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,2BAA2B,EAAE;AAEjE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,UAAU;AAAA,QACjB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,gCAAgC,EAAE;AAEtE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,+BAA+B,EAAE;AAErE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,uBAAuB,cAAc;AAAA,QAC5C,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,sCAAsC,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,iBAAiB;AAAA,QACxB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE,KAAK,MAAM,cAAc,2CAA2C,EAAE;AAErI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,OAAO;AAAA,QACd,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,6BAA6B,EAAE;AAEnE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,2CAA2C,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAErI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,sCAAsC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,0BAA0B;AAAA,QACjC,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,qDAAqD,EAAE;AAE3F,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,mCAAmC,EAAE;AAEzE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,cAAc;AAAA,QACrB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yCAAyC,EAAE;AAE/E,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,sCAAsC,EAAE,KAAK,MAAM,cAAc,yBAAyB,EAAE;AAEhI,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,oBAAoB;AAAA,QAC3B,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,4CAA4C,EAAE;AAElF,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA;AAAA,QACE,MAAM,CAAC,MAAM;AAAA,QACb,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,8BAA8B,EAAE;AAEpE,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,gBAAM,iBAAiB,CAAC,OAAO,KAAK,eAAe;AACnD,gBAAM,aAAa,OAAO,KAAK,UAAU;AACzC,iBAAO,kBAAkB;AAAA,QAC3B;AAAA,QACA,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE;AAE/D,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,gBAAgB;AAAA,QACvB,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE;AAE/D,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,qBAAqB;AAAA,QAC5B,SAAS,IAAI;AACX,gBAAM,UAAU;AAAA,YACd,MAAM;AAAA,UACR;AACA,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE;AAE/D,cAAI,SAAS;AACX,oBAAQ,UAAU;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,KAAK;AAAA,QACZ,SAAS,IAAI;AAKX,gBAAM,0BAA0B;AAChC,gBAAM,uBAAuB;AAC7B,gBAAM,gBAAgB,GAAG,OAAO,KAAK,MAAM;AAC3C,gBAAM,SAAS,gBAAgB,uBAAuB;AACtD,iBAAO;AAAA,YACL,MAAM,MAAM,cAAc,QAAQ,EAAE;AAAA,YACpC,SAAS,MAAM,eAAe,QAAQ,EAAE;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,IAAO,0BAAQ;AAAA;AAAA;;;AC3rBf,IAGO;AAHP;AAAA;AAAA,IAAAC;AACA,IAAAC;AAEA,IAAO,oBAAQ;AAAA;AAAA,MAEb;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,yBAAyB,EAAE;AAC/D,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,gBAAgB;AAAA,QACvB,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,0CAA0C,EAAE;AAChF,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,kCAAkC,EAAE;AACxE,gBAAM,cAAc,MAAM,sBAAsB,OAAO;AAEvD,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,6BAA6B;AAAA,QACpC,SAAS,IAAI;AACX,gBAAM,SAAS;AAAA,YACb,MAAM,OAAO;AAAA,UACf;AACA,gBAAM,UAAU,MAAM,eAAe,yBAAyB,EAAE;AAChE,cAAI,SAAS;AACX,mBAAO,UAAU;AAAA,UACnB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,8BAA8B,EAAE,EAAE,QAAQ,UAAU,GAAG;AAC3F,gBAAM,cAAc,MAAM,oBAAoB,OAAO;AAErD,gBAAM,KAAK;AAAA,YACT,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AACA,cAAI,aAAa;AACf,eAAG,cAAc;AAAA,UACnB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,qBAAqB;AAAA,QAC5B,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,sCAAsC,EAAE,EAAE,QAAQ,UAAU,GAAG;AAEnG,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,gBAAM,iBAAiB,CAAC,OAAO,KAAK,eAAe;AACnD,gBAAM,aAAa,OAAO,KAAK,UAAU;AACzC,iBAAO,kBAAkB;AAAA,QAC3B;AAAA,QACA,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,+BAA+B,EAAE;AACrE,gBAAM,cAAc,MAAM,sBAAsB,OAAO;AACvD,gBAAM,KAAK;AAAA,YACT,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AACA,cAAI,aAAa;AACf,eAAG,cAAc;AAAA,UACnB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,iBAAiB;AAAA,QACxB,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,oCAAoC,EAAE;AAC1E,gBAAM,KAAK;AAAA,YACT,MAAM,OAAO;AAAA,UACf;AAEA,cAAI,WAAW,QAAQ,QAAQ;AAC7B,eAAG,UAAU;AAAA,UACf;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,uBAAuB,cAAc;AAAA,QAC5C,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,mCAAmC,EAAE,KACpE,MAAM,cAAc,oCAAoC,EAAE,KAC1D,MAAM,cAAc,cAAc,EAAE;AAEzC,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,OAAO;AAAA,QACd,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,wBAAwB,EAAE;AAE9D,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,4BAA4B,EAAE;AAElE,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,QAAQ;AAAA,QACf,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,MAAM;AAAA,QACb,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,eAAe;AAAA,QACtB,SAAS,IAAI;AACX,gBAAM,UAAU,MAAM,cAAc,oCAAoC,EAAE;AAC1E,iBAAO;AAAA,YACL,MAAM,OAAO;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACtMA,IAQO;AARP;AAAA;AAAA,IAAAC;AACA,IAAAC;AAOA,IAAO,2BAAQ;AAAA;AAAA,MAEb;AAAA,QACE,MAAM,CAAC,YAAY;AAAA,QACnB,WAAW;AACT,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,SAAS;AAAA,QAChB,SAAS,IAAI;AACX,gBAAM,QAAQ,MAAM,cAAc,cAAc,EAAE,KAAK;AACvD,gBAAM,WAAW;AAAA,YACf,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AACA,cAAI,OAAO;AACT,qBAAS,QAAQ;AAAA,UACnB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,yBAAyB;AAAA,QAChC,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,OAAO;AAAA,QACd,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,6BAA6B;AAAA,QACpC,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,aAAa;AAAA,QACpB,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,OAAO;AAAA,QACd,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,gBAAgB;AAAA,QACvB,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,gBAAM,UAAU,OAAO,KAAK,cAAc;AAC1C,gBAAM,cAAc,OAAO,KAAK,qBAAqB;AACrD,iBAAO,WAAW,CAAC;AAAA,QACrB;AAAA,QACA,SAAS,IAAI;AACX,gBAAM,QAAQ,MAAM,cAAc,kBAAkB,EAAE;AACtD,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,oBAAoB,eAAe;AAAA,QAC1C,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,WAAW;AAAA,QAClB,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,eAAe,IAAI,MAAM;AAAA,QACzC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,eAAe,IAAI,MAAM;AAAA,QACzC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,eAAe,MAAM;AAAA,QACrC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,gBAAM,iBAAiB,OAAO,OAAO,OAAO,aAAa,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AACzE,iBAAO,OAAO,UAAU,IAAI,MAAM,aAAc,kBAAkB;AAAA,QACpE;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,UAAU,IAAI,MAAM;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,UAAU,IAAI,MAAM;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,UAAU,IAAI,MAAM;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,UAAU,IAAI,MAAM;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,UAAU,IAAI,MAAM;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,UAAU,IAAI,MAAM;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,cAAc;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACzQA,IAMO;AANP;AAAA;AAAA,IAAAC;AACA,IAAAC;AAKA,IAAO,yBAAQ;AAAA;AAAA,MAEb;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,eAAe,IAAI,MAAM;AAAA,QACzC;AAAA,QACA,SAAS,IAAI;AACX,gBAAM,eAAe,WAAW,KAAK,EAAE;AAGvC,cAAI,cAAc;AAChB,mBAAO;AAAA,cACL,MAAM,WAAW;AAAA,YACnB;AAAA,UACF;AAGA,gBAAM,UAAU,MAAM,cAAc,2BAA2B,EAAE;AAEjE,iBAAO;AAAA,YACL,MAAM,WAAW;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,UAAU;AAAA,QACjB,SAAS,IAAI;AACX,gBAAM,SAAS;AAAA,YACb,MAAM,WAAW;AAAA,UACnB;AAEA,gBAAM,UAAU,MAAM,cAAc,8BAA8B,EAAE;AAEpE,cAAI,SAAS;AACX,mBAAO,UAAU;AAAA,UACnB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,iBAAO,OAAO,KAAK,SAAS;AAAA,QAC9B;AAAA,QACA,SAAS,IAAI;AACX,gBAAM,SAAS;AAAA,YACb,MAAM,WAAW;AAAA,UACnB;AAEA,gBAAM,UAAU,MAAM,cAAc,6BAA6B,EAAE;AAEnE,cAAI,SAAS;AACX,mBAAO,UAAU;AAAA,UACnB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,KAAK,QAAQ;AACX,gBAAM,UAAU,OAAO,KAAK,QAAQ;AACpC,gBAAM,YAAY,OAAO,KAAK,aAAa;AAC3C,iBAAO,WAAW,CAAC;AAAA,QACrB;AAAA,QACA,SAAS,IAAI;AACX,gBAAM,SAAS;AAAA,YACb,MAAM,WAAW;AAAA,UACnB;AAEA,gBAAM,UAAU,MAAM,cAAc,4BAA4B,EAAE;AAElE,cAAI,SAAS;AACX,mBAAO,UAAU;AAAA,UACnB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,0BAA0B;AAAA,QACjC,WAAW;AACT,iBAAO;AAAA,YACL,MAAM,WAAW;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAGA;AAAA,QACE,MAAM,CAAC,iBAAiB;AAAA,QACxB,SAAS,IAAI;AACX,gBAAM,SAAS;AAAA,YACb,MAAM,WAAW;AAAA,UACnB;AAEA,gBAAM,UAAU,MAAM,cAAc,6BAA6B,EAAE;AAEnE,cAAI,SAAS;AACX,mBAAO,UAAU;AAAA,UACnB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACvHA,IASM,QAseC;AA/eP;AAAA;AAAA;AACA;AACA;AACA;AACA,IAAAC;AAKA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaX,YAAY,IAAI,cAAc,OAAO;AACnC,YAAI,OAAO,UAAY,OAAO,QAAQ,OAAO,IAAI;AAC/C,gBAAM,IAAI,MAAM,oCAAoC;AAAA,QACtD;AAEA,aAAK,MAAM;AAwBX,aAAK,eAAe,CAAC;AAErB,YAAI,gBAAgB,MAAM;AACxB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ;AACN,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,KAAK,OAAO;AACV,eAAO,MAAM,KAAK,KAAK,GAAG;AAAA,MAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe;AACb,aAAK,aAAa,UAAU,CAAC;AAE7B,cAAM,oBAAoB,MAAM,KAAK,yBAAoB,CAAC,aAAa;AACrE,cAAI,OAAO,SAAS,SAAS,YAAY;AACvC,mBAAO,SAAS,KAAK,IAAI;AAAA,UAC3B;AAEA,cAAI,SAAS,gBAAgB,OAAO;AAClC,mBAAO,SAAS,KAAK,KAAK,eAAa,KAAK,KAAK,SAAS,CAAC;AAAA,UAC7D;AAEA,gBAAM,IAAI,MAAM,sCAAsC;AAAA,QACxD,CAAC;AAED,YAAI,mBAAmB;AACrB,eAAK,aAAa,UAAU,kBAAkB,SAAS,KAAK,MAAM,CAAC;AAAA,QACrE;AAEA,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,aAAa;AACX,YAAI,KAAK,aAAa,SAAS;AAC7B,iBAAO,KAAK,aAAa;AAAA,QAC3B;AAEA,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,eAAe,aAAa;AAC1B,YAAI,aAAa;AACf,iBAAO,OAAO,KAAK,WAAW,EAAE,IAAI,EAAE,YAAY,KAAK;AAAA,QACzD;AACA,eAAO,KAAK,WAAW,EAAE,QAAQ;AAAA,MACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,oBAAoB;AAClB,eAAO,KAAK,WAAW,EAAE;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaA,QAAQ;AACN,YAAI,KAAK,aAAa,IAAI;AACxB,iBAAO,KAAK,aAAa;AAAA,QAC3B;AAEA,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU;AACR,aAAK,aAAa,KAAK,CAAC;AAExB,cAAM,KAAK,MAAM,KAAK,mBAAe,CAAC,QAAQ;AAC5C,cAAI,OAAO,IAAI,SAAS,YAAY;AAClC,mBAAO,IAAI,KAAK,IAAI;AAAA,UACtB;AAEA,cAAI,IAAI,gBAAgB,OAAO;AAC7B,mBAAO,IAAI,KAAK,KAAK,eAAa,KAAK,KAAK,SAAS,CAAC;AAAA,UACxD;AAEA,gBAAM,IAAI,MAAM,sCAAsC;AAAA,QACxD,CAAC;AAED,YAAI,IAAI;AACN,eAAK,aAAa,KAAK,GAAG,SAAS,KAAK,MAAM,CAAC;AAAA,QACjD;AAEA,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,aAAa;AACrB,cAAM,EAAE,KAAK,IAAI,KAAK,MAAM;AAE5B,YAAI,aAAa;AACf,iBAAO,OAAO,IAAI,EAAE,YAAY,KAAK;AAAA,QACvC;AAEA,eAAO,QAAQ;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,eAAe;AACb,eAAO,KAAK,MAAM,EAAE;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc;AACZ,YAAI,KAAK,aAAa,UAAU;AAC9B,iBAAO,KAAK,aAAa;AAAA,QAC3B;AAEA,eAAO,KAAK,cAAc;AAAA,MAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,gBAAgB,cAAc,OAAO;AACnC,cAAM,EAAE,KAAK,IAAI,KAAK,YAAY;AAElC,YAAI,aAAa;AACf,iBAAO,OAAO,IAAI,EAAE,YAAY,KAAK;AAAA,QACvC;AAEA,eAAO,QAAQ;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,gBAAgB;AACd,aAAK,aAAa,WAAW,CAAC;AAE9B,cAAM,WAAW,MAAM,KAAK,0BAAqB,CAAC,cAAc;AAC9D,cAAI,OAAO,UAAU,SAAS,YAAY;AACxC,mBAAO,UAAU,KAAK,IAAI;AAAA,UAC5B;AAEA,cAAI,UAAU,gBAAgB,OAAO;AACnC,mBAAO,UAAU,KAAK,KAAK,eAAa,KAAK,KAAK,SAAS,CAAC;AAAA,UAC9D;AAEA,gBAAM,IAAI,MAAM,sCAAsC;AAAA,QACxD,CAAC;AAED,YAAI,UAAU;AACZ,eAAK,aAAa,WAAW,SAAS,SAAS,KAAK,MAAM,CAAC;AAAA,QAC7D;AAEA,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AACV,YAAI,KAAK,aAAa,QAAQ;AAC5B,iBAAO,KAAK,aAAa;AAAA,QAC3B;AAEA,eAAO,KAAK,YAAY;AAAA,MAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,cAAc,aAAa;AACzB,YAAI,aAAa;AACf,iBAAO,OAAO,KAAK,UAAU,EAAE,IAAI,EAAE,YAAY,KAAK;AAAA,QACxD;AACA,eAAO,KAAK,UAAU,EAAE,QAAQ;AAAA,MAClC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,cAAc;AACZ,aAAK,aAAa,SAAS,CAAC;AAE5B,cAAM,SAAS,MAAM,KAAK,wBAAoB,CAAC,YAAY;AACzD,cAAI,OAAO,QAAQ,SAAS,YAAY;AACtC,mBAAO,QAAQ,KAAK,IAAI;AAAA,UAC1B;AAEA,cAAI,QAAQ,gBAAgB,OAAO;AACjC,mBAAO,QAAQ,KAAK,KAAK,eAAa,KAAK,KAAK,SAAS,CAAC;AAAA,UAC5D;AAEA,gBAAM,IAAI,MAAM,sCAAsC;AAAA,QACxD,CAAC;AAED,YAAI,QAAQ;AACV,eAAK,aAAa,SAAS,OAAO,SAAS,KAAK,MAAM,CAAC;AAAA,QACzD;AAEA,eAAO,KAAK,aAAa;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ;AACN,aAAK,aAAa;AAClB,aAAK,QAAQ;AACb,aAAK,cAAc;AACnB,aAAK,YAAY;AAEjB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AACV,eAAO,MAAM,OAAO,CAAC,GAAG,KAAK,YAAY;AAAA,MAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBA,UAAU,WAAW;AACnB,cAAM,mBAAmB,CAAC;AAC1B,YAAI,wBAAwB;AAC5B,cAAM,WAAW,CAAC;AAClB,YAAI,kBAAkB;AAEtB,cAAM,iBAAiB,OAAO,KAAK,SAAS;AAE5C,uBAAe,QAAQ,CAAC,QAAQ;AAC9B,gBAAM,oBAAoB,UAAU,GAAG;AACvC,cAAI,OAAO,sBAAsB,UAAU;AACzC,qBAAS,GAAG,IAAI;AAChB,+BAAmB;AAAA,UACrB,WAAW,OAAO,sBAAsB,UAAU;AAChD,6BAAiB,GAAG,IAAI;AACxB,qCAAyB;AAAA,UAC3B;AAAA,QACF,CAAC;AAED,YAAI,wBAAwB,GAAG;AAC7B,gBAAM,sBAAsB,OAAO,KAAK,gBAAgB;AACxD,gBAAM,uBAAuB,MAAM,KAAK,qBAAqB,UAAS,KAAK,KAAK,IAAI,CAAE;AAEtF,cAAI,sBAAsB;AACxB,kBAAM,WAAW,KAAK,UAAU,iBAAiB,oBAAoB,CAAC;AAEtE,gBAAI,aAAa,QAAQ;AACvB,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,gBAAM,6BAA6B,MAAM;AAAA,YACvC;AAAA,YACA,UAAS,KAAK,WAAW,IAAI;AAAA,UAC/B;AACA,cAAI,4BAA4B;AAC9B,kBAAM,iBAAiB,KAAK,UAAU,iBAAiB,0BAA0B,CAAC;AAElF,gBAAI,mBAAmB,QAAQ;AAC7B,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,YAAI,kBAAkB,GAAG;AACvB,gBAAM,eAAe,OAAO,KAAK,QAAQ;AACzC,gBAAM,qBAAqB,MAAM,KAAK,cAAc,UAAS,KAAK,UAAU,MAAM,IAAI,CAAE;AAExF,cAAI,uBAAuB,QAAQ;AACjC,mBAAO,KAAK,eAAe,SAAS,kBAAkB,CAAC;AAAA,UACzD;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,UAAU,aAAa,iBAAiB,OAAO;AAC7C,cAAM,qBAAqB,KAAK,eAAe,EAAE,YAAY;AAC7D,YAAI,mBAAmB,YAAY,YAAY;AAC/C,cAAM,QAAQ,MAAM,sBAAsB,gBAAgB;AAE1D,YAAI,kBAAkB,OAAO;AAC3B,6BAAmB,MAAM,YAAY;AAAA,QACvC;AACA,eAAO,qBAAqB;AAAA,MAC9B;AAAA,MAEA,eAAe,SAAS;AACtB,YAAI,kBAAkB,CAAC,CAAC;AACxB,YAAI,oBAAoB;AACxB,YAAI,UAAU;AAEd,cAAM,wBAAwB,KAAK,kBAAkB;AAErD,YAAI,OAAO,0BAA0B,UAAU;AAC7C,iBAAO;AAAA,QACT;AAEA,YAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM,KAAK;AAC5C,8BAAoB,QAAQ,OAAO,CAAC;AACpC,cAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,sBAAU;AACV,gCAAoB,QAAQ,OAAO,CAAC;AAAA,UACtC,OAAO;AACL,8BAAkB,CAAC;AAAA,UACrB;AACA,cAAI,QAAQ,CAAC,MAAM,KAAK;AACtB,4BAAgB,KAAK,CAAC;AAAA,UACxB,OAAO;AACL,4BAAgB,KAAK,EAAE;AAAA,UACzB;AAAA,QACF,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC7B,8BAAoB,QAAQ,OAAO,CAAC;AAAA,QACtC,WAAW,QAAQ,CAAC,MAAM,KAAK;AAC7B,oBAAU;AACV,8BAAoB,QAAQ,OAAO,CAAC;AAAA,QACtC;AAEA,eAAO,gBAAgB;AAAA,UACrB,MAAM,gBAAgB,uBAAuB,mBAAmB,OAAO;AAAA,QACzE,IAAI;AAAA,MACN;AAAA,MAEA,KAAK,QAAQ;AACX,eAAO,KAAK,UAAU,IAAI,MAAM,OAAO,MAAM,EAAE,YAAY;AAAA,MAC7D;AAAA,MAEA,WAAW,cAAc;AACvB,eAAO,KAAK,gBAAgB,IAAI,MAAM,OAAO,YAAY,EAAE,YAAY;AAAA,MACzE;AAAA,MAEA,SAAS,YAAY;AACnB,eAAO,KAAK,cAAc,IAAI,MAAM,OAAO,UAAU,EAAE,YAAY;AAAA,MACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,GAAG,UAAU,iBAAiB,OAAO;AACnC,eAAO,KAAK,UAAU,UAAU,cAAc,KAAK,KAAK,KAAK,QAAQ,KAChE,KAAK,WAAW,QAAQ;AAAA,MAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,KAAK,YAAY,CAAC,GAAG;AACnB,eAAO,UAAU,KAAK,cAAY,KAAK,GAAG,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF;AAEA,IAAO,iBAAQ;AAAA;AAAA;;;AC/ef,IAyBM,QAmDC;AA5EP;AAAA;AAMA;AACA,IAAAC;AAkBA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcX,OAAO,UAAU,IAAI,cAAc,OAAO;AACxC,YAAI,OAAO,OAAO,UAAU;AAC1B,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAChD;AACA,eAAO,IAAI,eAAO,IAAI,WAAW;AAAA,MACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,OAAO,MAAM,IAAI;AACf,eAAQ,IAAI,eAAO,EAAE,EAAG,UAAU;AAAA,MACpC;AAAA,MAEA,WAAW,cAAc;AACvB,eAAO;AAAA,MACT;AAAA,MAEA,WAAW,aAAa;AACtB,eAAO;AAAA,MACT;AAAA,MAEA,WAAW,SAAS;AAClB,eAAO;AAAA,MACT;AAAA,MAEA,WAAW,gBAAgB;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,IAAO,iBAAQ;AAAA;AAAA;;;AC5Ef,IACa;AADb,IAAAC,iBAAA;AAAA;AAAA;AACO,IAAM,iCAAiC,CAAC,EAAE,WAAW,cAAc,MAAM,OAAO,WAAW;AADlG;AAEI,YAAM,WAAW,OAAO,WAAW,iBAAe,sCAAQ,cAAR,mBAAmB,aAC/D,eAAO,MAAM,OAAO,UAAU,SAAS,IACvC;AACN,YAAM,WAAW;AAAA,QACb,CAAC,cAAc,aAAa;AAAA,QAC5B,CAAC,MAAM,KAAK;AAAA,QACZ,CAAC,QAAM,0CAAU,OAAV,mBAAc,SAAQ,OAAO,KAAI,0CAAU,OAAV,mBAAc,OAAO;AAAA,QAC7D,CAAC,SAAS;AAAA,QACV,CAAC,cAAc,KAAG,0CAAU,YAAV,mBAAmB,SAAQ,SAAS,MAAI,0CAAU,YAAV,mBAAmB,YAAW,SAAS,EAAE;AAAA,MACvG;AACA,UAAI,WAAW;AACX,iBAAS,KAAK,CAAC,OAAO,SAAS,IAAI,aAAa,CAAC;AAAA,MACrD;AACA,YAAM,QAAQ,QAAM,sCAAQ,mBAAR;AACpB,UAAI,OAAO;AACP,iBAAS,KAAK,CAAC,OAAO,KAAK,EAAE,CAAC;AAAA,MAClC;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACpBA,IAAa;AAAb;AAAA;AAAO,IAAM,kBAAkB,CAAC,YAAY,MAAM,QAAQ,OAAO,OAAO;AAAA;AAAA;;;ACAxE;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA,IAAM,cACO;AADb;AAAA;AAAA,IAAM,eAAe,OAAO,eAAe,aAAa,IAAI,YAAY,IAAI;AACrE,IAAM,sBAAsB,CAAC,SAAS;AACzC,UAAI,OAAO,SAAS,UAAU;AAC1B,YAAI,cAAc;AACd,iBAAO,aAAa,OAAO,IAAI,EAAE;AAAA,QACrC;AACA,YAAI,MAAM,KAAK;AACf,iBAAS,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK;AAC/B,gBAAM,OAAO,KAAK,WAAW,CAAC;AAC9B,cAAI,OAAO,OAAQ,QAAQ;AACvB;AAAA,mBACK,OAAO,QAAS,QAAQ;AAC7B,mBAAO;AACX,cAAI,QAAQ,SAAU,QAAQ;AAC1B;AAAA,QACR;AACA,eAAO;AAAA,MACX,WACS,OAAO,KAAK,eAAe,UAAU;AAC1C,eAAO,KAAK;AAAA,MAChB,WACS,OAAO,KAAK,SAAS,UAAU;AACpC,eAAO,KAAK;AAAA,MAChB;AACA,YAAM,IAAI,MAAM,sCAAsC,IAAI,EAAE;AAAA,IAChE;AAAA;AAAA;;;ACzBA,IAAAC,iBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAa;AAAb,IAAAC,kBAAA;AAAA;AAAO,IAAM,wBAAwB,CAAC,aAAa,gBAAgB,UAAU,YAAY,QAAQ;AAAA;AAAA;;;ACAjG,IAGa,2BAiBP;AApBN;AAAA;AAAA;AACA;AACA,IAAAC;AACO,IAAM,4BAA4B,CAAC,EAAE,aAAc,IAAI,CAAC,MAAM,QAAQ,YAAY;AACrF,YAAM,OAAO,OAAO,iBAAiB,aAAa,MAAM,aAAa,IAAI;AACzE,cAAQ,6BAAM,eAAe;AAAA,QACzB,KAAK;AACD,iBAAO,QAAQ,QAAQ,gBAAgB,IAAI,WAAW,UAAU;AAAA,QACpE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,QAAQ,QAAQ,6BAAM,mBAAmB;AAAA,QACpD,KAAK;AACD,iBAAO,QAAQ,QAAQ,QAAQ;AAAA,QACnC;AACI,gBAAM,IAAI,MAAM,gDAAgD,sBAAsB,KAAK,IAAI,CAAC,SAAS,IAAI,EAAE;AAAA,MACvH;AAAA,IACJ,CAAC;AACD,IAAM,kBAAkB,MAAM;AApB9B;AAqBI,YAAM,WAAW,OAAO,WAAW,iBAAe,sCAAQ,cAAR,mBAAmB,aAC/D,eAAO,MAAM,OAAO,UAAU,SAAS,IACvC;AACN,YAAM,YAAW,0CAAU,aAAV,mBAAoB;AACrC,aAAO,aAAa,YAAY,aAAa;AAAA,IACjD;AAAA;AAAA;;;AC1BA,IAAAC,iBAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAa,oCAUA;AAVb;AAAA;AAAO,IAAM,qCAAqC,CAAC,kBAAkB;AACjE,aAAO;AAAA,QACH,UAAU,QAAQ;AACd,wBAAc,SAAS;AAAA,QAC3B;AAAA,QACA,SAAS;AACL,iBAAO,cAAc;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACO,IAAM,yCAAyC,CAAC,oCAAoC;AACvF,aAAO;AAAA,QACH,QAAQ,gCAAgC,OAAO;AAAA,MACnD;AAAA,IACJ;AAAA;AAAA;;;ACdA,IAAAC,eAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,qBAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,sBAAA;AAAA;AAAA,IAAAC;AAAA;AAAA;;;ACAA,IAAAC,4BAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACDA,IAAAC,qBAAA;AAAA;AAAA,IAAAC;AACA,IAAAC;AAAA;AAAA;;;ACDA,IAAAC,iBAAA;AAAA;AAAA;AACA,IAAAC;AAAA;AAAA;", "names": ["init_dist_es", "init_dist_es", "init_dist_es", "normalizeProvider", "init_dist_es", "val", "init_dist_es", "hostname", "protocol", "init_dist_es", "init_isIpAddress", "init_dist_es", "init_dist_es", "init_isIpAddress", "partition", "partition", "init_dist_es", "init_resolveEndpoint", "init_dist_es", "init_EndpointError", "init_dist_es", "init_EndpointRuleObject", "init_ErrorRuleObject", "init_RuleSetObject", "init_TreeRuleObject", "init_shared", "init_types", "init_EndpointError", "init_EndpointRuleObject", "init_ErrorRuleObject", "init_RuleSetObject", "init_TreeRuleObject", "init_shared", "init_dist_es", "init_isIpAddress", "init_resolveEndpoint", "init_types", "setFeature", "init_dist_es", "init_utils", "init_dist_es", "init_utils", "init_dist_es", "init_utils", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "HEADER_VALUE_TYPE", "init_dist_es", "init_dist_es", "init_dist_es", "value", "serialized", "init_dist_es", "init_dist_es", "init_dist_es", "normalizeProvider", "init_dist_es", "init_dist_es", "init_dist_es", "val", "val", "entityName", "val", "val", "result", "entityName", "val", "XMLParser", "attStr", "val", "XMLParser", "init_dist_es", "val", "init_dist_es", "setFeature", "init_dist_es", "init_constants", "val", "init_dist_es", "init_constants", "init_dist_es", "init_types", "SelectorType", "init_dist_es", "init_types", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "partition", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_types", "init_dist_es", "init_types", "init_config", "RETRY_MODES", "init_constants", "init_dist_es", "init_constants", "init_dist_es", "init_constants", "init_constants", "init_constants", "init_config", "init_constants", "init_config", "init_constants", "init_types", "init_dist_es", "init_config", "init_constants", "init_types", "init_configurations", "init_dist_es", "URL", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es", "init_StandardRetryStrategy", "init_dist_es", "init_AdaptiveRetryStrategy", "init_dist_es", "init_StandardRetryStrategy", "init_dist_es", "init_dist_es", "init_AdaptiveRetryStrategy", "init_StandardRetryStrategy", "init_configurations", "init_constants", "init_dist_es", "Sha256", "init_constants", "init_dist_es", "window", "getRandomValues", "init_module", "Sha256", "init_module", "init_dist_es", "init_module", "init_constants", "init_utils", "init_constants", "init_utils", "init_utils", "init_constants", "init_utils", "init_constants", "init_utils", "init_constants", "init_utils", "init_constants", "init_dist_es", "init_dist_es", "init_dist_es", "init_constants", "init_constants", "init_dist_es", "init_config", "init_isFipsRegion", "init_getRealRegion", "init_isFipsRegion", "init_resolveRegionConfig", "init_getRealRegion", "init_isFipsRegion", "init_regionConfig", "init_config", "init_resolveRegionConfig", "init_dist_es", "init_regionConfig"]}