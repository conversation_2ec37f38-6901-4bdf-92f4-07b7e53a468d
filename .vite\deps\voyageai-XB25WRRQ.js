import {
  require_base64,
  require_browser,
  require_lib,
  require_url_join
} from "./chunk-KXXQHYEK.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/voyageai/api/types/EmbedRequestInput.js
var require_EmbedRequestInput = __commonJS({
  "node_modules/voyageai/api/types/EmbedRequestInput.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/EmbedRequestInputType.js
var require_EmbedRequestInputType = __commonJS({
  "node_modules/voyageai/api/types/EmbedRequestInputType.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedRequestInputType = void 0;
    exports.EmbedRequestInputType = {
      Query: "query",
      Document: "document"
    };
  }
});

// node_modules/voyageai/api/types/EmbedResponseDataItem.js
var require_EmbedResponseDataItem = __commonJS({
  "node_modules/voyageai/api/types/EmbedResponseDataItem.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/EmbedResponseUsage.js
var require_EmbedResponseUsage = __commonJS({
  "node_modules/voyageai/api/types/EmbedResponseUsage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/EmbedResponse.js
var require_EmbedResponse = __commonJS({
  "node_modules/voyageai/api/types/EmbedResponse.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/RerankResponseDataItem.js
var require_RerankResponseDataItem = __commonJS({
  "node_modules/voyageai/api/types/RerankResponseDataItem.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/RerankResponseUsage.js
var require_RerankResponseUsage = __commonJS({
  "node_modules/voyageai/api/types/RerankResponseUsage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/RerankResponse.js
var require_RerankResponse = __commonJS({
  "node_modules/voyageai/api/types/RerankResponse.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/MultimodalEmbedRequestInputsItemContentItem.js
var require_MultimodalEmbedRequestInputsItemContentItem = __commonJS({
  "node_modules/voyageai/api/types/MultimodalEmbedRequestInputsItemContentItem.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/MultimodalEmbedRequestInputsItem.js
var require_MultimodalEmbedRequestInputsItem = __commonJS({
  "node_modules/voyageai/api/types/MultimodalEmbedRequestInputsItem.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/MultimodalEmbedRequestInputType.js
var require_MultimodalEmbedRequestInputType = __commonJS({
  "node_modules/voyageai/api/types/MultimodalEmbedRequestInputType.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedRequestInputType = void 0;
    exports.MultimodalEmbedRequestInputType = {
      Query: "query",
      Document: "document"
    };
  }
});

// node_modules/voyageai/api/types/MultimodalEmbedResponseDataItem.js
var require_MultimodalEmbedResponseDataItem = __commonJS({
  "node_modules/voyageai/api/types/MultimodalEmbedResponseDataItem.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/MultimodalEmbedResponseUsage.js
var require_MultimodalEmbedResponseUsage = __commonJS({
  "node_modules/voyageai/api/types/MultimodalEmbedResponseUsage.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/MultimodalEmbedResponse.js
var require_MultimodalEmbedResponse = __commonJS({
  "node_modules/voyageai/api/types/MultimodalEmbedResponse.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/types/index.js
var require_types = __commonJS({
  "node_modules/voyageai/api/types/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_EmbedRequestInput(), exports);
    __exportStar(require_EmbedRequestInputType(), exports);
    __exportStar(require_EmbedResponseDataItem(), exports);
    __exportStar(require_EmbedResponseUsage(), exports);
    __exportStar(require_EmbedResponse(), exports);
    __exportStar(require_RerankResponseDataItem(), exports);
    __exportStar(require_RerankResponseUsage(), exports);
    __exportStar(require_RerankResponse(), exports);
    __exportStar(require_MultimodalEmbedRequestInputsItemContentItem(), exports);
    __exportStar(require_MultimodalEmbedRequestInputsItem(), exports);
    __exportStar(require_MultimodalEmbedRequestInputType(), exports);
    __exportStar(require_MultimodalEmbedResponseDataItem(), exports);
    __exportStar(require_MultimodalEmbedResponseUsage(), exports);
    __exportStar(require_MultimodalEmbedResponse(), exports);
  }
});

// node_modules/voyageai/api/client/requests/index.js
var require_requests = __commonJS({
  "node_modules/voyageai/api/client/requests/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
  }
});

// node_modules/voyageai/api/client/index.js
var require_client = __commonJS({
  "node_modules/voyageai/api/client/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_requests(), exports);
  }
});

// node_modules/voyageai/api/index.js
var require_api = __commonJS({
  "node_modules/voyageai/api/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_types(), exports);
    __exportStar(require_client(), exports);
  }
});

// node_modules/voyageai/environments.js
var require_environments = __commonJS({
  "node_modules/voyageai/environments.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.VoyageAIEnvironment = void 0;
    exports.VoyageAIEnvironment = {
      Default: "https://api.voyageai.com/v1"
    };
  }
});

// node_modules/voyageai/core/fetcher/createRequestUrl.js
var require_createRequestUrl = __commonJS({
  "node_modules/voyageai/core/fetcher/createRequestUrl.js"(exports) {
    "use strict";
    var __importDefault = exports && exports.__importDefault || function(mod) {
      return mod && mod.__esModule ? mod : { "default": mod };
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.createRequestUrl = void 0;
    var qs_1 = __importDefault(require_lib());
    function createRequestUrl(baseUrl, queryParameters) {
      return Object.keys(queryParameters !== null && queryParameters !== void 0 ? queryParameters : {}).length > 0 ? `${baseUrl}?${qs_1.default.stringify(queryParameters, { arrayFormat: "repeat" })}` : baseUrl;
    }
    exports.createRequestUrl = createRequestUrl;
  }
});

// node_modules/voyageai/core/runtime/runtime.js
var require_runtime = __commonJS({
  "node_modules/voyageai/core/runtime/runtime.js"(exports) {
    "use strict";
    var _a;
    var _b;
    var _c;
    var _d;
    var _e;
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.RUNTIME = void 0;
    var isBrowser = typeof window !== "undefined" && typeof window.document !== "undefined";
    var isWebWorker = typeof self === "object" && // @ts-ignore
    typeof (self === null || self === void 0 ? void 0 : self.importScripts) === "function" && (((_a = self.constructor) === null || _a === void 0 ? void 0 : _a.name) === "DedicatedWorkerGlobalScope" || ((_b = self.constructor) === null || _b === void 0 ? void 0 : _b.name) === "ServiceWorkerGlobalScope" || ((_c = self.constructor) === null || _c === void 0 ? void 0 : _c.name) === "SharedWorkerGlobalScope");
    var isDeno = typeof Deno !== "undefined" && typeof Deno.version !== "undefined" && typeof Deno.version.deno !== "undefined";
    var isBun = typeof Bun !== "undefined" && typeof Bun.version !== "undefined";
    var isNode = typeof process !== "undefined" && Boolean(process.version) && Boolean((_d = process.versions) === null || _d === void 0 ? void 0 : _d.node) && // Deno spoofs process.versions.node, see https://deno.land/std@0.177.0/node/process.ts?s=versions
    !isDeno && !isBun;
    var isReactNative = typeof navigator !== "undefined" && (navigator === null || navigator === void 0 ? void 0 : navigator.product) === "ReactNative";
    var isCloudflare = typeof globalThis !== "undefined" && ((_e = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _e === void 0 ? void 0 : _e.userAgent) === "Cloudflare-Workers";
    exports.RUNTIME = evaluateRuntime();
    function evaluateRuntime() {
      if (isBrowser) {
        return {
          type: "browser",
          version: window.navigator.userAgent
        };
      }
      if (isCloudflare) {
        return {
          type: "workerd"
        };
      }
      if (isWebWorker) {
        return {
          type: "web-worker"
        };
      }
      if (isDeno) {
        return {
          type: "deno",
          version: Deno.version.deno
        };
      }
      if (isBun) {
        return {
          type: "bun",
          version: Bun.version
        };
      }
      if (isNode) {
        return {
          type: "node",
          version: process.versions.node,
          parsedVersion: Number(process.versions.node.split(".")[0])
        };
      }
      if (isReactNative) {
        return {
          type: "react-native"
        };
      }
      return {
        type: "unknown"
      };
    }
  }
});

// node_modules/voyageai/core/runtime/index.js
var require_runtime2 = __commonJS({
  "node_modules/voyageai/core/runtime/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.RUNTIME = void 0;
    var runtime_1 = require_runtime();
    Object.defineProperty(exports, "RUNTIME", { enumerable: true, get: function() {
      return runtime_1.RUNTIME;
    } });
  }
});

// node_modules/voyageai/core/fetcher/getFetchFn.js
var require_getFetchFn = __commonJS({
  "node_modules/voyageai/core/fetcher/getFetchFn.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getFetchFn = void 0;
    var runtime_1 = require_runtime2();
    function getFetchFn() {
      return __awaiter(this, void 0, void 0, function* () {
        if (runtime_1.RUNTIME.type === "node" && runtime_1.RUNTIME.parsedVersion != null && runtime_1.RUNTIME.parsedVersion >= 18) {
          return fetch;
        }
        if (runtime_1.RUNTIME.type === "node") {
          return (yield Promise.resolve().then(() => __importStar(require_browser()))).default;
        }
        if (typeof fetch == "function") {
          return fetch;
        }
        return (yield Promise.resolve().then(() => __importStar(require_browser()))).default;
      });
    }
    exports.getFetchFn = getFetchFn;
  }
});

// node_modules/voyageai/core/fetcher/getRequestBody.js
var require_getRequestBody = __commonJS({
  "node_modules/voyageai/core/fetcher/getRequestBody.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getRequestBody = void 0;
    function getRequestBody({ body, type }) {
      return __awaiter(this, void 0, void 0, function* () {
        if (type.includes("json")) {
          return JSON.stringify(body);
        } else {
          return body;
        }
      });
    }
    exports.getRequestBody = getRequestBody;
  }
});

// node_modules/voyageai/core/fetcher/stream-wrappers/Node18UniversalStreamWrapper.js
var require_Node18UniversalStreamWrapper = __commonJS({
  "node_modules/voyageai/core/fetcher/stream-wrappers/Node18UniversalStreamWrapper.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Node18UniversalStreamWrapper = void 0;
    var Node18UniversalStreamWrapper = class _Node18UniversalStreamWrapper {
      constructor(readableStream) {
        this.readableStream = readableStream;
        this.reader = this.readableStream.getReader();
        this.events = {
          data: [],
          end: [],
          error: [],
          readable: [],
          close: [],
          pause: [],
          resume: []
        };
        this.paused = false;
        this.resumeCallback = null;
        this.encoding = null;
      }
      on(event, callback) {
        var _a;
        (_a = this.events[event]) === null || _a === void 0 ? void 0 : _a.push(callback);
      }
      off(event, callback) {
        var _a;
        this.events[event] = (_a = this.events[event]) === null || _a === void 0 ? void 0 : _a.filter((cb) => cb !== callback);
      }
      pipe(dest) {
        this.on("data", (chunk) => __awaiter(this, void 0, void 0, function* () {
          if (dest instanceof _Node18UniversalStreamWrapper) {
            dest._write(chunk);
          } else if (dest instanceof WritableStream) {
            const writer = dest.getWriter();
            writer.write(chunk).then(() => writer.releaseLock());
          } else {
            dest.write(chunk);
          }
        }));
        this.on("end", () => __awaiter(this, void 0, void 0, function* () {
          if (dest instanceof _Node18UniversalStreamWrapper) {
            dest._end();
          } else if (dest instanceof WritableStream) {
            const writer = dest.getWriter();
            writer.close();
          } else {
            dest.end();
          }
        }));
        this.on("error", (error) => __awaiter(this, void 0, void 0, function* () {
          if (dest instanceof _Node18UniversalStreamWrapper) {
            dest._error(error);
          } else if (dest instanceof WritableStream) {
            const writer = dest.getWriter();
            writer.abort(error);
          } else {
            dest.destroy(error);
          }
        }));
        this._startReading();
        return dest;
      }
      pipeTo(dest) {
        return this.pipe(dest);
      }
      unpipe(dest) {
        this.off("data", (chunk) => __awaiter(this, void 0, void 0, function* () {
          if (dest instanceof _Node18UniversalStreamWrapper) {
            dest._write(chunk);
          } else if (dest instanceof WritableStream) {
            const writer = dest.getWriter();
            writer.write(chunk).then(() => writer.releaseLock());
          } else {
            dest.write(chunk);
          }
        }));
        this.off("end", () => __awaiter(this, void 0, void 0, function* () {
          if (dest instanceof _Node18UniversalStreamWrapper) {
            dest._end();
          } else if (dest instanceof WritableStream) {
            const writer = dest.getWriter();
            writer.close();
          } else {
            dest.end();
          }
        }));
        this.off("error", (error) => __awaiter(this, void 0, void 0, function* () {
          if (dest instanceof _Node18UniversalStreamWrapper) {
            dest._error(error);
          } else if (dest instanceof WritableStream) {
            const writer = dest.getWriter();
            writer.abort(error);
          } else {
            dest.destroy(error);
          }
        }));
      }
      destroy(error) {
        this.reader.cancel(error).then(() => {
          this._emit("close");
        }).catch((err) => {
          this._emit("error", err);
        });
      }
      pause() {
        this.paused = true;
        this._emit("pause");
      }
      resume() {
        if (this.paused) {
          this.paused = false;
          this._emit("resume");
          if (this.resumeCallback) {
            this.resumeCallback();
            this.resumeCallback = null;
          }
        }
      }
      get isPaused() {
        return this.paused;
      }
      read() {
        return __awaiter(this, void 0, void 0, function* () {
          if (this.paused) {
            yield new Promise((resolve) => {
              this.resumeCallback = resolve;
            });
          }
          const { done, value } = yield this.reader.read();
          if (done) {
            return void 0;
          }
          return value;
        });
      }
      setEncoding(encoding) {
        this.encoding = encoding;
      }
      text() {
        return __awaiter(this, void 0, void 0, function* () {
          const chunks = [];
          while (true) {
            const { done, value } = yield this.reader.read();
            if (done) {
              break;
            }
            if (value) {
              chunks.push(value);
            }
          }
          const decoder = new TextDecoder(this.encoding || "utf-8");
          return decoder.decode(yield new Blob(chunks).arrayBuffer());
        });
      }
      json() {
        return __awaiter(this, void 0, void 0, function* () {
          const text = yield this.text();
          return JSON.parse(text);
        });
      }
      _write(chunk) {
        this._emit("data", chunk);
      }
      _end() {
        this._emit("end");
      }
      _error(error) {
        this._emit("error", error);
      }
      _emit(event, data) {
        if (this.events[event]) {
          for (const callback of this.events[event] || []) {
            callback(data);
          }
        }
      }
      _startReading() {
        return __awaiter(this, void 0, void 0, function* () {
          try {
            this._emit("readable");
            while (true) {
              if (this.paused) {
                yield new Promise((resolve) => {
                  this.resumeCallback = resolve;
                });
              }
              const { done, value } = yield this.reader.read();
              if (done) {
                this._emit("end");
                this._emit("close");
                break;
              }
              if (value) {
                this._emit("data", value);
              }
            }
          } catch (error) {
            this._emit("error", error);
          }
        });
      }
      [Symbol.asyncIterator]() {
        return {
          next: () => __awaiter(this, void 0, void 0, function* () {
            if (this.paused) {
              yield new Promise((resolve) => {
                this.resumeCallback = resolve;
              });
            }
            const { done, value } = yield this.reader.read();
            if (done) {
              return { done: true, value: void 0 };
            }
            return { done: false, value };
          }),
          [Symbol.asyncIterator]() {
            return this;
          }
        };
      }
    };
    exports.Node18UniversalStreamWrapper = Node18UniversalStreamWrapper;
  }
});

// node_modules/voyageai/core/fetcher/stream-wrappers/UndiciStreamWrapper.js
var require_UndiciStreamWrapper = __commonJS({
  "node_modules/voyageai/core/fetcher/stream-wrappers/UndiciStreamWrapper.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.UndiciStreamWrapper = void 0;
    var UndiciStreamWrapper = class _UndiciStreamWrapper {
      constructor(readableStream) {
        this.readableStream = readableStream;
        this.reader = this.readableStream.getReader();
        this.events = {
          data: [],
          end: [],
          error: [],
          readable: [],
          close: [],
          pause: [],
          resume: []
        };
        this.paused = false;
        this.resumeCallback = null;
        this.encoding = null;
      }
      on(event, callback) {
        var _a;
        (_a = this.events[event]) === null || _a === void 0 ? void 0 : _a.push(callback);
      }
      off(event, callback) {
        var _a;
        this.events[event] = (_a = this.events[event]) === null || _a === void 0 ? void 0 : _a.filter((cb) => cb !== callback);
      }
      pipe(dest) {
        this.on("data", (chunk) => {
          if (dest instanceof _UndiciStreamWrapper) {
            dest._write(chunk);
          } else {
            const writer = dest.getWriter();
            writer.write(chunk).then(() => writer.releaseLock());
          }
        });
        this.on("end", () => {
          if (dest instanceof _UndiciStreamWrapper) {
            dest._end();
          } else {
            const writer = dest.getWriter();
            writer.close();
          }
        });
        this.on("error", (error) => {
          if (dest instanceof _UndiciStreamWrapper) {
            dest._error(error);
          } else {
            const writer = dest.getWriter();
            writer.abort(error);
          }
        });
        this._startReading();
        return dest;
      }
      pipeTo(dest) {
        return this.pipe(dest);
      }
      unpipe(dest) {
        this.off("data", (chunk) => {
          if (dest instanceof _UndiciStreamWrapper) {
            dest._write(chunk);
          } else {
            const writer = dest.getWriter();
            writer.write(chunk).then(() => writer.releaseLock());
          }
        });
        this.off("end", () => {
          if (dest instanceof _UndiciStreamWrapper) {
            dest._end();
          } else {
            const writer = dest.getWriter();
            writer.close();
          }
        });
        this.off("error", (error) => {
          if (dest instanceof _UndiciStreamWrapper) {
            dest._error(error);
          } else {
            const writer = dest.getWriter();
            writer.abort(error);
          }
        });
      }
      destroy(error) {
        this.reader.cancel(error).then(() => {
          this._emit("close");
        }).catch((err) => {
          this._emit("error", err);
        });
      }
      pause() {
        this.paused = true;
        this._emit("pause");
      }
      resume() {
        if (this.paused) {
          this.paused = false;
          this._emit("resume");
          if (this.resumeCallback) {
            this.resumeCallback();
            this.resumeCallback = null;
          }
        }
      }
      get isPaused() {
        return this.paused;
      }
      read() {
        return __awaiter(this, void 0, void 0, function* () {
          if (this.paused) {
            yield new Promise((resolve) => {
              this.resumeCallback = resolve;
            });
          }
          const { done, value } = yield this.reader.read();
          if (done) {
            return void 0;
          }
          return value;
        });
      }
      setEncoding(encoding) {
        this.encoding = encoding;
      }
      text() {
        return __awaiter(this, void 0, void 0, function* () {
          const chunks = [];
          while (true) {
            const { done, value } = yield this.reader.read();
            if (done) {
              break;
            }
            if (value) {
              chunks.push(value);
            }
          }
          const decoder = new TextDecoder(this.encoding || "utf-8");
          return decoder.decode(yield new Blob(chunks).arrayBuffer());
        });
      }
      json() {
        return __awaiter(this, void 0, void 0, function* () {
          const text = yield this.text();
          return JSON.parse(text);
        });
      }
      _write(chunk) {
        this._emit("data", chunk);
      }
      _end() {
        this._emit("end");
      }
      _error(error) {
        this._emit("error", error);
      }
      _emit(event, data) {
        if (this.events[event]) {
          for (const callback of this.events[event] || []) {
            callback(data);
          }
        }
      }
      _startReading() {
        return __awaiter(this, void 0, void 0, function* () {
          try {
            this._emit("readable");
            while (true) {
              if (this.paused) {
                yield new Promise((resolve) => {
                  this.resumeCallback = resolve;
                });
              }
              const { done, value } = yield this.reader.read();
              if (done) {
                this._emit("end");
                this._emit("close");
                break;
              }
              if (value) {
                this._emit("data", value);
              }
            }
          } catch (error) {
            this._emit("error", error);
          }
        });
      }
      [Symbol.asyncIterator]() {
        return {
          next: () => __awaiter(this, void 0, void 0, function* () {
            if (this.paused) {
              yield new Promise((resolve) => {
                this.resumeCallback = resolve;
              });
            }
            const { done, value } = yield this.reader.read();
            if (done) {
              return { done: true, value: void 0 };
            }
            return { done: false, value };
          }),
          [Symbol.asyncIterator]() {
            return this;
          }
        };
      }
    };
    exports.UndiciStreamWrapper = UndiciStreamWrapper;
  }
});

// node_modules/voyageai/core/fetcher/stream-wrappers/NodePre18StreamWrapper.js
var require_NodePre18StreamWrapper = __commonJS({
  "node_modules/voyageai/core/fetcher/stream-wrappers/NodePre18StreamWrapper.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __asyncValues = exports && exports.__asyncValues || function(o) {
      if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
      var m = o[Symbol.asyncIterator], i;
      return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
      }, i);
      function verb(n) {
        i[n] = o[n] && function(v) {
          return new Promise(function(resolve, reject) {
            v = o[n](v), settle(resolve, reject, v.done, v.value);
          });
        };
      }
      function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v2) {
          resolve({ value: v2, done: d });
        }, reject);
      }
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.NodePre18StreamWrapper = void 0;
    var NodePre18StreamWrapper = class {
      constructor(readableStream) {
        this.readableStream = readableStream;
      }
      on(event, callback) {
        this.readableStream.on(event, callback);
      }
      off(event, callback) {
        this.readableStream.off(event, callback);
      }
      pipe(dest) {
        this.readableStream.pipe(dest);
        return dest;
      }
      pipeTo(dest) {
        return this.pipe(dest);
      }
      unpipe(dest) {
        if (dest) {
          this.readableStream.unpipe(dest);
        } else {
          this.readableStream.unpipe();
        }
      }
      destroy(error) {
        this.readableStream.destroy(error);
      }
      pause() {
        this.readableStream.pause();
      }
      resume() {
        this.readableStream.resume();
      }
      get isPaused() {
        return this.readableStream.isPaused();
      }
      read() {
        return __awaiter(this, void 0, void 0, function* () {
          return new Promise((resolve, reject) => {
            const chunk = this.readableStream.read();
            if (chunk) {
              resolve(chunk);
            } else {
              this.readableStream.once("readable", () => {
                const chunk2 = this.readableStream.read();
                resolve(chunk2);
              });
              this.readableStream.once("error", reject);
            }
          });
        });
      }
      setEncoding(encoding) {
        this.readableStream.setEncoding(encoding);
        this.encoding = encoding;
      }
      text() {
        var e_1, _a;
        return __awaiter(this, void 0, void 0, function* () {
          const chunks = [];
          const encoder = new TextEncoder();
          this.readableStream.setEncoding(this.encoding || "utf-8");
          try {
            for (var _b = __asyncValues(this.readableStream), _c; _c = yield _b.next(), !_c.done; ) {
              const chunk = _c.value;
              chunks.push(encoder.encode(chunk));
            }
          } catch (e_1_1) {
            e_1 = { error: e_1_1 };
          } finally {
            try {
              if (_c && !_c.done && (_a = _b.return)) yield _a.call(_b);
            } finally {
              if (e_1) throw e_1.error;
            }
          }
          const decoder = new TextDecoder(this.encoding || "utf-8");
          return decoder.decode(Buffer.concat(chunks));
        });
      }
      json() {
        return __awaiter(this, void 0, void 0, function* () {
          const text = yield this.text();
          return JSON.parse(text);
        });
      }
      [Symbol.asyncIterator]() {
        const readableStream = this.readableStream;
        const iterator = readableStream[Symbol.asyncIterator]();
        return {
          next() {
            return __awaiter(this, void 0, void 0, function* () {
              const { value, done } = yield iterator.next();
              return { value, done };
            });
          },
          [Symbol.asyncIterator]() {
            return this;
          }
        };
      }
    };
    exports.NodePre18StreamWrapper = NodePre18StreamWrapper;
  }
});

// node_modules/voyageai/core/fetcher/stream-wrappers/chooseStreamWrapper.js
var require_chooseStreamWrapper = __commonJS({
  "node_modules/voyageai/core/fetcher/stream-wrappers/chooseStreamWrapper.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.chooseStreamWrapper = void 0;
    var runtime_1 = require_runtime2();
    function chooseStreamWrapper(responseBody) {
      return __awaiter(this, void 0, void 0, function* () {
        if (runtime_1.RUNTIME.type === "node" && runtime_1.RUNTIME.parsedVersion != null && runtime_1.RUNTIME.parsedVersion >= 18) {
          return new (yield Promise.resolve().then(() => __importStar(require_Node18UniversalStreamWrapper()))).Node18UniversalStreamWrapper(responseBody);
        } else if (runtime_1.RUNTIME.type !== "node" && typeof fetch === "function") {
          return new (yield Promise.resolve().then(() => __importStar(require_UndiciStreamWrapper()))).UndiciStreamWrapper(responseBody);
        } else {
          return new (yield Promise.resolve().then(() => __importStar(require_NodePre18StreamWrapper()))).NodePre18StreamWrapper(responseBody);
        }
      });
    }
    exports.chooseStreamWrapper = chooseStreamWrapper;
  }
});

// node_modules/voyageai/core/fetcher/getResponseBody.js
var require_getResponseBody = __commonJS({
  "node_modules/voyageai/core/fetcher/getResponseBody.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getResponseBody = void 0;
    var chooseStreamWrapper_1 = require_chooseStreamWrapper();
    function getResponseBody(response, responseType) {
      return __awaiter(this, void 0, void 0, function* () {
        if (response.body != null && responseType === "blob") {
          return yield response.blob();
        } else if (response.body != null && responseType === "sse") {
          return response.body;
        } else if (response.body != null && responseType === "streaming") {
          return (0, chooseStreamWrapper_1.chooseStreamWrapper)(response.body);
        } else if (response.body != null && responseType === "text") {
          return yield response.text();
        } else {
          const text = yield response.text();
          if (text.length > 0) {
            try {
              let responseBody = JSON.parse(text);
              return responseBody;
            } catch (err) {
              return {
                ok: false,
                error: {
                  reason: "non-json",
                  statusCode: response.status,
                  rawBody: text
                }
              };
            }
          } else {
            return void 0;
          }
        }
      });
    }
    exports.getResponseBody = getResponseBody;
  }
});

// node_modules/voyageai/core/fetcher/signals.js
var require_signals = __commonJS({
  "node_modules/voyageai/core/fetcher/signals.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.anySignal = exports.getTimeoutSignal = void 0;
    var TIMEOUT = "timeout";
    function getTimeoutSignal(timeoutMs) {
      const controller = new AbortController();
      const abortId = setTimeout(() => controller.abort(TIMEOUT), timeoutMs);
      return { signal: controller.signal, abortId };
    }
    exports.getTimeoutSignal = getTimeoutSignal;
    function anySignal(...args) {
      const signals = args.length === 1 && Array.isArray(args[0]) ? args[0] : args;
      const controller = new AbortController();
      for (const signal of signals) {
        if (signal.aborted) {
          controller.abort(signal === null || signal === void 0 ? void 0 : signal.reason);
          break;
        }
        signal.addEventListener("abort", () => controller.abort(signal === null || signal === void 0 ? void 0 : signal.reason), {
          signal: controller.signal
        });
      }
      return controller.signal;
    }
    exports.anySignal = anySignal;
  }
});

// node_modules/voyageai/core/fetcher/makeRequest.js
var require_makeRequest = __commonJS({
  "node_modules/voyageai/core/fetcher/makeRequest.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.makeRequest = void 0;
    var signals_1 = require_signals();
    var makeRequest = (fetchFn, url, method, headers, requestBody, timeoutMs, abortSignal, withCredentials, duplex) => __awaiter(void 0, void 0, void 0, function* () {
      const signals = [];
      let timeoutAbortId = void 0;
      if (timeoutMs != null) {
        const { signal, abortId } = (0, signals_1.getTimeoutSignal)(timeoutMs);
        timeoutAbortId = abortId;
        signals.push(signal);
      }
      if (abortSignal != null) {
        signals.push(abortSignal);
      }
      let newSignals = (0, signals_1.anySignal)(signals);
      const response = yield fetchFn(url, {
        method,
        headers,
        body: requestBody,
        signal: newSignals,
        credentials: withCredentials ? "include" : void 0,
        // @ts-ignore
        duplex
      });
      if (timeoutAbortId != null) {
        clearTimeout(timeoutAbortId);
      }
      return response;
    });
    exports.makeRequest = makeRequest;
  }
});

// node_modules/voyageai/core/fetcher/requestWithRetries.js
var require_requestWithRetries = __commonJS({
  "node_modules/voyageai/core/fetcher/requestWithRetries.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.requestWithRetries = void 0;
    var INITIAL_RETRY_DELAY = 1;
    var MAX_RETRY_DELAY = 60;
    var DEFAULT_MAX_RETRIES = 2;
    function requestWithRetries(requestFn, maxRetries = DEFAULT_MAX_RETRIES) {
      return __awaiter(this, void 0, void 0, function* () {
        let response = yield requestFn();
        for (let i = 0; i < maxRetries; ++i) {
          if ([408, 409, 429].includes(response.status) || response.status >= 500) {
            const delay = Math.min(INITIAL_RETRY_DELAY * Math.pow(2, i), MAX_RETRY_DELAY);
            yield new Promise((resolve) => setTimeout(resolve, delay));
            response = yield requestFn();
          } else {
            break;
          }
        }
        return response;
      });
    }
    exports.requestWithRetries = requestWithRetries;
  }
});

// node_modules/voyageai/core/fetcher/Fetcher.js
var require_Fetcher = __commonJS({
  "node_modules/voyageai/core/fetcher/Fetcher.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.fetcher = exports.fetcherImpl = void 0;
    var createRequestUrl_1 = require_createRequestUrl();
    var getFetchFn_1 = require_getFetchFn();
    var getRequestBody_1 = require_getRequestBody();
    var getResponseBody_1 = require_getResponseBody();
    var makeRequest_1 = require_makeRequest();
    var requestWithRetries_1 = require_requestWithRetries();
    function fetcherImpl(args) {
      return __awaiter(this, void 0, void 0, function* () {
        const headers = {};
        if (args.body !== void 0 && args.contentType != null) {
          headers["Content-Type"] = args.contentType;
        }
        if (args.headers != null) {
          for (const [key, value] of Object.entries(args.headers)) {
            if (value != null) {
              headers[key] = value;
            }
          }
        }
        const url = (0, createRequestUrl_1.createRequestUrl)(args.url, args.queryParameters);
        let requestBody = yield (0, getRequestBody_1.getRequestBody)({
          body: args.body,
          type: args.requestType === "json" ? "json" : "other"
        });
        const fetchFn = yield (0, getFetchFn_1.getFetchFn)();
        try {
          const response = yield (0, requestWithRetries_1.requestWithRetries)(() => __awaiter(this, void 0, void 0, function* () {
            return (0, makeRequest_1.makeRequest)(fetchFn, url, args.method, headers, requestBody, args.timeoutMs, args.abortSignal, args.withCredentials, args.duplex);
          }), args.maxRetries);
          let responseBody = yield (0, getResponseBody_1.getResponseBody)(response, args.responseType);
          if (response.status >= 200 && response.status < 400) {
            return {
              ok: true,
              body: responseBody,
              headers: response.headers
            };
          } else {
            return {
              ok: false,
              error: {
                reason: "status-code",
                statusCode: response.status,
                body: responseBody
              }
            };
          }
        } catch (error) {
          if (args.abortSignal != null && args.abortSignal.aborted) {
            return {
              ok: false,
              error: {
                reason: "unknown",
                errorMessage: "The user aborted a request"
              }
            };
          } else if (error instanceof Error && error.name === "AbortError") {
            return {
              ok: false,
              error: {
                reason: "timeout"
              }
            };
          } else if (error instanceof Error) {
            return {
              ok: false,
              error: {
                reason: "unknown",
                errorMessage: error.message
              }
            };
          }
          return {
            ok: false,
            error: {
              reason: "unknown",
              errorMessage: JSON.stringify(error)
            }
          };
        }
      });
    }
    exports.fetcherImpl = fetcherImpl;
    exports.fetcher = fetcherImpl;
  }
});

// node_modules/voyageai/core/fetcher/getHeader.js
var require_getHeader = __commonJS({
  "node_modules/voyageai/core/fetcher/getHeader.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getHeader = void 0;
    function getHeader(headers, header) {
      for (const [headerKey, headerValue] of Object.entries(headers)) {
        if (headerKey.toLowerCase() === header.toLowerCase()) {
          return headerValue;
        }
      }
      return void 0;
    }
    exports.getHeader = getHeader;
  }
});

// node_modules/voyageai/core/fetcher/Supplier.js
var require_Supplier = __commonJS({
  "node_modules/voyageai/core/fetcher/Supplier.js"(exports) {
    "use strict";
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Supplier = void 0;
    exports.Supplier = {
      get: (supplier) => __awaiter(void 0, void 0, void 0, function* () {
        if (typeof supplier === "function") {
          return supplier();
        } else {
          return supplier;
        }
      })
    };
  }
});

// node_modules/voyageai/core/fetcher/index.js
var require_fetcher = __commonJS({
  "node_modules/voyageai/core/fetcher/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Supplier = exports.getHeader = exports.fetcher = void 0;
    var Fetcher_1 = require_Fetcher();
    Object.defineProperty(exports, "fetcher", { enumerable: true, get: function() {
      return Fetcher_1.fetcher;
    } });
    var getHeader_1 = require_getHeader();
    Object.defineProperty(exports, "getHeader", { enumerable: true, get: function() {
      return getHeader_1.getHeader;
    } });
    var Supplier_1 = require_Supplier();
    Object.defineProperty(exports, "Supplier", { enumerable: true, get: function() {
      return Supplier_1.Supplier;
    } });
  }
});

// node_modules/voyageai/core/auth/BasicAuth.js
var require_BasicAuth = __commonJS({
  "node_modules/voyageai/core/auth/BasicAuth.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BasicAuth = void 0;
    var js_base64_1 = require_base64();
    var BASIC_AUTH_HEADER_PREFIX = /^Basic /i;
    exports.BasicAuth = {
      toAuthorizationHeader: (basicAuth) => {
        if (basicAuth == null) {
          return void 0;
        }
        const token = js_base64_1.Base64.encode(`${basicAuth.username}:${basicAuth.password}`);
        return `Basic ${token}`;
      },
      fromAuthorizationHeader: (header) => {
        const credentials = header.replace(BASIC_AUTH_HEADER_PREFIX, "");
        const decoded = js_base64_1.Base64.decode(credentials);
        const [username, password] = decoded.split(":", 2);
        if (username == null || password == null) {
          throw new Error("Invalid basic auth");
        }
        return {
          username,
          password
        };
      }
    };
  }
});

// node_modules/voyageai/core/auth/BearerToken.js
var require_BearerToken = __commonJS({
  "node_modules/voyageai/core/auth/BearerToken.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BearerToken = void 0;
    var BEARER_AUTH_HEADER_PREFIX = /^Bearer /i;
    exports.BearerToken = {
      toAuthorizationHeader: (token) => {
        if (token == null) {
          return void 0;
        }
        return `Bearer ${token}`;
      },
      fromAuthorizationHeader: (header) => {
        return header.replace(BEARER_AUTH_HEADER_PREFIX, "").trim();
      }
    };
  }
});

// node_modules/voyageai/core/auth/index.js
var require_auth = __commonJS({
  "node_modules/voyageai/core/auth/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BearerToken = exports.BasicAuth = void 0;
    var BasicAuth_1 = require_BasicAuth();
    Object.defineProperty(exports, "BasicAuth", { enumerable: true, get: function() {
      return BasicAuth_1.BasicAuth;
    } });
    var BearerToken_1 = require_BearerToken();
    Object.defineProperty(exports, "BearerToken", { enumerable: true, get: function() {
      return BearerToken_1.BearerToken;
    } });
  }
});

// node_modules/voyageai/core/schemas/Schema.js
var require_Schema = __commonJS({
  "node_modules/voyageai/core/schemas/Schema.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SchemaType = void 0;
    exports.SchemaType = {
      DATE: "date",
      ENUM: "enum",
      LIST: "list",
      STRING_LITERAL: "stringLiteral",
      BOOLEAN_LITERAL: "booleanLiteral",
      OBJECT: "object",
      ANY: "any",
      BOOLEAN: "boolean",
      NUMBER: "number",
      STRING: "string",
      UNKNOWN: "unknown",
      RECORD: "record",
      SET: "set",
      UNION: "union",
      UNDISCRIMINATED_UNION: "undiscriminatedUnion",
      OPTIONAL: "optional"
    };
  }
});

// node_modules/voyageai/core/schemas/utils/getErrorMessageForIncorrectType.js
var require_getErrorMessageForIncorrectType = __commonJS({
  "node_modules/voyageai/core/schemas/utils/getErrorMessageForIncorrectType.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getErrorMessageForIncorrectType = void 0;
    function getErrorMessageForIncorrectType(value, expectedType) {
      return `Expected ${expectedType}. Received ${getTypeAsString(value)}.`;
    }
    exports.getErrorMessageForIncorrectType = getErrorMessageForIncorrectType;
    function getTypeAsString(value) {
      if (Array.isArray(value)) {
        return "list";
      }
      if (value === null) {
        return "null";
      }
      switch (typeof value) {
        case "string":
          return `"${value}"`;
        case "number":
        case "boolean":
        case "undefined":
          return `${value}`;
      }
      return typeof value;
    }
  }
});

// node_modules/voyageai/core/schemas/utils/maybeSkipValidation.js
var require_maybeSkipValidation = __commonJS({
  "node_modules/voyageai/core/schemas/utils/maybeSkipValidation.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.maybeSkipValidation = void 0;
    function maybeSkipValidation(schema) {
      return Object.assign(Object.assign({}, schema), { json: transformAndMaybeSkipValidation(schema.json), parse: transformAndMaybeSkipValidation(schema.parse) });
    }
    exports.maybeSkipValidation = maybeSkipValidation;
    function transformAndMaybeSkipValidation(transform) {
      return (value, opts) => {
        const transformed = transform(value, opts);
        const { skipValidation = false } = opts !== null && opts !== void 0 ? opts : {};
        if (!transformed.ok && skipValidation) {
          console.warn([
            "Failed to validate.",
            ...transformed.errors.map((error) => "  - " + (error.path.length > 0 ? `${error.path.join(".")}: ${error.message}` : error.message))
          ].join("\n"));
          return {
            ok: true,
            value
          };
        } else {
          return transformed;
        }
      };
    }
  }
});

// node_modules/voyageai/core/schemas/builders/schema-utils/stringifyValidationErrors.js
var require_stringifyValidationErrors = __commonJS({
  "node_modules/voyageai/core/schemas/builders/schema-utils/stringifyValidationErrors.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.stringifyValidationError = void 0;
    function stringifyValidationError(error) {
      if (error.path.length === 0) {
        return error.message;
      }
      return `${error.path.join(" -> ")}: ${error.message}`;
    }
    exports.stringifyValidationError = stringifyValidationError;
  }
});

// node_modules/voyageai/core/schemas/builders/schema-utils/JsonError.js
var require_JsonError = __commonJS({
  "node_modules/voyageai/core/schemas/builders/schema-utils/JsonError.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.JsonError = void 0;
    var stringifyValidationErrors_1 = require_stringifyValidationErrors();
    var JsonError = class _JsonError extends Error {
      constructor(errors) {
        super(errors.map(stringifyValidationErrors_1.stringifyValidationError).join("; "));
        this.errors = errors;
        Object.setPrototypeOf(this, _JsonError.prototype);
      }
    };
    exports.JsonError = JsonError;
  }
});

// node_modules/voyageai/core/schemas/builders/schema-utils/ParseError.js
var require_ParseError = __commonJS({
  "node_modules/voyageai/core/schemas/builders/schema-utils/ParseError.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.ParseError = void 0;
    var stringifyValidationErrors_1 = require_stringifyValidationErrors();
    var ParseError = class _ParseError extends Error {
      constructor(errors) {
        super(errors.map(stringifyValidationErrors_1.stringifyValidationError).join("; "));
        this.errors = errors;
        Object.setPrototypeOf(this, _ParseError.prototype);
      }
    };
    exports.ParseError = ParseError;
  }
});

// node_modules/voyageai/core/schemas/builders/schema-utils/getSchemaUtils.js
var require_getSchemaUtils = __commonJS({
  "node_modules/voyageai/core/schemas/builders/schema-utils/getSchemaUtils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.transform = exports.optional = exports.getSchemaUtils = void 0;
    var Schema_1 = require_Schema();
    var JsonError_1 = require_JsonError();
    var ParseError_1 = require_ParseError();
    function getSchemaUtils(schema) {
      return {
        optional: () => optional(schema),
        transform: (transformer) => transform(schema, transformer),
        parseOrThrow: (raw, opts) => {
          const parsed = schema.parse(raw, opts);
          if (parsed.ok) {
            return parsed.value;
          }
          throw new ParseError_1.ParseError(parsed.errors);
        },
        jsonOrThrow: (parsed, opts) => {
          const raw = schema.json(parsed, opts);
          if (raw.ok) {
            return raw.value;
          }
          throw new JsonError_1.JsonError(raw.errors);
        }
      };
    }
    exports.getSchemaUtils = getSchemaUtils;
    function optional(schema) {
      const baseSchema = {
        parse: (raw, opts) => {
          if (raw == null) {
            return {
              ok: true,
              value: void 0
            };
          }
          return schema.parse(raw, opts);
        },
        json: (parsed, opts) => {
          if ((opts === null || opts === void 0 ? void 0 : opts.omitUndefined) && parsed === void 0) {
            return {
              ok: true,
              value: void 0
            };
          }
          if (parsed == null) {
            return {
              ok: true,
              value: null
            };
          }
          return schema.json(parsed, opts);
        },
        getType: () => Schema_1.SchemaType.OPTIONAL
      };
      return Object.assign(Object.assign({}, baseSchema), getSchemaUtils(baseSchema));
    }
    exports.optional = optional;
    function transform(schema, transformer) {
      const baseSchema = {
        parse: (raw, opts) => {
          const parsed = schema.parse(raw, opts);
          if (!parsed.ok) {
            return parsed;
          }
          return {
            ok: true,
            value: transformer.transform(parsed.value)
          };
        },
        json: (transformed, opts) => {
          const parsed = transformer.untransform(transformed);
          return schema.json(parsed, opts);
        },
        getType: () => schema.getType()
      };
      return Object.assign(Object.assign({}, baseSchema), getSchemaUtils(baseSchema));
    }
    exports.transform = transform;
  }
});

// node_modules/voyageai/core/schemas/builders/schema-utils/index.js
var require_schema_utils = __commonJS({
  "node_modules/voyageai/core/schemas/builders/schema-utils/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.ParseError = exports.JsonError = exports.transform = exports.optional = exports.getSchemaUtils = void 0;
    var getSchemaUtils_1 = require_getSchemaUtils();
    Object.defineProperty(exports, "getSchemaUtils", { enumerable: true, get: function() {
      return getSchemaUtils_1.getSchemaUtils;
    } });
    Object.defineProperty(exports, "optional", { enumerable: true, get: function() {
      return getSchemaUtils_1.optional;
    } });
    Object.defineProperty(exports, "transform", { enumerable: true, get: function() {
      return getSchemaUtils_1.transform;
    } });
    var JsonError_1 = require_JsonError();
    Object.defineProperty(exports, "JsonError", { enumerable: true, get: function() {
      return JsonError_1.JsonError;
    } });
    var ParseError_1 = require_ParseError();
    Object.defineProperty(exports, "ParseError", { enumerable: true, get: function() {
      return ParseError_1.ParseError;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/date/date.js
var require_date = __commonJS({
  "node_modules/voyageai/core/schemas/builders/date/date.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.date = void 0;
    var Schema_1 = require_Schema();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var schema_utils_1 = require_schema_utils();
    var ISO_8601_REGEX = /^([+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([.,]\d+(?!:))?)?(\17[0-5]\d([.,]\d+)?)?([zZ]|([+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;
    function date() {
      const baseSchema = {
        parse: (raw, { breadcrumbsPrefix = [] } = {}) => {
          if (typeof raw !== "string") {
            return {
              ok: false,
              errors: [
                {
                  path: breadcrumbsPrefix,
                  message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(raw, "string")
                }
              ]
            };
          }
          if (!ISO_8601_REGEX.test(raw)) {
            return {
              ok: false,
              errors: [
                {
                  path: breadcrumbsPrefix,
                  message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(raw, "ISO 8601 date string")
                }
              ]
            };
          }
          return {
            ok: true,
            value: new Date(raw)
          };
        },
        json: (date2, { breadcrumbsPrefix = [] } = {}) => {
          if (date2 instanceof Date) {
            return {
              ok: true,
              value: date2.toISOString()
            };
          } else {
            return {
              ok: false,
              errors: [
                {
                  path: breadcrumbsPrefix,
                  message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(date2, "Date object")
                }
              ]
            };
          }
        },
        getType: () => Schema_1.SchemaType.DATE
      };
      return Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema));
    }
    exports.date = date;
  }
});

// node_modules/voyageai/core/schemas/builders/date/index.js
var require_date2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/date/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.date = void 0;
    var date_1 = require_date();
    Object.defineProperty(exports, "date", { enumerable: true, get: function() {
      return date_1.date;
    } });
  }
});

// node_modules/voyageai/core/schemas/utils/createIdentitySchemaCreator.js
var require_createIdentitySchemaCreator = __commonJS({
  "node_modules/voyageai/core/schemas/utils/createIdentitySchemaCreator.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.createIdentitySchemaCreator = void 0;
    var schema_utils_1 = require_schema_utils();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    function createIdentitySchemaCreator(schemaType, validate) {
      return () => {
        const baseSchema = {
          parse: validate,
          json: validate,
          getType: () => schemaType
        };
        return Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema));
      };
    }
    exports.createIdentitySchemaCreator = createIdentitySchemaCreator;
  }
});

// node_modules/voyageai/core/schemas/builders/enum/enum.js
var require_enum = __commonJS({
  "node_modules/voyageai/core/schemas/builders/enum/enum.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.enum_ = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    function enum_(values) {
      const validValues = new Set(values);
      const schemaCreator = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.ENUM, (value, { allowUnrecognizedEnumValues, breadcrumbsPrefix = [] } = {}) => {
        if (typeof value !== "string") {
          return {
            ok: false,
            errors: [
              {
                path: breadcrumbsPrefix,
                message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "string")
              }
            ]
          };
        }
        if (!validValues.has(value) && !allowUnrecognizedEnumValues) {
          return {
            ok: false,
            errors: [
              {
                path: breadcrumbsPrefix,
                message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "enum")
              }
            ]
          };
        }
        return {
          ok: true,
          value
        };
      });
      return schemaCreator();
    }
    exports.enum_ = enum_;
  }
});

// node_modules/voyageai/core/schemas/builders/enum/index.js
var require_enum2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/enum/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.enum_ = void 0;
    var enum_1 = require_enum();
    Object.defineProperty(exports, "enum_", { enumerable: true, get: function() {
      return enum_1.enum_;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/lazy/lazy.js
var require_lazy = __commonJS({
  "node_modules/voyageai/core/schemas/builders/lazy/lazy.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getMemoizedSchema = exports.constructLazyBaseSchema = exports.lazy = void 0;
    var schema_utils_1 = require_schema_utils();
    function lazy(getter) {
      const baseSchema = constructLazyBaseSchema(getter);
      return Object.assign(Object.assign({}, baseSchema), (0, schema_utils_1.getSchemaUtils)(baseSchema));
    }
    exports.lazy = lazy;
    function constructLazyBaseSchema(getter) {
      return {
        parse: (raw, opts) => getMemoizedSchema(getter).parse(raw, opts),
        json: (parsed, opts) => getMemoizedSchema(getter).json(parsed, opts),
        getType: () => getMemoizedSchema(getter).getType()
      };
    }
    exports.constructLazyBaseSchema = constructLazyBaseSchema;
    function getMemoizedSchema(getter) {
      const castedGetter = getter;
      if (castedGetter.__zurg_memoized == null) {
        castedGetter.__zurg_memoized = getter();
      }
      return castedGetter.__zurg_memoized;
    }
    exports.getMemoizedSchema = getMemoizedSchema;
  }
});

// node_modules/voyageai/core/schemas/utils/entries.js
var require_entries = __commonJS({
  "node_modules/voyageai/core/schemas/utils/entries.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.entries = void 0;
    function entries(object) {
      return Object.entries(object);
    }
    exports.entries = entries;
  }
});

// node_modules/voyageai/core/schemas/utils/filterObject.js
var require_filterObject = __commonJS({
  "node_modules/voyageai/core/schemas/utils/filterObject.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.filterObject = void 0;
    function filterObject(obj, keysToInclude) {
      const keysToIncludeSet = new Set(keysToInclude);
      return Object.entries(obj).reduce((acc, [key, value]) => {
        if (keysToIncludeSet.has(key)) {
          acc[key] = value;
        }
        return acc;
      }, {});
    }
    exports.filterObject = filterObject;
  }
});

// node_modules/voyageai/core/schemas/utils/isPlainObject.js
var require_isPlainObject = __commonJS({
  "node_modules/voyageai/core/schemas/utils/isPlainObject.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isPlainObject = void 0;
    function isPlainObject(value) {
      if (typeof value !== "object" || value === null) {
        return false;
      }
      if (Object.getPrototypeOf(value) === null) {
        return true;
      }
      let proto = value;
      while (Object.getPrototypeOf(proto) !== null) {
        proto = Object.getPrototypeOf(proto);
      }
      return Object.getPrototypeOf(value) === proto;
    }
    exports.isPlainObject = isPlainObject;
  }
});

// node_modules/voyageai/core/schemas/utils/keys.js
var require_keys = __commonJS({
  "node_modules/voyageai/core/schemas/utils/keys.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.keys = void 0;
    function keys(object) {
      return Object.keys(object);
    }
    exports.keys = keys;
  }
});

// node_modules/voyageai/core/schemas/utils/partition.js
var require_partition = __commonJS({
  "node_modules/voyageai/core/schemas/utils/partition.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.partition = void 0;
    function partition(items, predicate) {
      const trueItems = [], falseItems = [];
      for (const item of items) {
        if (predicate(item)) {
          trueItems.push(item);
        } else {
          falseItems.push(item);
        }
      }
      return [trueItems, falseItems];
    }
    exports.partition = partition;
  }
});

// node_modules/voyageai/core/schemas/builders/object-like/getObjectLikeUtils.js
var require_getObjectLikeUtils = __commonJS({
  "node_modules/voyageai/core/schemas/builders/object-like/getObjectLikeUtils.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.withParsedProperties = exports.getObjectLikeUtils = void 0;
    var filterObject_1 = require_filterObject();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var isPlainObject_1 = require_isPlainObject();
    var schema_utils_1 = require_schema_utils();
    function getObjectLikeUtils(schema) {
      return {
        withParsedProperties: (properties) => withParsedProperties(schema, properties)
      };
    }
    exports.getObjectLikeUtils = getObjectLikeUtils;
    function withParsedProperties(objectLike, properties) {
      const objectSchema = {
        parse: (raw, opts) => {
          const parsedObject = objectLike.parse(raw, opts);
          if (!parsedObject.ok) {
            return parsedObject;
          }
          const additionalProperties = Object.entries(properties).reduce((processed, [key, value]) => {
            return Object.assign(Object.assign({}, processed), { [key]: typeof value === "function" ? value(parsedObject.value) : value });
          }, {});
          return {
            ok: true,
            value: Object.assign(Object.assign({}, parsedObject.value), additionalProperties)
          };
        },
        json: (parsed, opts) => {
          var _a;
          if (!(0, isPlainObject_1.isPlainObject)(parsed)) {
            return {
              ok: false,
              errors: [
                {
                  path: (_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [],
                  message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(parsed, "object")
                }
              ]
            };
          }
          const addedPropertyKeys = new Set(Object.keys(properties));
          const parsedWithoutAddedProperties = (0, filterObject_1.filterObject)(parsed, Object.keys(parsed).filter((key) => !addedPropertyKeys.has(key)));
          return objectLike.json(parsedWithoutAddedProperties, opts);
        },
        getType: () => objectLike.getType()
      };
      return Object.assign(Object.assign(Object.assign({}, objectSchema), (0, schema_utils_1.getSchemaUtils)(objectSchema)), getObjectLikeUtils(objectSchema));
    }
    exports.withParsedProperties = withParsedProperties;
  }
});

// node_modules/voyageai/core/schemas/builders/object-like/index.js
var require_object_like = __commonJS({
  "node_modules/voyageai/core/schemas/builders/object-like/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.withParsedProperties = exports.getObjectLikeUtils = void 0;
    var getObjectLikeUtils_1 = require_getObjectLikeUtils();
    Object.defineProperty(exports, "getObjectLikeUtils", { enumerable: true, get: function() {
      return getObjectLikeUtils_1.getObjectLikeUtils;
    } });
    Object.defineProperty(exports, "withParsedProperties", { enumerable: true, get: function() {
      return getObjectLikeUtils_1.withParsedProperties;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/object/property.js
var require_property = __commonJS({
  "node_modules/voyageai/core/schemas/builders/object/property.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isProperty = exports.property = void 0;
    function property(rawKey, valueSchema) {
      return {
        rawKey,
        valueSchema,
        isProperty: true
      };
    }
    exports.property = property;
    function isProperty(maybeProperty) {
      return maybeProperty.isProperty;
    }
    exports.isProperty = isProperty;
  }
});

// node_modules/voyageai/core/schemas/builders/object/object.js
var require_object = __commonJS({
  "node_modules/voyageai/core/schemas/builders/object/object.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getObjectUtils = exports.object = void 0;
    var Schema_1 = require_Schema();
    var entries_1 = require_entries();
    var filterObject_1 = require_filterObject();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var isPlainObject_1 = require_isPlainObject();
    var keys_1 = require_keys();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var partition_1 = require_partition();
    var object_like_1 = require_object_like();
    var schema_utils_1 = require_schema_utils();
    var property_1 = require_property();
    function object(schemas) {
      const baseSchema = {
        _getRawProperties: () => Object.entries(schemas).map(([parsedKey, propertySchema]) => (0, property_1.isProperty)(propertySchema) ? propertySchema.rawKey : parsedKey),
        _getParsedProperties: () => (0, keys_1.keys)(schemas),
        parse: (raw, opts) => {
          const rawKeyToProperty = {};
          const requiredKeys = [];
          for (const [parsedKey, schemaOrObjectProperty] of (0, entries_1.entries)(schemas)) {
            const rawKey = (0, property_1.isProperty)(schemaOrObjectProperty) ? schemaOrObjectProperty.rawKey : parsedKey;
            const valueSchema = (0, property_1.isProperty)(schemaOrObjectProperty) ? schemaOrObjectProperty.valueSchema : schemaOrObjectProperty;
            const property = {
              rawKey,
              parsedKey,
              valueSchema
            };
            rawKeyToProperty[rawKey] = property;
            if (isSchemaRequired(valueSchema)) {
              requiredKeys.push(rawKey);
            }
          }
          return validateAndTransformObject({
            value: raw,
            requiredKeys,
            getProperty: (rawKey) => {
              const property = rawKeyToProperty[rawKey];
              if (property == null) {
                return void 0;
              }
              return {
                transformedKey: property.parsedKey,
                transform: (propertyValue) => {
                  var _a;
                  return property.valueSchema.parse(propertyValue, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], rawKey] }));
                }
              };
            },
            unrecognizedObjectKeys: opts === null || opts === void 0 ? void 0 : opts.unrecognizedObjectKeys,
            skipValidation: opts === null || opts === void 0 ? void 0 : opts.skipValidation,
            breadcrumbsPrefix: opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix,
            omitUndefined: opts === null || opts === void 0 ? void 0 : opts.omitUndefined
          });
        },
        json: (parsed, opts) => {
          const requiredKeys = [];
          for (const [parsedKey, schemaOrObjectProperty] of (0, entries_1.entries)(schemas)) {
            const valueSchema = (0, property_1.isProperty)(schemaOrObjectProperty) ? schemaOrObjectProperty.valueSchema : schemaOrObjectProperty;
            if (isSchemaRequired(valueSchema)) {
              requiredKeys.push(parsedKey);
            }
          }
          return validateAndTransformObject({
            value: parsed,
            requiredKeys,
            getProperty: (parsedKey) => {
              const property = schemas[parsedKey];
              if (property == null) {
                return void 0;
              }
              if ((0, property_1.isProperty)(property)) {
                return {
                  transformedKey: property.rawKey,
                  transform: (propertyValue) => {
                    var _a;
                    return property.valueSchema.json(propertyValue, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], parsedKey] }));
                  }
                };
              } else {
                return {
                  transformedKey: parsedKey,
                  transform: (propertyValue) => {
                    var _a;
                    return property.json(propertyValue, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], parsedKey] }));
                  }
                };
              }
            },
            unrecognizedObjectKeys: opts === null || opts === void 0 ? void 0 : opts.unrecognizedObjectKeys,
            skipValidation: opts === null || opts === void 0 ? void 0 : opts.skipValidation,
            breadcrumbsPrefix: opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix,
            omitUndefined: opts === null || opts === void 0 ? void 0 : opts.omitUndefined
          });
        },
        getType: () => Schema_1.SchemaType.OBJECT
      };
      return Object.assign(Object.assign(Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema)), (0, object_like_1.getObjectLikeUtils)(baseSchema)), getObjectUtils(baseSchema));
    }
    exports.object = object;
    function validateAndTransformObject({ value, requiredKeys, getProperty, unrecognizedObjectKeys = "fail", skipValidation = false, breadcrumbsPrefix = [] }) {
      if (!(0, isPlainObject_1.isPlainObject)(value)) {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "object")
            }
          ]
        };
      }
      const missingRequiredKeys = new Set(requiredKeys);
      const errors = [];
      const transformed = {};
      for (const [preTransformedKey, preTransformedItemValue] of Object.entries(value)) {
        const property = getProperty(preTransformedKey);
        if (property != null) {
          missingRequiredKeys.delete(preTransformedKey);
          const value2 = property.transform(preTransformedItemValue);
          if (value2.ok) {
            transformed[property.transformedKey] = value2.value;
          } else {
            transformed[preTransformedKey] = preTransformedItemValue;
            errors.push(...value2.errors);
          }
        } else {
          switch (unrecognizedObjectKeys) {
            case "fail":
              errors.push({
                path: [...breadcrumbsPrefix, preTransformedKey],
                message: `Unexpected key "${preTransformedKey}"`
              });
              break;
            case "strip":
              break;
            case "passthrough":
              transformed[preTransformedKey] = preTransformedItemValue;
              break;
          }
        }
      }
      errors.push(...requiredKeys.filter((key) => missingRequiredKeys.has(key)).map((key) => ({
        path: breadcrumbsPrefix,
        message: `Missing required key "${key}"`
      })));
      if (errors.length === 0 || skipValidation) {
        return {
          ok: true,
          value: transformed
        };
      } else {
        return {
          ok: false,
          errors
        };
      }
    }
    function getObjectUtils(schema) {
      return {
        extend: (extension) => {
          const baseSchema = {
            _getParsedProperties: () => [...schema._getParsedProperties(), ...extension._getParsedProperties()],
            _getRawProperties: () => [...schema._getRawProperties(), ...extension._getRawProperties()],
            parse: (raw, opts) => {
              return validateAndTransformExtendedObject({
                extensionKeys: extension._getRawProperties(),
                value: raw,
                transformBase: (rawBase) => schema.parse(rawBase, opts),
                transformExtension: (rawExtension) => extension.parse(rawExtension, opts)
              });
            },
            json: (parsed, opts) => {
              return validateAndTransformExtendedObject({
                extensionKeys: extension._getParsedProperties(),
                value: parsed,
                transformBase: (parsedBase) => schema.json(parsedBase, opts),
                transformExtension: (parsedExtension) => extension.json(parsedExtension, opts)
              });
            },
            getType: () => Schema_1.SchemaType.OBJECT
          };
          return Object.assign(Object.assign(Object.assign(Object.assign({}, baseSchema), (0, schema_utils_1.getSchemaUtils)(baseSchema)), (0, object_like_1.getObjectLikeUtils)(baseSchema)), getObjectUtils(baseSchema));
        }
      };
    }
    exports.getObjectUtils = getObjectUtils;
    function validateAndTransformExtendedObject({ extensionKeys, value, transformBase, transformExtension }) {
      const extensionPropertiesSet = new Set(extensionKeys);
      const [extensionProperties, baseProperties] = (0, partition_1.partition)((0, keys_1.keys)(value), (key) => extensionPropertiesSet.has(key));
      const transformedBase = transformBase((0, filterObject_1.filterObject)(value, baseProperties));
      const transformedExtension = transformExtension((0, filterObject_1.filterObject)(value, extensionProperties));
      if (transformedBase.ok && transformedExtension.ok) {
        return {
          ok: true,
          value: Object.assign(Object.assign({}, transformedBase.value), transformedExtension.value)
        };
      } else {
        return {
          ok: false,
          errors: [
            ...transformedBase.ok ? [] : transformedBase.errors,
            ...transformedExtension.ok ? [] : transformedExtension.errors
          ]
        };
      }
    }
    function isSchemaRequired(schema) {
      return !isSchemaOptional(schema);
    }
    function isSchemaOptional(schema) {
      switch (schema.getType()) {
        case Schema_1.SchemaType.ANY:
        case Schema_1.SchemaType.UNKNOWN:
        case Schema_1.SchemaType.OPTIONAL:
          return true;
        default:
          return false;
      }
    }
  }
});

// node_modules/voyageai/core/schemas/builders/object/objectWithoutOptionalProperties.js
var require_objectWithoutOptionalProperties = __commonJS({
  "node_modules/voyageai/core/schemas/builders/object/objectWithoutOptionalProperties.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.objectWithoutOptionalProperties = void 0;
    var object_1 = require_object();
    function objectWithoutOptionalProperties(schemas) {
      return (0, object_1.object)(schemas);
    }
    exports.objectWithoutOptionalProperties = objectWithoutOptionalProperties;
  }
});

// node_modules/voyageai/core/schemas/builders/object/index.js
var require_object2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/object/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.property = exports.isProperty = exports.objectWithoutOptionalProperties = exports.object = exports.getObjectUtils = void 0;
    var object_1 = require_object();
    Object.defineProperty(exports, "getObjectUtils", { enumerable: true, get: function() {
      return object_1.getObjectUtils;
    } });
    Object.defineProperty(exports, "object", { enumerable: true, get: function() {
      return object_1.object;
    } });
    var objectWithoutOptionalProperties_1 = require_objectWithoutOptionalProperties();
    Object.defineProperty(exports, "objectWithoutOptionalProperties", { enumerable: true, get: function() {
      return objectWithoutOptionalProperties_1.objectWithoutOptionalProperties;
    } });
    var property_1 = require_property();
    Object.defineProperty(exports, "isProperty", { enumerable: true, get: function() {
      return property_1.isProperty;
    } });
    Object.defineProperty(exports, "property", { enumerable: true, get: function() {
      return property_1.property;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/lazy/lazyObject.js
var require_lazyObject = __commonJS({
  "node_modules/voyageai/core/schemas/builders/lazy/lazyObject.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.lazyObject = void 0;
    var object_1 = require_object2();
    var object_like_1 = require_object_like();
    var schema_utils_1 = require_schema_utils();
    var lazy_1 = require_lazy();
    function lazyObject(getter) {
      const baseSchema = Object.assign(Object.assign({}, (0, lazy_1.constructLazyBaseSchema)(getter)), { _getRawProperties: () => (0, lazy_1.getMemoizedSchema)(getter)._getRawProperties(), _getParsedProperties: () => (0, lazy_1.getMemoizedSchema)(getter)._getParsedProperties() });
      return Object.assign(Object.assign(Object.assign(Object.assign({}, baseSchema), (0, schema_utils_1.getSchemaUtils)(baseSchema)), (0, object_like_1.getObjectLikeUtils)(baseSchema)), (0, object_1.getObjectUtils)(baseSchema));
    }
    exports.lazyObject = lazyObject;
  }
});

// node_modules/voyageai/core/schemas/builders/lazy/index.js
var require_lazy2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/lazy/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.lazyObject = exports.lazy = void 0;
    var lazy_1 = require_lazy();
    Object.defineProperty(exports, "lazy", { enumerable: true, get: function() {
      return lazy_1.lazy;
    } });
    var lazyObject_1 = require_lazyObject();
    Object.defineProperty(exports, "lazyObject", { enumerable: true, get: function() {
      return lazyObject_1.lazyObject;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/list/list.js
var require_list = __commonJS({
  "node_modules/voyageai/core/schemas/builders/list/list.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.list = void 0;
    var Schema_1 = require_Schema();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var schema_utils_1 = require_schema_utils();
    function list(schema) {
      const baseSchema = {
        parse: (raw, opts) => validateAndTransformArray(raw, (item, index) => {
          var _a;
          return schema.parse(item, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], `[${index}]`] }));
        }),
        json: (parsed, opts) => validateAndTransformArray(parsed, (item, index) => {
          var _a;
          return schema.json(item, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], `[${index}]`] }));
        }),
        getType: () => Schema_1.SchemaType.LIST
      };
      return Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema));
    }
    exports.list = list;
    function validateAndTransformArray(value, transformItem) {
      if (!Array.isArray(value)) {
        return {
          ok: false,
          errors: [
            {
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "list"),
              path: []
            }
          ]
        };
      }
      const maybeValidItems = value.map((item, index) => transformItem(item, index));
      return maybeValidItems.reduce((acc, item) => {
        if (acc.ok && item.ok) {
          return {
            ok: true,
            value: [...acc.value, item.value]
          };
        }
        const errors = [];
        if (!acc.ok) {
          errors.push(...acc.errors);
        }
        if (!item.ok) {
          errors.push(...item.errors);
        }
        return {
          ok: false,
          errors
        };
      }, { ok: true, value: [] });
    }
  }
});

// node_modules/voyageai/core/schemas/builders/list/index.js
var require_list2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/list/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.list = void 0;
    var list_1 = require_list();
    Object.defineProperty(exports, "list", { enumerable: true, get: function() {
      return list_1.list;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/literals/stringLiteral.js
var require_stringLiteral = __commonJS({
  "node_modules/voyageai/core/schemas/builders/literals/stringLiteral.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.stringLiteral = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    function stringLiteral(literal) {
      const schemaCreator = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.STRING_LITERAL, (value, { breadcrumbsPrefix = [] } = {}) => {
        if (value === literal) {
          return {
            ok: true,
            value: literal
          };
        } else {
          return {
            ok: false,
            errors: [
              {
                path: breadcrumbsPrefix,
                message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, `"${literal}"`)
              }
            ]
          };
        }
      });
      return schemaCreator();
    }
    exports.stringLiteral = stringLiteral;
  }
});

// node_modules/voyageai/core/schemas/builders/literals/booleanLiteral.js
var require_booleanLiteral = __commonJS({
  "node_modules/voyageai/core/schemas/builders/literals/booleanLiteral.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.booleanLiteral = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    function booleanLiteral(literal) {
      const schemaCreator = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.BOOLEAN_LITERAL, (value, { breadcrumbsPrefix = [] } = {}) => {
        if (value === literal) {
          return {
            ok: true,
            value: literal
          };
        } else {
          return {
            ok: false,
            errors: [
              {
                path: breadcrumbsPrefix,
                message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, `${literal.toString()}`)
              }
            ]
          };
        }
      });
      return schemaCreator();
    }
    exports.booleanLiteral = booleanLiteral;
  }
});

// node_modules/voyageai/core/schemas/builders/literals/index.js
var require_literals = __commonJS({
  "node_modules/voyageai/core/schemas/builders/literals/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.booleanLiteral = exports.stringLiteral = void 0;
    var stringLiteral_1 = require_stringLiteral();
    Object.defineProperty(exports, "stringLiteral", { enumerable: true, get: function() {
      return stringLiteral_1.stringLiteral;
    } });
    var booleanLiteral_1 = require_booleanLiteral();
    Object.defineProperty(exports, "booleanLiteral", { enumerable: true, get: function() {
      return booleanLiteral_1.booleanLiteral;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/primitives/any.js
var require_any = __commonJS({
  "node_modules/voyageai/core/schemas/builders/primitives/any.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.any = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    exports.any = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.ANY, (value) => ({ ok: true, value }));
  }
});

// node_modules/voyageai/core/schemas/builders/primitives/boolean.js
var require_boolean = __commonJS({
  "node_modules/voyageai/core/schemas/builders/primitives/boolean.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.boolean = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    exports.boolean = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.BOOLEAN, (value, { breadcrumbsPrefix = [] } = {}) => {
      if (typeof value === "boolean") {
        return {
          ok: true,
          value
        };
      } else {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "boolean")
            }
          ]
        };
      }
    });
  }
});

// node_modules/voyageai/core/schemas/builders/primitives/number.js
var require_number = __commonJS({
  "node_modules/voyageai/core/schemas/builders/primitives/number.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.number = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    exports.number = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.NUMBER, (value, { breadcrumbsPrefix = [] } = {}) => {
      if (typeof value === "number") {
        return {
          ok: true,
          value
        };
      } else {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "number")
            }
          ]
        };
      }
    });
  }
});

// node_modules/voyageai/core/schemas/builders/primitives/string.js
var require_string = __commonJS({
  "node_modules/voyageai/core/schemas/builders/primitives/string.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.string = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    exports.string = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.STRING, (value, { breadcrumbsPrefix = [] } = {}) => {
      if (typeof value === "string") {
        return {
          ok: true,
          value
        };
      } else {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "string")
            }
          ]
        };
      }
    });
  }
});

// node_modules/voyageai/core/schemas/builders/primitives/unknown.js
var require_unknown = __commonJS({
  "node_modules/voyageai/core/schemas/builders/primitives/unknown.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.unknown = void 0;
    var Schema_1 = require_Schema();
    var createIdentitySchemaCreator_1 = require_createIdentitySchemaCreator();
    exports.unknown = (0, createIdentitySchemaCreator_1.createIdentitySchemaCreator)(Schema_1.SchemaType.UNKNOWN, (value) => ({ ok: true, value }));
  }
});

// node_modules/voyageai/core/schemas/builders/primitives/index.js
var require_primitives = __commonJS({
  "node_modules/voyageai/core/schemas/builders/primitives/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.unknown = exports.string = exports.number = exports.boolean = exports.any = void 0;
    var any_1 = require_any();
    Object.defineProperty(exports, "any", { enumerable: true, get: function() {
      return any_1.any;
    } });
    var boolean_1 = require_boolean();
    Object.defineProperty(exports, "boolean", { enumerable: true, get: function() {
      return boolean_1.boolean;
    } });
    var number_1 = require_number();
    Object.defineProperty(exports, "number", { enumerable: true, get: function() {
      return number_1.number;
    } });
    var string_1 = require_string();
    Object.defineProperty(exports, "string", { enumerable: true, get: function() {
      return string_1.string;
    } });
    var unknown_1 = require_unknown();
    Object.defineProperty(exports, "unknown", { enumerable: true, get: function() {
      return unknown_1.unknown;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/record/record.js
var require_record = __commonJS({
  "node_modules/voyageai/core/schemas/builders/record/record.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.record = void 0;
    var Schema_1 = require_Schema();
    var entries_1 = require_entries();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var isPlainObject_1 = require_isPlainObject();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var schema_utils_1 = require_schema_utils();
    function record(keySchema, valueSchema) {
      const baseSchema = {
        parse: (raw, opts) => {
          return validateAndTransformRecord({
            value: raw,
            isKeyNumeric: keySchema.getType() === Schema_1.SchemaType.NUMBER,
            transformKey: (key) => {
              var _a;
              return keySchema.parse(key, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], `${key} (key)`] }));
            },
            transformValue: (value, key) => {
              var _a;
              return valueSchema.parse(value, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], `${key}`] }));
            },
            breadcrumbsPrefix: opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix
          });
        },
        json: (parsed, opts) => {
          return validateAndTransformRecord({
            value: parsed,
            isKeyNumeric: keySchema.getType() === Schema_1.SchemaType.NUMBER,
            transformKey: (key) => {
              var _a;
              return keySchema.json(key, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], `${key} (key)`] }));
            },
            transformValue: (value, key) => {
              var _a;
              return valueSchema.json(value, Object.assign(Object.assign({}, opts), { breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], `${key}`] }));
            },
            breadcrumbsPrefix: opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix
          });
        },
        getType: () => Schema_1.SchemaType.RECORD
      };
      return Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema));
    }
    exports.record = record;
    function validateAndTransformRecord({ value, isKeyNumeric, transformKey, transformValue, breadcrumbsPrefix = [] }) {
      if (!(0, isPlainObject_1.isPlainObject)(value)) {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "object")
            }
          ]
        };
      }
      return (0, entries_1.entries)(value).reduce((accPromise, [stringKey, value2]) => {
        if (value2 == null) {
          return accPromise;
        }
        const acc = accPromise;
        let key = stringKey;
        if (isKeyNumeric) {
          const numberKey = stringKey.length > 0 ? Number(stringKey) : NaN;
          if (!isNaN(numberKey)) {
            key = numberKey;
          }
        }
        const transformedKey = transformKey(key);
        const transformedValue = transformValue(value2, key);
        if (acc.ok && transformedKey.ok && transformedValue.ok) {
          return {
            ok: true,
            value: Object.assign(Object.assign({}, acc.value), { [transformedKey.value]: transformedValue.value })
          };
        }
        const errors = [];
        if (!acc.ok) {
          errors.push(...acc.errors);
        }
        if (!transformedKey.ok) {
          errors.push(...transformedKey.errors);
        }
        if (!transformedValue.ok) {
          errors.push(...transformedValue.errors);
        }
        return {
          ok: false,
          errors
        };
      }, { ok: true, value: {} });
    }
  }
});

// node_modules/voyageai/core/schemas/builders/record/index.js
var require_record2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/record/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.record = void 0;
    var record_1 = require_record();
    Object.defineProperty(exports, "record", { enumerable: true, get: function() {
      return record_1.record;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/set/set.js
var require_set = __commonJS({
  "node_modules/voyageai/core/schemas/builders/set/set.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.set = void 0;
    var Schema_1 = require_Schema();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var list_1 = require_list2();
    var schema_utils_1 = require_schema_utils();
    function set(schema) {
      const listSchema = (0, list_1.list)(schema);
      const baseSchema = {
        parse: (raw, opts) => {
          const parsedList = listSchema.parse(raw, opts);
          if (parsedList.ok) {
            return {
              ok: true,
              value: new Set(parsedList.value)
            };
          } else {
            return parsedList;
          }
        },
        json: (parsed, opts) => {
          var _a;
          if (!(parsed instanceof Set)) {
            return {
              ok: false,
              errors: [
                {
                  path: (_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [],
                  message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(parsed, "Set")
                }
              ]
            };
          }
          const jsonList = listSchema.json([...parsed], opts);
          return jsonList;
        },
        getType: () => Schema_1.SchemaType.SET
      };
      return Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema));
    }
    exports.set = set;
  }
});

// node_modules/voyageai/core/schemas/builders/set/index.js
var require_set2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/set/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.set = void 0;
    var set_1 = require_set();
    Object.defineProperty(exports, "set", { enumerable: true, get: function() {
      return set_1.set;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/undiscriminated-union/undiscriminatedUnion.js
var require_undiscriminatedUnion = __commonJS({
  "node_modules/voyageai/core/schemas/builders/undiscriminated-union/undiscriminatedUnion.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.undiscriminatedUnion = void 0;
    var Schema_1 = require_Schema();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var schema_utils_1 = require_schema_utils();
    function undiscriminatedUnion(schemas) {
      const baseSchema = {
        parse: (raw, opts) => {
          return validateAndTransformUndiscriminatedUnion((schema, opts2) => schema.parse(raw, opts2), schemas, opts);
        },
        json: (parsed, opts) => {
          return validateAndTransformUndiscriminatedUnion((schema, opts2) => schema.json(parsed, opts2), schemas, opts);
        },
        getType: () => Schema_1.SchemaType.UNDISCRIMINATED_UNION
      };
      return Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema));
    }
    exports.undiscriminatedUnion = undiscriminatedUnion;
    function validateAndTransformUndiscriminatedUnion(transform, schemas, opts) {
      const errors = [];
      for (const [index, schema] of schemas.entries()) {
        const transformed = transform(schema, Object.assign(Object.assign({}, opts), { skipValidation: false }));
        if (transformed.ok) {
          return transformed;
        } else {
          for (const error of transformed.errors) {
            errors.push({
              path: error.path,
              message: `[Variant ${index}] ${error.message}`
            });
          }
        }
      }
      return {
        ok: false,
        errors
      };
    }
  }
});

// node_modules/voyageai/core/schemas/builders/undiscriminated-union/index.js
var require_undiscriminated_union = __commonJS({
  "node_modules/voyageai/core/schemas/builders/undiscriminated-union/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.undiscriminatedUnion = void 0;
    var undiscriminatedUnion_1 = require_undiscriminatedUnion();
    Object.defineProperty(exports, "undiscriminatedUnion", { enumerable: true, get: function() {
      return undiscriminatedUnion_1.undiscriminatedUnion;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/union/discriminant.js
var require_discriminant = __commonJS({
  "node_modules/voyageai/core/schemas/builders/union/discriminant.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.discriminant = void 0;
    function discriminant(parsedDiscriminant, rawDiscriminant) {
      return {
        parsedDiscriminant,
        rawDiscriminant
      };
    }
    exports.discriminant = discriminant;
  }
});

// node_modules/voyageai/core/schemas/builders/union/union.js
var require_union = __commonJS({
  "node_modules/voyageai/core/schemas/builders/union/union.js"(exports) {
    "use strict";
    var __rest = exports && exports.__rest || function(s, e) {
      var t = {};
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
      if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
            t[p[i]] = s[p[i]];
        }
      return t;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.union = void 0;
    var Schema_1 = require_Schema();
    var getErrorMessageForIncorrectType_1 = require_getErrorMessageForIncorrectType();
    var isPlainObject_1 = require_isPlainObject();
    var keys_1 = require_keys();
    var maybeSkipValidation_1 = require_maybeSkipValidation();
    var enum_1 = require_enum2();
    var object_like_1 = require_object_like();
    var schema_utils_1 = require_schema_utils();
    function union(discriminant, union2) {
      const rawDiscriminant = typeof discriminant === "string" ? discriminant : discriminant.rawDiscriminant;
      const parsedDiscriminant = typeof discriminant === "string" ? discriminant : discriminant.parsedDiscriminant;
      const discriminantValueSchema = (0, enum_1.enum_)((0, keys_1.keys)(union2));
      const baseSchema = {
        parse: (raw, opts) => {
          return transformAndValidateUnion({
            value: raw,
            discriminant: rawDiscriminant,
            transformedDiscriminant: parsedDiscriminant,
            transformDiscriminantValue: (discriminantValue) => {
              var _a;
              return discriminantValueSchema.parse(discriminantValue, {
                allowUnrecognizedEnumValues: opts === null || opts === void 0 ? void 0 : opts.allowUnrecognizedUnionMembers,
                breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], rawDiscriminant]
              });
            },
            getAdditionalPropertiesSchema: (discriminantValue) => union2[discriminantValue],
            allowUnrecognizedUnionMembers: opts === null || opts === void 0 ? void 0 : opts.allowUnrecognizedUnionMembers,
            transformAdditionalProperties: (additionalProperties, additionalPropertiesSchema) => additionalPropertiesSchema.parse(additionalProperties, opts),
            breadcrumbsPrefix: opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix
          });
        },
        json: (parsed, opts) => {
          return transformAndValidateUnion({
            value: parsed,
            discriminant: parsedDiscriminant,
            transformedDiscriminant: rawDiscriminant,
            transformDiscriminantValue: (discriminantValue) => {
              var _a;
              return discriminantValueSchema.json(discriminantValue, {
                allowUnrecognizedEnumValues: opts === null || opts === void 0 ? void 0 : opts.allowUnrecognizedUnionMembers,
                breadcrumbsPrefix: [...(_a = opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix) !== null && _a !== void 0 ? _a : [], parsedDiscriminant]
              });
            },
            getAdditionalPropertiesSchema: (discriminantValue) => union2[discriminantValue],
            allowUnrecognizedUnionMembers: opts === null || opts === void 0 ? void 0 : opts.allowUnrecognizedUnionMembers,
            transformAdditionalProperties: (additionalProperties, additionalPropertiesSchema) => additionalPropertiesSchema.json(additionalProperties, opts),
            breadcrumbsPrefix: opts === null || opts === void 0 ? void 0 : opts.breadcrumbsPrefix
          });
        },
        getType: () => Schema_1.SchemaType.UNION
      };
      return Object.assign(Object.assign(Object.assign({}, (0, maybeSkipValidation_1.maybeSkipValidation)(baseSchema)), (0, schema_utils_1.getSchemaUtils)(baseSchema)), (0, object_like_1.getObjectLikeUtils)(baseSchema));
    }
    exports.union = union;
    function transformAndValidateUnion({ value, discriminant, transformedDiscriminant, transformDiscriminantValue, getAdditionalPropertiesSchema, allowUnrecognizedUnionMembers = false, transformAdditionalProperties, breadcrumbsPrefix = [] }) {
      if (!(0, isPlainObject_1.isPlainObject)(value)) {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: (0, getErrorMessageForIncorrectType_1.getErrorMessageForIncorrectType)(value, "object")
            }
          ]
        };
      }
      const _a = value, _b = discriminant, discriminantValue = _a[_b], additionalProperties = __rest(_a, [typeof _b === "symbol" ? _b : _b + ""]);
      if (discriminantValue == null) {
        return {
          ok: false,
          errors: [
            {
              path: breadcrumbsPrefix,
              message: `Missing discriminant ("${discriminant}")`
            }
          ]
        };
      }
      const transformedDiscriminantValue = transformDiscriminantValue(discriminantValue);
      if (!transformedDiscriminantValue.ok) {
        return {
          ok: false,
          errors: transformedDiscriminantValue.errors
        };
      }
      const additionalPropertiesSchema = getAdditionalPropertiesSchema(transformedDiscriminantValue.value);
      if (additionalPropertiesSchema == null) {
        if (allowUnrecognizedUnionMembers) {
          return {
            ok: true,
            value: Object.assign({ [transformedDiscriminant]: transformedDiscriminantValue.value }, additionalProperties)
          };
        } else {
          return {
            ok: false,
            errors: [
              {
                path: [...breadcrumbsPrefix, discriminant],
                message: "Unexpected discriminant value"
              }
            ]
          };
        }
      }
      const transformedAdditionalProperties = transformAdditionalProperties(additionalProperties, additionalPropertiesSchema);
      if (!transformedAdditionalProperties.ok) {
        return transformedAdditionalProperties;
      }
      return {
        ok: true,
        value: Object.assign({ [transformedDiscriminant]: discriminantValue }, transformedAdditionalProperties.value)
      };
    }
  }
});

// node_modules/voyageai/core/schemas/builders/union/index.js
var require_union2 = __commonJS({
  "node_modules/voyageai/core/schemas/builders/union/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.union = exports.discriminant = void 0;
    var discriminant_1 = require_discriminant();
    Object.defineProperty(exports, "discriminant", { enumerable: true, get: function() {
      return discriminant_1.discriminant;
    } });
    var union_1 = require_union();
    Object.defineProperty(exports, "union", { enumerable: true, get: function() {
      return union_1.union;
    } });
  }
});

// node_modules/voyageai/core/schemas/builders/index.js
var require_builders = __commonJS({
  "node_modules/voyageai/core/schemas/builders/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_date2(), exports);
    __exportStar(require_enum2(), exports);
    __exportStar(require_lazy2(), exports);
    __exportStar(require_list2(), exports);
    __exportStar(require_literals(), exports);
    __exportStar(require_object2(), exports);
    __exportStar(require_object_like(), exports);
    __exportStar(require_primitives(), exports);
    __exportStar(require_record2(), exports);
    __exportStar(require_schema_utils(), exports);
    __exportStar(require_set2(), exports);
    __exportStar(require_undiscriminated_union(), exports);
    __exportStar(require_union2(), exports);
  }
});

// node_modules/voyageai/core/schemas/index.js
var require_schemas = __commonJS({
  "node_modules/voyageai/core/schemas/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_builders(), exports);
  }
});

// node_modules/voyageai/core/index.js
var require_core = __commonJS({
  "node_modules/voyageai/core/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.serialization = void 0;
    __exportStar(require_fetcher(), exports);
    __exportStar(require_auth(), exports);
    __exportStar(require_runtime2(), exports);
    exports.serialization = __importStar(require_schemas());
  }
});

// node_modules/voyageai/serialization/types/EmbedRequestInput.js
var require_EmbedRequestInput2 = __commonJS({
  "node_modules/voyageai/serialization/types/EmbedRequestInput.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedRequestInput = void 0;
    var core = __importStar(require_core());
    exports.EmbedRequestInput = core.serialization.undiscriminatedUnion([
      core.serialization.string(),
      core.serialization.list(core.serialization.string())
    ]);
  }
});

// node_modules/voyageai/serialization/types/EmbedRequestInputType.js
var require_EmbedRequestInputType2 = __commonJS({
  "node_modules/voyageai/serialization/types/EmbedRequestInputType.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedRequestInputType = void 0;
    var core = __importStar(require_core());
    exports.EmbedRequestInputType = core.serialization.enum_(["query", "document"]);
  }
});

// node_modules/voyageai/serialization/types/EmbedResponseDataItem.js
var require_EmbedResponseDataItem2 = __commonJS({
  "node_modules/voyageai/serialization/types/EmbedResponseDataItem.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedResponseDataItem = void 0;
    var core = __importStar(require_core());
    exports.EmbedResponseDataItem = core.serialization.object({
      object: core.serialization.string().optional(),
      embedding: core.serialization.list(core.serialization.number()).optional(),
      index: core.serialization.number().optional()
    });
  }
});

// node_modules/voyageai/serialization/types/EmbedResponseUsage.js
var require_EmbedResponseUsage2 = __commonJS({
  "node_modules/voyageai/serialization/types/EmbedResponseUsage.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedResponseUsage = void 0;
    var core = __importStar(require_core());
    exports.EmbedResponseUsage = core.serialization.object({
      totalTokens: core.serialization.property("total_tokens", core.serialization.number().optional())
    });
  }
});

// node_modules/voyageai/serialization/types/EmbedResponse.js
var require_EmbedResponse2 = __commonJS({
  "node_modules/voyageai/serialization/types/EmbedResponse.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedResponse = void 0;
    var core = __importStar(require_core());
    var EmbedResponseDataItem_1 = require_EmbedResponseDataItem2();
    var EmbedResponseUsage_1 = require_EmbedResponseUsage2();
    exports.EmbedResponse = core.serialization.object({
      object: core.serialization.string().optional(),
      data: core.serialization.list(EmbedResponseDataItem_1.EmbedResponseDataItem).optional(),
      model: core.serialization.string().optional(),
      usage: EmbedResponseUsage_1.EmbedResponseUsage.optional()
    });
  }
});

// node_modules/voyageai/serialization/types/RerankResponseDataItem.js
var require_RerankResponseDataItem2 = __commonJS({
  "node_modules/voyageai/serialization/types/RerankResponseDataItem.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.RerankResponseDataItem = void 0;
    var core = __importStar(require_core());
    exports.RerankResponseDataItem = core.serialization.object({
      index: core.serialization.number().optional(),
      relevanceScore: core.serialization.property("relevance_score", core.serialization.number().optional()),
      document: core.serialization.string().optional()
    });
  }
});

// node_modules/voyageai/serialization/types/RerankResponseUsage.js
var require_RerankResponseUsage2 = __commonJS({
  "node_modules/voyageai/serialization/types/RerankResponseUsage.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.RerankResponseUsage = void 0;
    var core = __importStar(require_core());
    exports.RerankResponseUsage = core.serialization.object({
      totalTokens: core.serialization.property("total_tokens", core.serialization.number().optional())
    });
  }
});

// node_modules/voyageai/serialization/types/RerankResponse.js
var require_RerankResponse2 = __commonJS({
  "node_modules/voyageai/serialization/types/RerankResponse.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.RerankResponse = void 0;
    var core = __importStar(require_core());
    var RerankResponseDataItem_1 = require_RerankResponseDataItem2();
    var RerankResponseUsage_1 = require_RerankResponseUsage2();
    exports.RerankResponse = core.serialization.object({
      object: core.serialization.string().optional(),
      data: core.serialization.list(RerankResponseDataItem_1.RerankResponseDataItem).optional(),
      model: core.serialization.string().optional(),
      usage: RerankResponseUsage_1.RerankResponseUsage.optional()
    });
  }
});

// node_modules/voyageai/serialization/types/MultimodalEmbedRequestInputsItemContentItem.js
var require_MultimodalEmbedRequestInputsItemContentItem2 = __commonJS({
  "node_modules/voyageai/serialization/types/MultimodalEmbedRequestInputsItemContentItem.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedRequestInputsItemContentItem = void 0;
    var core = __importStar(require_core());
    exports.MultimodalEmbedRequestInputsItemContentItem = core.serialization.object({
      type: core.serialization.string(),
      text: core.serialization.string().optional(),
      imageBase64: core.serialization.property("image_base64", core.serialization.string().optional()),
      imageUrl: core.serialization.property("image_url", core.serialization.string().optional())
    });
  }
});

// node_modules/voyageai/serialization/types/MultimodalEmbedRequestInputsItem.js
var require_MultimodalEmbedRequestInputsItem2 = __commonJS({
  "node_modules/voyageai/serialization/types/MultimodalEmbedRequestInputsItem.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedRequestInputsItem = void 0;
    var core = __importStar(require_core());
    var MultimodalEmbedRequestInputsItemContentItem_1 = require_MultimodalEmbedRequestInputsItemContentItem2();
    exports.MultimodalEmbedRequestInputsItem = core.serialization.object({
      content: core.serialization.list(MultimodalEmbedRequestInputsItemContentItem_1.MultimodalEmbedRequestInputsItemContentItem).optional()
    });
  }
});

// node_modules/voyageai/serialization/types/MultimodalEmbedRequestInputType.js
var require_MultimodalEmbedRequestInputType2 = __commonJS({
  "node_modules/voyageai/serialization/types/MultimodalEmbedRequestInputType.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedRequestInputType = void 0;
    var core = __importStar(require_core());
    exports.MultimodalEmbedRequestInputType = core.serialization.enum_(["query", "document"]);
  }
});

// node_modules/voyageai/serialization/types/MultimodalEmbedResponseDataItem.js
var require_MultimodalEmbedResponseDataItem2 = __commonJS({
  "node_modules/voyageai/serialization/types/MultimodalEmbedResponseDataItem.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedResponseDataItem = void 0;
    var core = __importStar(require_core());
    exports.MultimodalEmbedResponseDataItem = core.serialization.object({
      object: core.serialization.string().optional(),
      embedding: core.serialization.list(core.serialization.number()).optional(),
      index: core.serialization.number().optional()
    });
  }
});

// node_modules/voyageai/serialization/types/MultimodalEmbedResponseUsage.js
var require_MultimodalEmbedResponseUsage2 = __commonJS({
  "node_modules/voyageai/serialization/types/MultimodalEmbedResponseUsage.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedResponseUsage = void 0;
    var core = __importStar(require_core());
    exports.MultimodalEmbedResponseUsage = core.serialization.object({
      totalTokens: core.serialization.property("total_tokens", core.serialization.number().optional())
    });
  }
});

// node_modules/voyageai/serialization/types/MultimodalEmbedResponse.js
var require_MultimodalEmbedResponse2 = __commonJS({
  "node_modules/voyageai/serialization/types/MultimodalEmbedResponse.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedResponse = void 0;
    var core = __importStar(require_core());
    var MultimodalEmbedResponseDataItem_1 = require_MultimodalEmbedResponseDataItem2();
    var MultimodalEmbedResponseUsage_1 = require_MultimodalEmbedResponseUsage2();
    exports.MultimodalEmbedResponse = core.serialization.object({
      object: core.serialization.string().optional(),
      data: core.serialization.list(MultimodalEmbedResponseDataItem_1.MultimodalEmbedResponseDataItem).optional(),
      model: core.serialization.string().optional(),
      usage: MultimodalEmbedResponseUsage_1.MultimodalEmbedResponseUsage.optional()
    });
  }
});

// node_modules/voyageai/serialization/types/index.js
var require_types2 = __commonJS({
  "node_modules/voyageai/serialization/types/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_EmbedRequestInput2(), exports);
    __exportStar(require_EmbedRequestInputType2(), exports);
    __exportStar(require_EmbedResponseDataItem2(), exports);
    __exportStar(require_EmbedResponseUsage2(), exports);
    __exportStar(require_EmbedResponse2(), exports);
    __exportStar(require_RerankResponseDataItem2(), exports);
    __exportStar(require_RerankResponseUsage2(), exports);
    __exportStar(require_RerankResponse2(), exports);
    __exportStar(require_MultimodalEmbedRequestInputsItemContentItem2(), exports);
    __exportStar(require_MultimodalEmbedRequestInputsItem2(), exports);
    __exportStar(require_MultimodalEmbedRequestInputType2(), exports);
    __exportStar(require_MultimodalEmbedResponseDataItem2(), exports);
    __exportStar(require_MultimodalEmbedResponseUsage2(), exports);
    __exportStar(require_MultimodalEmbedResponse2(), exports);
  }
});

// node_modules/voyageai/serialization/client/requests/EmbedRequest.js
var require_EmbedRequest = __commonJS({
  "node_modules/voyageai/serialization/client/requests/EmbedRequest.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.EmbedRequest = void 0;
    var core = __importStar(require_core());
    var EmbedRequestInput_1 = require_EmbedRequestInput2();
    var EmbedRequestInputType_1 = require_EmbedRequestInputType2();
    exports.EmbedRequest = core.serialization.object({
      input: EmbedRequestInput_1.EmbedRequestInput,
      model: core.serialization.string(),
      inputType: core.serialization.property("input_type", EmbedRequestInputType_1.EmbedRequestInputType.optional()),
      truncation: core.serialization.boolean().optional(),
      encodingFormat: core.serialization.property("encoding_format", core.serialization.stringLiteral("base64").optional())
    });
  }
});

// node_modules/voyageai/serialization/client/requests/RerankRequest.js
var require_RerankRequest = __commonJS({
  "node_modules/voyageai/serialization/client/requests/RerankRequest.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.RerankRequest = void 0;
    var core = __importStar(require_core());
    exports.RerankRequest = core.serialization.object({
      query: core.serialization.string(),
      documents: core.serialization.list(core.serialization.string()),
      model: core.serialization.string(),
      topK: core.serialization.property("top_k", core.serialization.number().optional()),
      returnDocuments: core.serialization.property("return_documents", core.serialization.boolean().optional()),
      truncation: core.serialization.boolean().optional()
    });
  }
});

// node_modules/voyageai/serialization/client/requests/MultimodalEmbedRequest.js
var require_MultimodalEmbedRequest = __commonJS({
  "node_modules/voyageai/serialization/client/requests/MultimodalEmbedRequest.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedRequest = void 0;
    var core = __importStar(require_core());
    var MultimodalEmbedRequestInputsItem_1 = require_MultimodalEmbedRequestInputsItem2();
    var MultimodalEmbedRequestInputType_1 = require_MultimodalEmbedRequestInputType2();
    exports.MultimodalEmbedRequest = core.serialization.object({
      inputs: core.serialization.list(MultimodalEmbedRequestInputsItem_1.MultimodalEmbedRequestInputsItem),
      model: core.serialization.string(),
      inputType: core.serialization.property("input_type", MultimodalEmbedRequestInputType_1.MultimodalEmbedRequestInputType.optional()),
      truncation: core.serialization.boolean().optional(),
      encodingFormat: core.serialization.property("encoding_format", core.serialization.stringLiteral("base64").optional())
    });
  }
});

// node_modules/voyageai/serialization/client/requests/index.js
var require_requests2 = __commonJS({
  "node_modules/voyageai/serialization/client/requests/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.MultimodalEmbedRequest = exports.RerankRequest = exports.EmbedRequest = void 0;
    var EmbedRequest_1 = require_EmbedRequest();
    Object.defineProperty(exports, "EmbedRequest", { enumerable: true, get: function() {
      return EmbedRequest_1.EmbedRequest;
    } });
    var RerankRequest_1 = require_RerankRequest();
    Object.defineProperty(exports, "RerankRequest", { enumerable: true, get: function() {
      return RerankRequest_1.RerankRequest;
    } });
    var MultimodalEmbedRequest_1 = require_MultimodalEmbedRequest();
    Object.defineProperty(exports, "MultimodalEmbedRequest", { enumerable: true, get: function() {
      return MultimodalEmbedRequest_1.MultimodalEmbedRequest;
    } });
  }
});

// node_modules/voyageai/serialization/client/index.js
var require_client2 = __commonJS({
  "node_modules/voyageai/serialization/client/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_requests2(), exports);
  }
});

// node_modules/voyageai/serialization/index.js
var require_serialization = __commonJS({
  "node_modules/voyageai/serialization/index.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __exportStar = exports && exports.__exportStar || function(m, exports2) {
      for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports2, p)) __createBinding(exports2, m, p);
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    __exportStar(require_types2(), exports);
    __exportStar(require_client2(), exports);
  }
});

// node_modules/voyageai/errors/VoyageAIError.js
var require_VoyageAIError = __commonJS({
  "node_modules/voyageai/errors/VoyageAIError.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.VoyageAIError = void 0;
    var VoyageAIError = class _VoyageAIError extends Error {
      constructor({ message, statusCode, body }) {
        super(buildMessage({ message, statusCode, body }));
        Object.setPrototypeOf(this, _VoyageAIError.prototype);
        if (statusCode != null) {
          this.statusCode = statusCode;
        }
        if (body !== void 0) {
          this.body = body;
        }
      }
    };
    exports.VoyageAIError = VoyageAIError;
    function buildMessage({ message, statusCode, body }) {
      let lines = [];
      if (message != null) {
        lines.push(message);
      }
      if (statusCode != null) {
        lines.push(`Status code: ${statusCode.toString()}`);
      }
      if (body != null) {
        lines.push(`Body: ${JSON.stringify(body, void 0, 2)}`);
      }
      return lines.join("\n");
    }
  }
});

// node_modules/voyageai/errors/VoyageAITimeoutError.js
var require_VoyageAITimeoutError = __commonJS({
  "node_modules/voyageai/errors/VoyageAITimeoutError.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.VoyageAITimeoutError = void 0;
    var VoyageAITimeoutError = class _VoyageAITimeoutError extends Error {
      constructor() {
        super("Timeout");
        Object.setPrototypeOf(this, _VoyageAITimeoutError.prototype);
      }
    };
    exports.VoyageAITimeoutError = VoyageAITimeoutError;
  }
});

// node_modules/voyageai/errors/index.js
var require_errors = __commonJS({
  "node_modules/voyageai/errors/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.VoyageAITimeoutError = exports.VoyageAIError = void 0;
    var VoyageAIError_1 = require_VoyageAIError();
    Object.defineProperty(exports, "VoyageAIError", { enumerable: true, get: function() {
      return VoyageAIError_1.VoyageAIError;
    } });
    var VoyageAITimeoutError_1 = require_VoyageAITimeoutError();
    Object.defineProperty(exports, "VoyageAITimeoutError", { enumerable: true, get: function() {
      return VoyageAITimeoutError_1.VoyageAITimeoutError;
    } });
  }
});

// node_modules/voyageai/Client.js
var require_Client = __commonJS({
  "node_modules/voyageai/Client.js"(exports) {
    "use strict";
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    var __awaiter = exports && exports.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __importDefault = exports && exports.__importDefault || function(mod) {
      return mod && mod.__esModule ? mod : { "default": mod };
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.VoyageAIClient = void 0;
    var environments = __importStar(require_environments());
    var core = __importStar(require_core());
    var serializers = __importStar(require_serialization());
    var url_join_1 = __importDefault(require_url_join());
    var errors = __importStar(require_errors());
    var VoyageAIClient = class {
      constructor(_options = {}) {
        this._options = _options;
      }
      /**
       * Voyage embedding endpoint receives as input a string (or a list of strings) and other arguments such as the preferred model name, and returns a response containing a list of embeddings.
       *
       * @param {VoyageAI.EmbedRequest} request
       * @param {VoyageAIClient.RequestOptions} requestOptions - Request-specific configuration.
       *
       * @example
       *     await client.embed({
       *         input: "input",
       *         model: "model"
       *     })
       */
      embed(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
          const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
            url: (0, url_join_1.default)((_b = yield core.Supplier.get(this._options.environment)) !== null && _b !== void 0 ? _b : environments.VoyageAIEnvironment.Default, "embeddings"),
            method: "POST",
            headers: {
              Authorization: yield this._getAuthorizationHeader(),
              "X-Fern-Language": "JavaScript",
              "X-Fern-SDK-Name": "voyageai",
              "X-Fern-SDK-Version": "0.0.3",
              "User-Agent": "voyageai/0.0.3",
              "X-Fern-Runtime": core.RUNTIME.type,
              "X-Fern-Runtime-Version": core.RUNTIME.version
            },
            contentType: "application/json",
            requestType: "json",
            body: serializers.EmbedRequest.jsonOrThrow(request, { unrecognizedObjectKeys: "strip" }),
            timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1e3 : 6e4,
            maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
            abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal
          });
          if (_response.ok) {
            return serializers.EmbedResponse.parseOrThrow(_response.body, {
              unrecognizedObjectKeys: "passthrough",
              allowUnrecognizedUnionMembers: true,
              allowUnrecognizedEnumValues: true,
              skipValidation: true,
              breadcrumbsPrefix: ["response"]
            });
          }
          if (_response.error.reason === "status-code") {
            throw new errors.VoyageAIError({
              statusCode: _response.error.statusCode,
              body: _response.error.body
            });
          }
          switch (_response.error.reason) {
            case "non-json":
              throw new errors.VoyageAIError({
                statusCode: _response.error.statusCode,
                body: _response.error.rawBody
              });
            case "timeout":
              throw new errors.VoyageAITimeoutError();
            case "unknown":
              throw new errors.VoyageAIError({
                message: _response.error.errorMessage
              });
          }
        });
      }
      /**
       * Voyage reranker endpoint receives as input a query, a list of documents, and other arguments such as the model name, and returns a response containing the reranking results.
       *
       * @param {VoyageAI.RerankRequest} request
       * @param {VoyageAIClient.RequestOptions} requestOptions - Request-specific configuration.
       *
       * @example
       *     await client.rerank({
       *         query: "query",
       *         documents: ["documents"],
       *         model: "model"
       *     })
       */
      rerank(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
          const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
            url: (0, url_join_1.default)((_b = yield core.Supplier.get(this._options.environment)) !== null && _b !== void 0 ? _b : environments.VoyageAIEnvironment.Default, "rerank"),
            method: "POST",
            headers: {
              Authorization: yield this._getAuthorizationHeader(),
              "X-Fern-Language": "JavaScript",
              "X-Fern-SDK-Name": "voyageai",
              "X-Fern-SDK-Version": "0.0.3",
              "User-Agent": "voyageai/0.0.3",
              "X-Fern-Runtime": core.RUNTIME.type,
              "X-Fern-Runtime-Version": core.RUNTIME.version
            },
            contentType: "application/json",
            requestType: "json",
            body: serializers.RerankRequest.jsonOrThrow(request, { unrecognizedObjectKeys: "strip" }),
            timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1e3 : 6e4,
            maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
            abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal
          });
          if (_response.ok) {
            return serializers.RerankResponse.parseOrThrow(_response.body, {
              unrecognizedObjectKeys: "passthrough",
              allowUnrecognizedUnionMembers: true,
              allowUnrecognizedEnumValues: true,
              skipValidation: true,
              breadcrumbsPrefix: ["response"]
            });
          }
          if (_response.error.reason === "status-code") {
            throw new errors.VoyageAIError({
              statusCode: _response.error.statusCode,
              body: _response.error.body
            });
          }
          switch (_response.error.reason) {
            case "non-json":
              throw new errors.VoyageAIError({
                statusCode: _response.error.statusCode,
                body: _response.error.rawBody
              });
            case "timeout":
              throw new errors.VoyageAITimeoutError();
            case "unknown":
              throw new errors.VoyageAIError({
                message: _response.error.errorMessage
              });
          }
        });
      }
      /**
       * The Voyage multimodal embedding endpoint returns vector representations for a given list of multimodal inputs consisting of text, images, or an interleaving of both modalities.
       *
       * @param {VoyageAI.MultimodalEmbedRequest} request
       * @param {VoyageAIClient.RequestOptions} requestOptions - Request-specific configuration.
       *
       * @example
       *     await client.multimodalEmbed({
       *         inputs: [{}],
       *         model: "model"
       *     })
       */
      multimodalEmbed(request, requestOptions) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
          const _response = yield ((_a = this._options.fetcher) !== null && _a !== void 0 ? _a : core.fetcher)({
            url: (0, url_join_1.default)((_b = yield core.Supplier.get(this._options.environment)) !== null && _b !== void 0 ? _b : environments.VoyageAIEnvironment.Default, "multimodalembeddings"),
            method: "POST",
            headers: {
              Authorization: yield this._getAuthorizationHeader(),
              "X-Fern-Language": "JavaScript",
              "X-Fern-SDK-Name": "voyageai",
              "X-Fern-SDK-Version": "0.0.3",
              "User-Agent": "voyageai/0.0.3",
              "X-Fern-Runtime": core.RUNTIME.type,
              "X-Fern-Runtime-Version": core.RUNTIME.version
            },
            contentType: "application/json",
            requestType: "json",
            body: serializers.MultimodalEmbedRequest.jsonOrThrow(request, { unrecognizedObjectKeys: "strip" }),
            timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1e3 : 6e4,
            maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
            abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal
          });
          if (_response.ok) {
            return serializers.MultimodalEmbedResponse.parseOrThrow(_response.body, {
              unrecognizedObjectKeys: "passthrough",
              allowUnrecognizedUnionMembers: true,
              allowUnrecognizedEnumValues: true,
              skipValidation: true,
              breadcrumbsPrefix: ["response"]
            });
          }
          if (_response.error.reason === "status-code") {
            throw new errors.VoyageAIError({
              statusCode: _response.error.statusCode,
              body: _response.error.body
            });
          }
          switch (_response.error.reason) {
            case "non-json":
              throw new errors.VoyageAIError({
                statusCode: _response.error.statusCode,
                body: _response.error.rawBody
              });
            case "timeout":
              throw new errors.VoyageAITimeoutError();
            case "unknown":
              throw new errors.VoyageAIError({
                message: _response.error.errorMessage
              });
          }
        });
      }
      _getAuthorizationHeader() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
          const bearer = (_a = yield core.Supplier.get(this._options.apiKey)) !== null && _a !== void 0 ? _a : process === null || process === void 0 ? void 0 : process.env["VOYAGE_API_KEY"];
          if (bearer == null) {
            throw new errors.VoyageAIError({
              message: "Please specify VOYAGE_API_KEY when instantiating the client."
            });
          }
          return `Bearer ${bearer}`;
        });
      }
    };
    exports.VoyageAIClient = VoyageAIClient;
  }
});

// node_modules/voyageai/index.js
var require_voyageai = __commonJS({
  "node_modules/voyageai/index.js"(exports) {
    var __createBinding = exports && exports.__createBinding || (Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    });
    var __setModuleDefault = exports && exports.__setModuleDefault || (Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    });
    var __importStar = exports && exports.__importStar || function(mod) {
      if (mod && mod.__esModule) return mod;
      var result = {};
      if (mod != null) {
        for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
      }
      __setModuleDefault(result, mod);
      return result;
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.VoyageAITimeoutError = exports.VoyageAIError = exports.VoyageAIEnvironment = exports.VoyageAIClient = exports.VoyageAI = void 0;
    exports.VoyageAI = __importStar(require_api());
    var Client_1 = require_Client();
    Object.defineProperty(exports, "VoyageAIClient", { enumerable: true, get: function() {
      return Client_1.VoyageAIClient;
    } });
    var environments_1 = require_environments();
    Object.defineProperty(exports, "VoyageAIEnvironment", { enumerable: true, get: function() {
      return environments_1.VoyageAIEnvironment;
    } });
    var errors_1 = require_errors();
    Object.defineProperty(exports, "VoyageAIError", { enumerable: true, get: function() {
      return errors_1.VoyageAIError;
    } });
    Object.defineProperty(exports, "VoyageAITimeoutError", { enumerable: true, get: function() {
      return errors_1.VoyageAITimeoutError;
    } });
  }
});
export default require_voyageai();
//# sourceMappingURL=voyageai-XB25WRRQ.js.map
