{"version": 3, "sources": ["../../node_modules/whatwg-fetch/fetch.js"], "sourcesContent": ["/* eslint-disable no-prototype-builtins */\nvar g =\n  (typeof globalThis !== 'undefined' && globalThis) ||\n  (typeof self !== 'undefined' && self) ||\n  // eslint-disable-next-line no-undef\n  (typeof global !== 'undefined' && global) ||\n  {}\n\nvar support = {\n  searchParams: 'URLSearchParams' in g,\n  iterable: 'Symbol' in g && 'iterator' in Symbol,\n  blob:\n    'FileReader' in g &&\n    'Blob' in g &&\n    (function() {\n      try {\n        new Blob()\n        return true\n      } catch (e) {\n        return false\n      }\n    })(),\n  formData: 'FormData' in g,\n  arrayBuffer: 'ArrayBuffer' in g\n}\n\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj)\n}\n\nif (support.arrayBuffer) {\n  var viewClasses = [\n    '[object Int8Array]',\n    '[object Uint8Array]',\n    '[object Uint8ClampedArray]',\n    '[object Int16Array]',\n    '[object Uint16Array]',\n    '[object Int32Array]',\n    '[object Uint32Array]',\n    '[object Float32Array]',\n    '[object Float64Array]'\n  ]\n\n  var isArrayBufferView =\n    ArrayBuffer.isView ||\n    function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name)\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n    throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n  }\n  return name.toLowerCase()\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value)\n  }\n  return value\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function() {\n      var value = items.shift()\n      return {done: value === undefined, value: value}\n    }\n  }\n\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function() {\n      return iterator\n    }\n  }\n\n  return iterator\n}\n\nexport function Headers(headers) {\n  this.map = {}\n\n  if (headers instanceof Headers) {\n    headers.forEach(function(value, name) {\n      this.append(name, value)\n    }, this)\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function(header) {\n      if (header.length != 2) {\n        throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n      }\n      this.append(header[0], header[1])\n    }, this)\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function(name) {\n      this.append(name, headers[name])\n    }, this)\n  }\n}\n\nHeaders.prototype.append = function(name, value) {\n  name = normalizeName(name)\n  value = normalizeValue(value)\n  var oldValue = this.map[name]\n  this.map[name] = oldValue ? oldValue + ', ' + value : value\n}\n\nHeaders.prototype['delete'] = function(name) {\n  delete this.map[normalizeName(name)]\n}\n\nHeaders.prototype.get = function(name) {\n  name = normalizeName(name)\n  return this.has(name) ? this.map[name] : null\n}\n\nHeaders.prototype.has = function(name) {\n  return this.map.hasOwnProperty(normalizeName(name))\n}\n\nHeaders.prototype.set = function(name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value)\n}\n\nHeaders.prototype.forEach = function(callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this)\n    }\n  }\n}\n\nHeaders.prototype.keys = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push(name)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.values = function() {\n  var items = []\n  this.forEach(function(value) {\n    items.push(value)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.entries = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push([name, value])\n  })\n  return iteratorFor(items)\n}\n\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n}\n\nfunction consumed(body) {\n  if (body._noBody) return\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'))\n  }\n  body.bodyUsed = true\n}\n\nfunction fileReaderReady(reader) {\n  return new Promise(function(resolve, reject) {\n    reader.onload = function() {\n      resolve(reader.result)\n    }\n    reader.onerror = function() {\n      reject(reader.error)\n    }\n  })\n}\n\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsArrayBuffer(blob)\n  return promise\n}\n\nfunction readBlobAsText(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type)\n  var encoding = match ? match[1] : 'utf-8'\n  reader.readAsText(blob, encoding)\n  return promise\n}\n\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf)\n  var chars = new Array(view.length)\n\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i])\n  }\n  return chars.join('')\n}\n\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0)\n  } else {\n    var view = new Uint8Array(buf.byteLength)\n    view.set(new Uint8Array(buf))\n    return view.buffer\n  }\n}\n\nfunction Body() {\n  this.bodyUsed = false\n\n  this._initBody = function(body) {\n    /*\n      fetch-mock wraps the Response object in an ES6 Proxy to\n      provide useful test harness features such as flush. However, on\n      ES5 browsers without fetch or Proxy support pollyfills must be used;\n      the proxy-pollyfill is unable to proxy an attribute unless it exists\n      on the object before the Proxy is created. This change ensures\n      Response.bodyUsed exists on the instance, while maintaining the\n      semantic of setting Request.bodyUsed in the constructor before\n      _initBody is called.\n    */\n    // eslint-disable-next-line no-self-assign\n    this.bodyUsed = this.bodyUsed\n    this._bodyInit = body\n    if (!body) {\n      this._noBody = true;\n      this._bodyText = ''\n    } else if (typeof body === 'string') {\n      this._bodyText = body\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString()\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer)\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer])\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body)\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body)\n    }\n\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8')\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type)\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n      }\n    }\n  }\n\n  if (support.blob) {\n    this.blob = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob')\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]))\n      }\n    }\n  }\n\n  this.arrayBuffer = function() {\n    if (this._bodyArrayBuffer) {\n      var isConsumed = consumed(this)\n      if (isConsumed) {\n        return isConsumed\n      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n        return Promise.resolve(\n          this._bodyArrayBuffer.buffer.slice(\n            this._bodyArrayBuffer.byteOffset,\n            this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n          )\n        )\n      } else {\n        return Promise.resolve(this._bodyArrayBuffer)\n      }\n    } else if (support.blob) {\n      return this.blob().then(readBlobAsArrayBuffer)\n    } else {\n      throw new Error('could not read as ArrayBuffer')\n    }\n  }\n\n  this.text = function() {\n    var rejected = consumed(this)\n    if (rejected) {\n      return rejected\n    }\n\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob)\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text')\n    } else {\n      return Promise.resolve(this._bodyText)\n    }\n  }\n\n  if (support.formData) {\n    this.formData = function() {\n      return this.text().then(decode)\n    }\n  }\n\n  this.json = function() {\n    return this.text().then(JSON.parse)\n  }\n\n  return this\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE']\n\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase()\n  return methods.indexOf(upcased) > -1 ? upcased : method\n}\n\nexport function Request(input, options) {\n  if (!(this instanceof Request)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n\n  options = options || {}\n  var body = options.body\n\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read')\n    }\n    this.url = input.url\n    this.credentials = input.credentials\n    if (!options.headers) {\n      this.headers = new Headers(input.headers)\n    }\n    this.method = input.method\n    this.mode = input.mode\n    this.signal = input.signal\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit\n      input.bodyUsed = true\n    }\n  } else {\n    this.url = String(input)\n  }\n\n  this.credentials = options.credentials || this.credentials || 'same-origin'\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers)\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET')\n  this.mode = options.mode || this.mode || null\n  this.signal = options.signal || this.signal || (function () {\n    if ('AbortController' in g) {\n      var ctrl = new AbortController();\n      return ctrl.signal;\n    }\n  }());\n  this.referrer = null\n\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests')\n  }\n  this._initBody(body)\n\n  if (this.method === 'GET' || this.method === 'HEAD') {\n    if (options.cache === 'no-store' || options.cache === 'no-cache') {\n      // Search for a '_' parameter in the query string\n      var reParamSearch = /([?&])_=[^&]*/\n      if (reParamSearch.test(this.url)) {\n        // If it already exists then set the value with the current time\n        this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime())\n      } else {\n        // Otherwise add a new '_' parameter to the end with the current time\n        var reQueryString = /\\?/\n        this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime()\n      }\n    }\n  }\n}\n\nRequest.prototype.clone = function() {\n  return new Request(this, {body: this._bodyInit})\n}\n\nfunction decode(body) {\n  var form = new FormData()\n  body\n    .trim()\n    .split('&')\n    .forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n  return form\n}\n\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers()\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n  // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n  // https://github.com/github/fetch/issues/748\n  // https://github.com/zloirock/core-js/issues/751\n  preProcessedHeaders\n    .split('\\r')\n    .map(function(header) {\n      return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n    })\n    .forEach(function(line) {\n      var parts = line.split(':')\n      var key = parts.shift().trim()\n      if (key) {\n        var value = parts.join(':').trim()\n        try {\n          headers.append(key, value)\n        } catch (error) {\n          console.warn('Response ' + error.message)\n        }\n      }\n    })\n  return headers\n}\n\nBody.call(Request.prototype)\n\nexport function Response(bodyInit, options) {\n  if (!(this instanceof Response)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n  if (!options) {\n    options = {}\n  }\n\n  this.type = 'default'\n  this.status = options.status === undefined ? 200 : options.status\n  if (this.status < 200 || this.status > 599) {\n    throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n  }\n  this.ok = this.status >= 200 && this.status < 300\n  this.statusText = options.statusText === undefined ? '' : '' + options.statusText\n  this.headers = new Headers(options.headers)\n  this.url = options.url || ''\n  this._initBody(bodyInit)\n}\n\nBody.call(Response.prototype)\n\nResponse.prototype.clone = function() {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  })\n}\n\nResponse.error = function() {\n  var response = new Response(null, {status: 200, statusText: ''})\n  response.ok = false\n  response.status = 0\n  response.type = 'error'\n  return response\n}\n\nvar redirectStatuses = [301, 302, 303, 307, 308]\n\nResponse.redirect = function(url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code')\n  }\n\n  return new Response(null, {status: status, headers: {location: url}})\n}\n\nexport var DOMException = g.DOMException\ntry {\n  new DOMException()\n} catch (err) {\n  DOMException = function(message, name) {\n    this.message = message\n    this.name = name\n    var error = Error(message)\n    this.stack = error.stack\n  }\n  DOMException.prototype = Object.create(Error.prototype)\n  DOMException.prototype.constructor = DOMException\n}\n\nexport function fetch(input, init) {\n  return new Promise(function(resolve, reject) {\n    var request = new Request(input, init)\n\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    var xhr = new XMLHttpRequest()\n\n    function abortXhr() {\n      xhr.abort()\n    }\n\n    xhr.onload = function() {\n      var options = {\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      }\n      // This check if specifically for when a user fetches a file locally from the file system\n      // Only if the status is out of a normal range\n      if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n        options.status = 200;\n      } else {\n        options.status = xhr.status;\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n      var body = 'response' in xhr ? xhr.response : xhr.responseText\n      setTimeout(function() {\n        resolve(new Response(body, options))\n      }, 0)\n    }\n\n    xhr.onerror = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request failed'))\n      }, 0)\n    }\n\n    xhr.ontimeout = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request timed out'))\n      }, 0)\n    }\n\n    xhr.onabort = function() {\n      setTimeout(function() {\n        reject(new DOMException('Aborted', 'AbortError'))\n      }, 0)\n    }\n\n    function fixUrl(url) {\n      try {\n        return url === '' && g.location.href ? g.location.href : url\n      } catch (e) {\n        return url\n      }\n    }\n\n    xhr.open(request.method, fixUrl(request.url), true)\n\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false\n    }\n\n    if ('responseType' in xhr) {\n      if (support.blob) {\n        xhr.responseType = 'blob'\n      } else if (\n        support.arrayBuffer\n      ) {\n        xhr.responseType = 'arraybuffer'\n      }\n    }\n\n    if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n      var names = [];\n      Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n        names.push(normalizeName(name))\n        xhr.setRequestHeader(name, normalizeValue(init.headers[name]))\n      })\n      request.headers.forEach(function(value, name) {\n        if (names.indexOf(name) === -1) {\n          xhr.setRequestHeader(name, value)\n        }\n      })\n    } else {\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value)\n      })\n    }\n\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr)\n\n      xhr.onreadystatechange = function() {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr)\n        }\n      }\n    }\n\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n  })\n}\n\nfetch.polyfill = true\n\nif (!g.fetch) {\n  g.fetch = fetch\n  g.Headers = Headers\n  g.Request = Request\n  g.Response = Response\n}\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA,SAAS,WAAW,KAAK;AACvB,SAAO,OAAO,SAAS,UAAU,cAAc,GAAG;AACpD;AAsBA,SAAS,cAAc,MAAM;AAC3B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,OAAO,IAAI;AAAA,EACpB;AACA,MAAI,6BAA6B,KAAK,IAAI,KAAK,SAAS,IAAI;AAC1D,UAAM,IAAI,UAAU,8CAA8C,OAAO,GAAG;AAAA,EAC9E;AACA,SAAO,KAAK,YAAY;AAC1B;AAEA,SAAS,eAAe,OAAO;AAC7B,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,OAAO,KAAK;AAAA,EACtB;AACA,SAAO;AACT;AAGA,SAAS,YAAY,OAAO;AAC1B,MAAI,WAAW;AAAA,IACb,MAAM,WAAW;AACf,UAAI,QAAQ,MAAM,MAAM;AACxB,aAAO,EAAC,MAAM,UAAU,QAAW,MAAY;AAAA,IACjD;AAAA,EACF;AAEA,MAAI,QAAQ,UAAU;AACpB,aAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,QAAQ,SAAS;AAC/B,OAAK,MAAM,CAAC;AAEZ,MAAI,mBAAmB,SAAS;AAC9B,YAAQ,QAAQ,SAAS,OAAO,MAAM;AACpC,WAAK,OAAO,MAAM,KAAK;AAAA,IACzB,GAAG,IAAI;AAAA,EACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,YAAQ,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,UAAU,GAAG;AACtB,cAAM,IAAI,UAAU,wEAAwE,OAAO,MAAM;AAAA,MAC3G;AACA,WAAK,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IAClC,GAAG,IAAI;AAAA,EACT,WAAW,SAAS;AAClB,WAAO,oBAAoB,OAAO,EAAE,QAAQ,SAAS,MAAM;AACzD,WAAK,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,IACjC,GAAG,IAAI;AAAA,EACT;AACF;AA8DA,SAAS,SAAS,MAAM;AACtB,MAAI,KAAK,QAAS;AAClB,MAAI,KAAK,UAAU;AACjB,WAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC;AAAA,EACrD;AACA,OAAK,WAAW;AAClB;AAEA,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,WAAO,SAAS,WAAW;AACzB,cAAQ,OAAO,MAAM;AAAA,IACvB;AACA,WAAO,UAAU,WAAW;AAC1B,aAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF,CAAC;AACH;AAEA,SAAS,sBAAsB,MAAM;AACnC,MAAI,SAAS,IAAI,WAAW;AAC5B,MAAI,UAAU,gBAAgB,MAAM;AACpC,SAAO,kBAAkB,IAAI;AAC7B,SAAO;AACT;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI,SAAS,IAAI,WAAW;AAC5B,MAAI,UAAU,gBAAgB,MAAM;AACpC,MAAI,QAAQ,2BAA2B,KAAK,KAAK,IAAI;AACrD,MAAI,WAAW,QAAQ,MAAM,CAAC,IAAI;AAClC,SAAO,WAAW,MAAM,QAAQ;AAChC,SAAO;AACT;AAEA,SAAS,sBAAsB,KAAK;AAClC,MAAI,OAAO,IAAI,WAAW,GAAG;AAC7B,MAAI,QAAQ,IAAI,MAAM,KAAK,MAAM;AAEjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,CAAC,IAAI,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,EACxC;AACA,SAAO,MAAM,KAAK,EAAE;AACtB;AAEA,SAAS,YAAY,KAAK;AACxB,MAAI,IAAI,OAAO;AACb,WAAO,IAAI,MAAM,CAAC;AAAA,EACpB,OAAO;AACL,QAAI,OAAO,IAAI,WAAW,IAAI,UAAU;AACxC,SAAK,IAAI,IAAI,WAAW,GAAG,CAAC;AAC5B,WAAO,KAAK;AAAA,EACd;AACF;AAEA,SAAS,OAAO;AACd,OAAK,WAAW;AAEhB,OAAK,YAAY,SAAS,MAAM;AAY9B,SAAK,WAAW,KAAK;AACrB,SAAK,YAAY;AACjB,QAAI,CAAC,MAAM;AACT,WAAK,UAAU;AACf,WAAK,YAAY;AAAA,IACnB,WAAW,OAAO,SAAS,UAAU;AACnC,WAAK,YAAY;AAAA,IACnB,WAAW,QAAQ,QAAQ,KAAK,UAAU,cAAc,IAAI,GAAG;AAC7D,WAAK,YAAY;AAAA,IACnB,WAAW,QAAQ,YAAY,SAAS,UAAU,cAAc,IAAI,GAAG;AACrE,WAAK,gBAAgB;AAAA,IACvB,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,WAAK,YAAY,KAAK,SAAS;AAAA,IACjC,WAAW,QAAQ,eAAe,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAClE,WAAK,mBAAmB,YAAY,KAAK,MAAM;AAE/C,WAAK,YAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,IACnD,WAAW,QAAQ,gBAAgB,YAAY,UAAU,cAAc,IAAI,KAAK,kBAAkB,IAAI,IAAI;AACxG,WAAK,mBAAmB,YAAY,IAAI;AAAA,IAC1C,OAAO;AACL,WAAK,YAAY,OAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,IAC7D;AAEA,QAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,GAAG;AACrC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,MAC7D,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,aAAK,QAAQ,IAAI,gBAAgB,KAAK,UAAU,IAAI;AAAA,MACtD,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,aAAK,QAAQ,IAAI,gBAAgB,iDAAiD;AAAA,MACpF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM;AAChB,SAAK,OAAO,WAAW;AACrB,UAAI,WAAW,SAAS,IAAI;AAC5B,UAAI,UAAU;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,WAAW;AAClB,eAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,MACvC,WAAW,KAAK,kBAAkB;AAChC,eAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,MAC1D,WAAW,KAAK,eAAe;AAC7B,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD,OAAO;AACL,eAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAEA,OAAK,cAAc,WAAW;AAC5B,QAAI,KAAK,kBAAkB;AACzB,UAAI,aAAa,SAAS,IAAI;AAC9B,UAAI,YAAY;AACd,eAAO;AAAA,MACT,WAAW,YAAY,OAAO,KAAK,gBAAgB,GAAG;AACpD,eAAO,QAAQ;AAAA,UACb,KAAK,iBAAiB,OAAO;AAAA,YAC3B,KAAK,iBAAiB;AAAA,YACtB,KAAK,iBAAiB,aAAa,KAAK,iBAAiB;AAAA,UAC3D;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,MAC9C;AAAA,IACF,WAAW,QAAQ,MAAM;AACvB,aAAO,KAAK,KAAK,EAAE,KAAK,qBAAqB;AAAA,IAC/C,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAAA,EACF;AAEA,OAAK,OAAO,WAAW;AACrB,QAAI,WAAW,SAAS,IAAI;AAC5B,QAAI,UAAU;AACZ,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,WAAW;AAClB,aAAO,eAAe,KAAK,SAAS;AAAA,IACtC,WAAW,KAAK,kBAAkB;AAChC,aAAO,QAAQ,QAAQ,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,IACrE,WAAW,KAAK,eAAe;AAC7B,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD,OAAO;AACL,aAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,IACvC;AAAA,EACF;AAEA,MAAI,QAAQ,UAAU;AACpB,SAAK,WAAW,WAAW;AACzB,aAAO,KAAK,KAAK,EAAE,KAAK,MAAM;AAAA,IAChC;AAAA,EACF;AAEA,OAAK,OAAO,WAAW;AACrB,WAAO,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAKA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,UAAU,OAAO,YAAY;AACjC,SAAO,QAAQ,QAAQ,OAAO,IAAI,KAAK,UAAU;AACnD;AAEO,SAAS,QAAQ,OAAO,SAAS;AACtC,MAAI,EAAE,gBAAgB,UAAU;AAC9B,UAAM,IAAI,UAAU,4FAA4F;AAAA,EAClH;AAEA,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,QAAQ;AAEnB,MAAI,iBAAiB,SAAS;AAC5B,QAAI,MAAM,UAAU;AAClB,YAAM,IAAI,UAAU,cAAc;AAAA,IACpC;AACA,SAAK,MAAM,MAAM;AACjB,SAAK,cAAc,MAAM;AACzB,QAAI,CAAC,QAAQ,SAAS;AACpB,WAAK,UAAU,IAAI,QAAQ,MAAM,OAAO;AAAA,IAC1C;AACA,SAAK,SAAS,MAAM;AACpB,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,MAAM;AACpB,QAAI,CAAC,QAAQ,MAAM,aAAa,MAAM;AACpC,aAAO,MAAM;AACb,YAAM,WAAW;AAAA,IACnB;AAAA,EACF,OAAO;AACL,SAAK,MAAM,OAAO,KAAK;AAAA,EACzB;AAEA,OAAK,cAAc,QAAQ,eAAe,KAAK,eAAe;AAC9D,MAAI,QAAQ,WAAW,CAAC,KAAK,SAAS;AACpC,SAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAAA,EAC5C;AACA,OAAK,SAAS,gBAAgB,QAAQ,UAAU,KAAK,UAAU,KAAK;AACpE,OAAK,OAAO,QAAQ,QAAQ,KAAK,QAAQ;AACzC,OAAK,SAAS,QAAQ,UAAU,KAAK,UAAW,WAAY;AAC1D,QAAI,qBAAqB,GAAG;AAC1B,UAAI,OAAO,IAAI,gBAAgB;AAC/B,aAAO,KAAK;AAAA,IACd;AAAA,EACF,EAAE;AACF,OAAK,WAAW;AAEhB,OAAK,KAAK,WAAW,SAAS,KAAK,WAAW,WAAW,MAAM;AAC7D,UAAM,IAAI,UAAU,2CAA2C;AAAA,EACjE;AACA,OAAK,UAAU,IAAI;AAEnB,MAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,QAAI,QAAQ,UAAU,cAAc,QAAQ,UAAU,YAAY;AAEhE,UAAI,gBAAgB;AACpB,UAAI,cAAc,KAAK,KAAK,GAAG,GAAG;AAEhC,aAAK,MAAM,KAAK,IAAI,QAAQ,eAAe,UAAS,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,MAC1E,OAAO;AAEL,YAAI,gBAAgB;AACpB,aAAK,QAAQ,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM,OAAO,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AACF;AAMA,SAAS,OAAO,MAAM;AACpB,MAAI,OAAO,IAAI,SAAS;AACxB,OACG,KAAK,EACL,MAAM,GAAG,EACT,QAAQ,SAAS,OAAO;AACvB,QAAI,OAAO;AACT,UAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,UAAI,OAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG;AAC3C,UAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9C,WAAK,OAAO,mBAAmB,IAAI,GAAG,mBAAmB,KAAK,CAAC;AAAA,IACjE;AAAA,EACF,CAAC;AACH,SAAO;AACT;AAEA,SAAS,aAAa,YAAY;AAChC,MAAI,UAAU,IAAI,QAAQ;AAG1B,MAAI,sBAAsB,WAAW,QAAQ,gBAAgB,GAAG;AAIhE,sBACG,MAAM,IAAI,EACV,IAAI,SAAS,QAAQ;AACpB,WAAO,OAAO,QAAQ,IAAI,MAAM,IAAI,OAAO,OAAO,GAAG,OAAO,MAAM,IAAI;AAAA,EACxE,CAAC,EACA,QAAQ,SAAS,MAAM;AACtB,QAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,QAAI,MAAM,MAAM,MAAM,EAAE,KAAK;AAC7B,QAAI,KAAK;AACP,UAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK;AACjC,UAAI;AACF,gBAAQ,OAAO,KAAK,KAAK;AAAA,MAC3B,SAAS,OAAO;AACd,gBAAQ,KAAK,cAAc,MAAM,OAAO;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,CAAC;AACH,SAAO;AACT;AAIO,SAAS,SAAS,UAAU,SAAS;AAC1C,MAAI,EAAE,gBAAgB,WAAW;AAC/B,UAAM,IAAI,UAAU,4FAA4F;AAAA,EAClH;AACA,MAAI,CAAC,SAAS;AACZ,cAAU,CAAC;AAAA,EACb;AAEA,OAAK,OAAO;AACZ,OAAK,SAAS,QAAQ,WAAW,SAAY,MAAM,QAAQ;AAC3D,MAAI,KAAK,SAAS,OAAO,KAAK,SAAS,KAAK;AAC1C,UAAM,IAAI,WAAW,0FAA0F;AAAA,EACjH;AACA,OAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,OAAK,aAAa,QAAQ,eAAe,SAAY,KAAK,KAAK,QAAQ;AACvE,OAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAC1C,OAAK,MAAM,QAAQ,OAAO;AAC1B,OAAK,UAAU,QAAQ;AACzB;AA6CO,SAAS,MAAM,OAAO,MAAM;AACjC,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,UAAU,IAAI,QAAQ,OAAO,IAAI;AAErC,QAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS;AAC5C,aAAO,OAAO,IAAI,aAAa,WAAW,YAAY,CAAC;AAAA,IACzD;AAEA,QAAI,MAAM,IAAI,eAAe;AAE7B,aAAS,WAAW;AAClB,UAAI,MAAM;AAAA,IACZ;AAEA,QAAI,SAAS,WAAW;AACtB,UAAI,UAAU;AAAA,QACZ,YAAY,IAAI;AAAA,QAChB,SAAS,aAAa,IAAI,sBAAsB,KAAK,EAAE;AAAA,MACzD;AAGA,UAAI,QAAQ,IAAI,QAAQ,SAAS,MAAM,MAAM,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM;AAClF,gBAAQ,SAAS;AAAA,MACnB,OAAO;AACL,gBAAQ,SAAS,IAAI;AAAA,MACvB;AACA,cAAQ,MAAM,iBAAiB,MAAM,IAAI,cAAc,QAAQ,QAAQ,IAAI,eAAe;AAC1F,UAAI,OAAO,cAAc,MAAM,IAAI,WAAW,IAAI;AAClD,iBAAW,WAAW;AACpB,gBAAQ,IAAI,SAAS,MAAM,OAAO,CAAC;AAAA,MACrC,GAAG,CAAC;AAAA,IACN;AAEA,QAAI,UAAU,WAAW;AACvB,iBAAW,WAAW;AACpB,eAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,MAChD,GAAG,CAAC;AAAA,IACN;AAEA,QAAI,YAAY,WAAW;AACzB,iBAAW,WAAW;AACpB,eAAO,IAAI,UAAU,2BAA2B,CAAC;AAAA,MACnD,GAAG,CAAC;AAAA,IACN;AAEA,QAAI,UAAU,WAAW;AACvB,iBAAW,WAAW;AACpB,eAAO,IAAI,aAAa,WAAW,YAAY,CAAC;AAAA,MAClD,GAAG,CAAC;AAAA,IACN;AAEA,aAAS,OAAO,KAAK;AACnB,UAAI;AACF,eAAO,QAAQ,MAAM,EAAE,SAAS,OAAO,EAAE,SAAS,OAAO;AAAA,MAC3D,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,QAAQ,OAAO,QAAQ,GAAG,GAAG,IAAI;AAElD,QAAI,QAAQ,gBAAgB,WAAW;AACrC,UAAI,kBAAkB;AAAA,IACxB,WAAW,QAAQ,gBAAgB,QAAQ;AACzC,UAAI,kBAAkB;AAAA,IACxB;AAEA,QAAI,kBAAkB,KAAK;AACzB,UAAI,QAAQ,MAAM;AAChB,YAAI,eAAe;AAAA,MACrB,WACE,QAAQ,aACR;AACA,YAAI,eAAe;AAAA,MACrB;AAAA,IACF;AAEA,QAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,EAAE,KAAK,mBAAmB,WAAY,EAAE,WAAW,KAAK,mBAAmB,EAAE,UAAW;AACtI,UAAI,QAAQ,CAAC;AACb,aAAO,oBAAoB,KAAK,OAAO,EAAE,QAAQ,SAAS,MAAM;AAC9D,cAAM,KAAK,cAAc,IAAI,CAAC;AAC9B,YAAI,iBAAiB,MAAM,eAAe,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,MAC/D,CAAC;AACD,cAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,YAAI,MAAM,QAAQ,IAAI,MAAM,IAAI;AAC9B,cAAI,iBAAiB,MAAM,KAAK;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,YAAI,iBAAiB,MAAM,KAAK;AAAA,MAClC,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,QAAQ;AAClB,cAAQ,OAAO,iBAAiB,SAAS,QAAQ;AAEjD,UAAI,qBAAqB,WAAW;AAElC,YAAI,IAAI,eAAe,GAAG;AACxB,kBAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,OAAO,QAAQ,cAAc,cAAc,OAAO,QAAQ,SAAS;AAAA,EAC9E,CAAC;AACH;AAxnBA,IACI,GAOA,SAuBE,aAYA,mBA2SF,SA+JA,kBAUO;AA/fX;AAAA;AACA,IAAI,IACD,OAAO,eAAe,eAAe,cACrC,OAAO,SAAS,eAAe;AAAA,IAE/B,OAAO,WAAW,eAAe,UAClC,CAAC;AAEH,IAAI,UAAU;AAAA,MACZ,cAAc,qBAAqB;AAAA,MACnC,UAAU,YAAY,KAAK,cAAc;AAAA,MACzC,MACE,gBAAgB,KAChB,UAAU,KACT,WAAW;AACV,YAAI;AACF,cAAI,KAAK;AACT,iBAAO;AAAA,QACT,SAAS,GAAG;AACV,iBAAO;AAAA,QACT;AAAA,MACF,EAAG;AAAA,MACL,UAAU,cAAc;AAAA,MACxB,aAAa,iBAAiB;AAAA,IAChC;AAMA,QAAI,QAAQ,aAAa;AACnB,oBAAc;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEI,0BACF,YAAY,UACZ,SAAS,KAAK;AACZ,eAAO,OAAO,YAAY,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI;AAAA,MAC3E;AAAA,IACJ;AA0DA,YAAQ,UAAU,SAAS,SAAS,MAAM,OAAO;AAC/C,aAAO,cAAc,IAAI;AACzB,cAAQ,eAAe,KAAK;AAC5B,UAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,WAAK,IAAI,IAAI,IAAI,WAAW,WAAW,OAAO,QAAQ;AAAA,IACxD;AAEA,YAAQ,UAAU,QAAQ,IAAI,SAAS,MAAM;AAC3C,aAAO,KAAK,IAAI,cAAc,IAAI,CAAC;AAAA,IACrC;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,aAAO,cAAc,IAAI;AACzB,aAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,IAC3C;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,aAAO,KAAK,IAAI,eAAe,cAAc,IAAI,CAAC;AAAA,IACpD;AAEA,YAAQ,UAAU,MAAM,SAAS,MAAM,OAAO;AAC5C,WAAK,IAAI,cAAc,IAAI,CAAC,IAAI,eAAe,KAAK;AAAA,IACtD;AAEA,YAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACtD,eAAS,QAAQ,KAAK,KAAK;AACzB,YAAI,KAAK,IAAI,eAAe,IAAI,GAAG;AACjC,mBAAS,KAAK,SAAS,KAAK,IAAI,IAAI,GAAG,MAAM,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,UAAU,OAAO,WAAW;AAClC,UAAI,QAAQ,CAAC;AACb,WAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,cAAM,KAAK,IAAI;AAAA,MACjB,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B;AAEA,YAAQ,UAAU,SAAS,WAAW;AACpC,UAAI,QAAQ,CAAC;AACb,WAAK,QAAQ,SAAS,OAAO;AAC3B,cAAM,KAAK,KAAK;AAAA,MAClB,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B;AAEA,YAAQ,UAAU,UAAU,WAAW;AACrC,UAAI,QAAQ,CAAC;AACb,WAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,cAAM,KAAK,CAAC,MAAM,KAAK,CAAC;AAAA,MAC1B,CAAC;AACD,aAAO,YAAY,KAAK;AAAA,IAC1B;AAEA,QAAI,QAAQ,UAAU;AACpB,cAAQ,UAAU,OAAO,QAAQ,IAAI,QAAQ,UAAU;AAAA,IACzD;AAkLA,IAAI,UAAU,CAAC,WAAW,UAAU,OAAO,QAAQ,WAAW,SAAS,QAAQ,OAAO,OAAO;AAsE7F,YAAQ,UAAU,QAAQ,WAAW;AACnC,aAAO,IAAI,QAAQ,MAAM,EAAC,MAAM,KAAK,UAAS,CAAC;AAAA,IACjD;AA8CA,SAAK,KAAK,QAAQ,SAAS;AAsB3B,SAAK,KAAK,SAAS,SAAS;AAE5B,aAAS,UAAU,QAAQ,WAAW;AACpC,aAAO,IAAI,SAAS,KAAK,WAAW;AAAA,QAClC,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK;AAAA,QACjB,SAAS,IAAI,QAAQ,KAAK,OAAO;AAAA,QACjC,KAAK,KAAK;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,aAAS,QAAQ,WAAW;AAC1B,UAAI,WAAW,IAAI,SAAS,MAAM,EAAC,QAAQ,KAAK,YAAY,GAAE,CAAC;AAC/D,eAAS,KAAK;AACd,eAAS,SAAS;AAClB,eAAS,OAAO;AAChB,aAAO;AAAA,IACT;AAEA,IAAI,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAE/C,aAAS,WAAW,SAAS,KAAK,QAAQ;AACxC,UAAI,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAC3C,cAAM,IAAI,WAAW,qBAAqB;AAAA,MAC5C;AAEA,aAAO,IAAI,SAAS,MAAM,EAAC,QAAgB,SAAS,EAAC,UAAU,IAAG,EAAC,CAAC;AAAA,IACtE;AAEO,IAAI,eAAe,EAAE;AAC5B,QAAI;AACF,UAAI,aAAa;AAAA,IACnB,SAAS,KAAK;AACZ,qBAAe,SAAS,SAAS,MAAM;AACrC,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,YAAI,QAAQ,MAAM,OAAO;AACzB,aAAK,QAAQ,MAAM;AAAA,MACrB;AACA,mBAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AACtD,mBAAa,UAAU,cAAc;AAAA,IACvC;AA+GA,UAAM,WAAW;AAEjB,QAAI,CAAC,EAAE,OAAO;AACZ,QAAE,QAAQ;AACV,QAAE,UAAU;AACZ,QAAE,UAAU;AACZ,QAAE,WAAW;AAAA,IACf;AAAA;AAAA;", "names": []}