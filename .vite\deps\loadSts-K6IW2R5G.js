import {
  AssumeRoleCommand,
  STSClient,
  init_sts
} from "./chunk-CS7IYQNM.js";
import "./chunk-TJEVHZ2X.js";
import "./chunk-YDYV3GMK.js";
import "./chunk-NOR3QQAA.js";
import {
  __esm
} from "./chunk-EWTE5DHJ.js";

// node_modules/@aws-sdk/credential-providers/dist-es/loadSts.js
var init_loadSts = __esm({
  "node_modules/@aws-sdk/credential-providers/dist-es/loadSts.js"() {
    init_sts();
  }
});
init_loadSts();
export {
  AssumeRoleCommand,
  STSClient
};
//# sourceMappingURL=loadSts-K6IW2R5G.js.map
