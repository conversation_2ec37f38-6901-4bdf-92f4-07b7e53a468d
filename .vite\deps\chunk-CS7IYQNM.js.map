{"version": 3, "sources": ["../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthSchemeProvider.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js", "../../node_modules/@aws-sdk/nested-clients/package.json", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/ruleset.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/endpointResolver.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.shared.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.browser.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthExtensionConfiguration.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeExtensions.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/protocols/Aws_query.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STS.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/index.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/index.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultStsRoleAssumers.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultRoleAssumers.js", "../../node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/index.js"], "sourcesContent": ["import { resolveAwsSdkSigV4Config, } from \"@aws-sdk/core\";\nimport { getSmithyContext, normalizeProvider } from \"@smithy/util-middleware\";\nimport { STSClient } from \"../STSClient\";\nexport const defaultSTSHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: getSmithyContext(context).operation,\n        region: (await normalizeProvider(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"sts\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nfunction createSmithyApiNoAuthHttpAuthOption(authParameters) {\n    return {\n        schemeId: \"smithy.api#noAuth\",\n    };\n}\nexport const defaultSTSHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        case \"AssumeRoleWithWebIdentity\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nexport const resolveStsAuthConfig = (input) => Object.assign(input, {\n    stsClientCtor: STSClient,\n});\nexport const resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = resolveStsAuthConfig(config);\n    const config_1 = resolveAwsSdkSigV4Config(config_0);\n    return Object.assign(config_1, {\n        authSchemePreference: normalizeProvider(config.authSchemePreference ?? []),\n    });\n};\n", "export const resolveClientEndpointParameters = (options) => {\n    return Object.assign(options, {\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        useGlobalEndpoint: options.useGlobalEndpoint ?? false,\n        defaultSigningName: \"sts\",\n    });\n};\nexport const commonParams = {\n    UseGlobalEndpoint: { type: \"builtInParams\", name: \"useGlobalEndpoint\" },\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n", "{\n  \"name\": \"@aws-sdk/nested-clients\",\n  \"version\": \"3.804.0\",\n  \"description\": \"Nested clients for AWS SDK packages.\",\n  \"main\": \"./dist-cjs/index.js\",\n  \"module\": \"./dist-es/index.js\",\n  \"types\": \"./dist-types/index.d.ts\",\n  \"scripts\": {\n    \"build\": \"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'\",\n    \"build:cjs\": \"node ../../scripts/compilation/inline nested-clients\",\n    \"build:es\": \"tsc -p tsconfig.es.json\",\n    \"build:include:deps\": \"lerna run --scope $npm_package_name --include-dependencies build\",\n    \"build:types\": \"tsc -p tsconfig.types.json\",\n    \"build:types:downlevel\": \"downlevel-dts dist-types dist-types/ts3.4\",\n    \"clean\": \"rimraf ./dist-* && rimraf *.tsbuildinfo\",\n    \"lint\": \"node ../../scripts/validation/submodules-linter.js --pkg nested-clients\",\n    \"test\": \"yarn g:vitest run\",\n    \"test:watch\": \"yarn g:vitest watch\"\n  },\n  \"engines\": {\n    \"node\": \">=18.0.0\"\n  },\n  \"author\": {\n    \"name\": \"AWS SDK for JavaScript Team\",\n    \"url\": \"https://aws.amazon.com/javascript/\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"dependencies\": {\n    \"@aws-crypto/sha256-browser\": \"5.2.0\",\n    \"@aws-crypto/sha256-js\": \"5.2.0\",\n    \"@aws-sdk/core\": \"3.804.0\",\n    \"@aws-sdk/middleware-host-header\": \"3.804.0\",\n    \"@aws-sdk/middleware-logger\": \"3.804.0\",\n    \"@aws-sdk/middleware-recursion-detection\": \"3.804.0\",\n    \"@aws-sdk/middleware-user-agent\": \"3.804.0\",\n    \"@aws-sdk/region-config-resolver\": \"3.804.0\",\n    \"@aws-sdk/types\": \"3.804.0\",\n    \"@aws-sdk/util-endpoints\": \"3.804.0\",\n    \"@aws-sdk/util-user-agent-browser\": \"3.804.0\",\n    \"@aws-sdk/util-user-agent-node\": \"3.804.0\",\n    \"@smithy/config-resolver\": \"^4.1.0\",\n    \"@smithy/core\": \"^3.3.1\",\n    \"@smithy/fetch-http-handler\": \"^5.0.2\",\n    \"@smithy/hash-node\": \"^4.0.2\",\n    \"@smithy/invalid-dependency\": \"^4.0.2\",\n    \"@smithy/middleware-content-length\": \"^4.0.2\",\n    \"@smithy/middleware-endpoint\": \"^4.1.2\",\n    \"@smithy/middleware-retry\": \"^4.1.3\",\n    \"@smithy/middleware-serde\": \"^4.0.3\",\n    \"@smithy/middleware-stack\": \"^4.0.2\",\n    \"@smithy/node-config-provider\": \"^4.0.2\",\n    \"@smithy/node-http-handler\": \"^4.0.4\",\n    \"@smithy/protocol-http\": \"^5.1.0\",\n    \"@smithy/smithy-client\": \"^4.2.2\",\n    \"@smithy/types\": \"^4.2.0\",\n    \"@smithy/url-parser\": \"^4.0.2\",\n    \"@smithy/util-base64\": \"^4.0.0\",\n    \"@smithy/util-body-length-browser\": \"^4.0.0\",\n    \"@smithy/util-body-length-node\": \"^4.0.0\",\n    \"@smithy/util-defaults-mode-browser\": \"^4.0.10\",\n    \"@smithy/util-defaults-mode-node\": \"^4.0.10\",\n    \"@smithy/util-endpoints\": \"^3.0.2\",\n    \"@smithy/util-middleware\": \"^4.0.2\",\n    \"@smithy/util-retry\": \"^4.0.3\",\n    \"@smithy/util-utf8\": \"^4.0.0\",\n    \"tslib\": \"^2.6.2\"\n  },\n  \"devDependencies\": {\n    \"concurrently\": \"7.0.0\",\n    \"downlevel-dts\": \"0.10.1\",\n    \"rimraf\": \"3.0.2\",\n    \"typescript\": \"~5.8.3\"\n  },\n  \"typesVersions\": {\n    \"<4.0\": {\n      \"dist-types/*\": [\n        \"dist-types/ts3.4/*\"\n      ]\n    }\n  },\n  \"files\": [\n    \"./sso-oidc.d.ts\",\n    \"./sso-oidc.js\",\n    \"./sts.d.ts\",\n    \"./sts.js\",\n    \"dist-*/**\"\n  ],\n  \"browser\": {\n    \"./dist-es/submodules/sso-oidc/runtimeConfig\": \"./dist-es/submodules/sso-oidc/runtimeConfig.browser\",\n    \"./dist-es/submodules/sts/runtimeConfig\": \"./dist-es/submodules/sts/runtimeConfig.browser\"\n  },\n  \"react-native\": {},\n  \"homepage\": \"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/aws/aws-sdk-js-v3.git\",\n    \"directory\": \"packages/nested-clients\"\n  },\n  \"exports\": {\n    \"./sso-oidc\": {\n      \"types\": \"./dist-types/submodules/sso-oidc/index.d.ts\",\n      \"module\": \"./dist-es/submodules/sso-oidc/index.js\",\n      \"node\": \"./dist-cjs/submodules/sso-oidc/index.js\",\n      \"import\": \"./dist-es/submodules/sso-oidc/index.js\",\n      \"require\": \"./dist-cjs/submodules/sso-oidc/index.js\"\n    },\n    \"./sts\": {\n      \"types\": \"./dist-types/submodules/sts/index.d.ts\",\n      \"module\": \"./dist-es/submodules/sts/index.js\",\n      \"node\": \"./dist-cjs/submodules/sts/index.js\",\n      \"import\": \"./dist-es/submodules/sts/index.js\",\n      \"require\": \"./dist-cjs/submodules/sts/index.js\"\n    }\n  }\n}\n", "const F = \"required\", G = \"type\", H = \"fn\", I = \"argv\", J = \"ref\";\nconst a = false, b = true, c = \"booleanEquals\", d = \"stringEquals\", e = \"sigv4\", f = \"sts\", g = \"us-east-1\", h = \"endpoint\", i = \"https://sts.{Region}.{PartitionResult#dnsSuffix}\", j = \"tree\", k = \"error\", l = \"getAttr\", m = { [F]: false, [G]: \"String\" }, n = { [F]: true, \"default\": false, [G]: \"Boolean\" }, o = { [J]: \"Endpoint\" }, p = { [H]: \"isSet\", [I]: [{ [J]: \"Region\" }] }, q = { [J]: \"Region\" }, r = { [H]: \"aws.partition\", [I]: [q], \"assign\": \"PartitionResult\" }, s = { [J]: \"UseFIPS\" }, t = { [J]: \"UseDualStack\" }, u = { \"url\": \"https://sts.amazonaws.com\", \"properties\": { \"authSchemes\": [{ \"name\": e, \"signingName\": f, \"signingRegion\": g }] }, \"headers\": {} }, v = {}, w = { \"conditions\": [{ [H]: d, [I]: [q, \"aws-global\"] }], [h]: u, [G]: h }, x = { [H]: c, [I]: [s, true] }, y = { [H]: c, [I]: [t, true] }, z = { [H]: l, [I]: [{ [J]: \"PartitionResult\" }, \"supportsFIPS\"] }, A = { [J]: \"PartitionResult\" }, B = { [H]: c, [I]: [true, { [H]: l, [I]: [A, \"supportsDualStack\"] }] }, C = [{ [H]: \"isSet\", [I]: [o] }], D = [x], E = [y];\nconst _data = { version: \"1.0\", parameters: { Region: m, UseDualStack: n, UseFIPS: n, Endpoint: m, UseGlobalEndpoint: n }, rules: [{ conditions: [{ [H]: c, [I]: [{ [J]: \"UseGlobalEndpoint\" }, b] }, { [H]: \"not\", [I]: C }, p, r, { [H]: c, [I]: [s, a] }, { [H]: c, [I]: [t, a] }], rules: [{ conditions: [{ [H]: d, [I]: [q, \"ap-northeast-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"ap-south-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"ap-southeast-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"ap-southeast-2\"] }], endpoint: u, [G]: h }, w, { conditions: [{ [H]: d, [I]: [q, \"ca-central-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-central-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-north-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-west-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-west-2\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-west-3\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"sa-east-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, g] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"us-east-2\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"us-west-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"us-west-2\"] }], endpoint: u, [G]: h }, { endpoint: { url: i, properties: { authSchemes: [{ name: e, signingName: f, signingRegion: \"{Region}\" }] }, headers: v }, [G]: h }], [G]: j }, { conditions: C, rules: [{ conditions: D, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", [G]: k }, { conditions: E, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", [G]: k }, { endpoint: { url: o, properties: v, headers: v }, [G]: h }], [G]: j }, { conditions: [p], rules: [{ conditions: [r], rules: [{ conditions: [x, y], rules: [{ conditions: [{ [H]: c, [I]: [b, z] }, B], rules: [{ endpoint: { url: \"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: v, headers: v }, [G]: h }], [G]: j }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", [G]: k }], [G]: j }, { conditions: D, rules: [{ conditions: [{ [H]: c, [I]: [z, b] }], rules: [{ conditions: [{ [H]: d, [I]: [{ [H]: l, [I]: [A, \"name\"] }, \"aws-us-gov\"] }], endpoint: { url: \"https://sts.{Region}.amazonaws.com\", properties: v, headers: v }, [G]: h }, { endpoint: { url: \"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: v, headers: v }, [G]: h }], [G]: j }, { error: \"FIPS is enabled but this partition does not support FIPS\", [G]: k }], [G]: j }, { conditions: E, rules: [{ conditions: [B], rules: [{ endpoint: { url: \"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: v, headers: v }, [G]: h }], [G]: j }, { error: \"DualStack is enabled but this partition does not support DualStack\", [G]: k }], [G]: j }, w, { endpoint: { url: i, properties: v, headers: v }, [G]: h }], [G]: j }], [G]: j }, { error: \"Invalid Configuration: Missing Region\", [G]: k }] };\nexport const ruleSet = _data;\n", "import { awsEndpointFunctions } from \"@aws-sdk/util-endpoints\";\nimport { customEndpointFunctions, EndpointCache, resolveEndpoint } from \"@smithy/util-endpoints\";\nimport { ruleSet } from \"./ruleset\";\nconst cache = new EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\", \"UseGlobalEndpoint\"],\n});\nexport const defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => resolveEndpoint(ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\ncustomEndpointFunctions.aws = awsEndpointFunctions;\n", "import { AwsSdkSigV4Signer } from \"@aws-sdk/core\";\nimport { NoAuthSigner } from \"@smithy/core\";\nimport { NoOpLogger } from \"@smithy/smithy-client\";\nimport { parseUrl } from \"@smithy/url-parser\";\nimport { fromBase64, toBase64 } from \"@smithy/util-base64\";\nimport { fromUtf8, toUtf8 } from \"@smithy/util-utf8\";\nimport { defaultSTSHttpAuthSchemeProvider } from \"./auth/httpAuthSchemeProvider\";\nimport { defaultEndpointResolver } from \"./endpoint/endpointResolver\";\nexport const getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2011-06-15\",\n        base64Decoder: config?.base64Decoder ?? fromBase64,\n        base64Encoder: config?.base64Encoder ?? toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? defaultSTSHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new NoAuthSigner(),\n            },\n        ],\n        logger: config?.logger ?? new NoOpLogger(),\n        serviceId: config?.serviceId ?? \"STS\",\n        urlParser: config?.urlParser ?? parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? toUtf8,\n    };\n};\n", "import packageInfo from \"../../../package.json\";\nimport { Sha256 } from \"@aws-crypto/sha256-browser\";\nimport { createDefaultUserAgentProvider } from \"@aws-sdk/util-user-agent-browser\";\nimport { DEFAULT_USE_DUALSTACK_ENDPOINT, DEFAULT_USE_FIPS_ENDPOINT } from \"@smithy/config-resolver\";\nimport { Fetch<PERSON>ttpHand<PERSON> as RequestHandler, streamCollector } from \"@smithy/fetch-http-handler\";\nimport { invalidProvider } from \"@smithy/invalid-dependency\";\nimport { calculateBodyLength } from \"@smithy/util-body-length-browser\";\nimport { DEFAULT_MAX_ATTEMPTS, DEFAULT_RETRY_MODE } from \"@smithy/util-retry\";\nimport { getRuntimeConfig as getSharedRuntimeConfig } from \"./runtimeConfig.shared\";\nimport { loadConfigsForDefaultMode } from \"@smithy/smithy-client\";\nimport { resolveDefaultsModeConfig } from \"@smithy/util-defaults-mode-browser\";\nexport const getRuntimeConfig = (config) => {\n    const defaultsMode = resolveDefaultsModeConfig(config);\n    const defaultConfigProvider = () => defaultsMode().then(loadConfigsForDefaultMode);\n    const clientSharedValues = getSharedRuntimeConfig(config);\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"browser\",\n        defaultsMode,\n        bodyLengthChecker: config?.bodyLengthChecker ?? calculateBodyLength,\n        credentialDefaultProvider: config?.credentialDefaultProvider ?? ((_) => () => Promise.reject(new Error(\"Credential is missing\"))),\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            createDefaultUserAgentProvider({ serviceId: clientSharedValues.serviceId, clientVersion: packageInfo.version }),\n        maxAttempts: config?.maxAttempts ?? DEFAULT_MAX_ATTEMPTS,\n        region: config?.region ?? invalidProvider(\"Region is missing\"),\n        requestHandler: RequestHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ?? (async () => (await defaultConfigProvider()).retryMode || DEFAULT_RETRY_MODE),\n        sha256: config?.sha256 ?? Sha256,\n        streamCollector: config?.streamCollector ?? streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (() => Promise.resolve(DEFAULT_USE_DUALSTACK_ENDPOINT)),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (() => Promise.resolve(DEFAULT_USE_FIPS_ENDPOINT)),\n    };\n};\n", "export const getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nexport const resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n", "import { getAwsRegionExtensionConfiguration, resolveAwsRegionExtensionConfiguration, } from \"@aws-sdk/region-config-resolver\";\nimport { getHttpHandlerExtensionConfiguration, resolveHttpHandlerRuntimeConfig } from \"@smithy/protocol-http\";\nimport { getDefaultExtensionConfiguration, resolveDefaultRuntimeConfig } from \"@smithy/smithy-client\";\nimport { getHttpAuthExtensionConfiguration, resolveHttpAuthRuntimeConfig } from \"./auth/httpAuthExtensionConfiguration\";\nexport const resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = Object.assign(getAwsRegionExtensionConfiguration(runtimeConfig), getDefaultExtensionConfiguration(runtimeConfig), getHttpHandlerExtensionConfiguration(runtimeConfig), getHttpAuthExtensionConfiguration(runtimeConfig));\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return Object.assign(runtimeConfig, resolveAwsRegionExtensionConfiguration(extensionConfiguration), resolveDefaultRuntimeConfig(extensionConfiguration), resolveHttpHandlerRuntimeConfig(extensionConfiguration), resolveHttpAuthRuntimeConfig(extensionConfiguration));\n};\n", "import { getHostHeaderPlugin, resolveHostHeaderConfig, } from \"@aws-sdk/middleware-host-header\";\nimport { getLoggerPlugin } from \"@aws-sdk/middleware-logger\";\nimport { getRecursionDetectionPlugin } from \"@aws-sdk/middleware-recursion-detection\";\nimport { getUserAgentPlugin, resolveUserAgentConfig, } from \"@aws-sdk/middleware-user-agent\";\nimport { resolveRegionConfig } from \"@smithy/config-resolver\";\nimport { DefaultIdentityProviderConfig, getHttpAuthSchemeEndpointRuleSetPlugin, getHttpSigningPlugin, } from \"@smithy/core\";\nimport { getContentLengthPlugin } from \"@smithy/middleware-content-length\";\nimport { resolveEndpointConfig } from \"@smithy/middleware-endpoint\";\nimport { getRetryPlugin, resolveRetryConfig } from \"@smithy/middleware-retry\";\nimport { Client as __Client, } from \"@smithy/smithy-client\";\nimport { defaultSTSHttpAuthSchemeParametersProvider, resolveHttpAuthSchemeConfig, } from \"./auth/httpAuthSchemeProvider\";\nimport { resolveClientEndpointParameters, } from \"./endpoint/EndpointParameters\";\nimport { getRuntimeConfig as __getRuntimeConfig } from \"./runtimeConfig\";\nimport { resolveRuntimeExtensions } from \"./runtimeExtensions\";\nexport { __Client };\nexport class STSClient extends __Client {\n    config;\n    constructor(...[configuration]) {\n        const _config_0 = __getRuntimeConfig(configuration || {});\n        super(_config_0);\n        this.initConfig = _config_0;\n        const _config_1 = resolveClientEndpointParameters(_config_0);\n        const _config_2 = resolveUserAgentConfig(_config_1);\n        const _config_3 = resolveRetryConfig(_config_2);\n        const _config_4 = resolveRegionConfig(_config_3);\n        const _config_5 = resolveHostHeaderConfig(_config_4);\n        const _config_6 = resolveEndpointConfig(_config_5);\n        const _config_7 = resolveHttpAuthSchemeConfig(_config_6);\n        const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);\n        this.config = _config_8;\n        this.middlewareStack.use(getUserAgentPlugin(this.config));\n        this.middlewareStack.use(getRetryPlugin(this.config));\n        this.middlewareStack.use(getContentLengthPlugin(this.config));\n        this.middlewareStack.use(getHostHeaderPlugin(this.config));\n        this.middlewareStack.use(getLoggerPlugin(this.config));\n        this.middlewareStack.use(getRecursionDetectionPlugin(this.config));\n        this.middlewareStack.use(getHttpAuthSchemeEndpointRuleSetPlugin(this.config, {\n            httpAuthSchemeParametersProvider: defaultSTSHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use(getHttpSigningPlugin(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n", "import { ServiceException as __ServiceException, } from \"@smithy/smithy-client\";\nexport { __ServiceException };\nexport class STSServiceException extends __ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, STSServiceException.prototype);\n    }\n}\n", "import { SENSITIVE_STRING } from \"@smithy/smithy-client\";\nimport { STSServiceException as __BaseException } from \"./STSServiceException\";\nexport const CredentialsFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.SecretAccessKey && { SecretAccessKey: SENSITIVE_STRING }),\n});\nexport const AssumeRoleResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Credentials && { Credentials: CredentialsFilterSensitiveLog(obj.Credentials) }),\n});\nexport class ExpiredTokenException extends __BaseException {\n    name = \"ExpiredTokenException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ExpiredTokenException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ExpiredTokenException.prototype);\n    }\n}\nexport class MalformedPolicyDocumentException extends __BaseException {\n    name = \"MalformedPolicyDocumentException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"MalformedPolicyDocumentException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, MalformedPolicyDocumentException.prototype);\n    }\n}\nexport class PackedPolicyTooLargeException extends __BaseException {\n    name = \"PackedPolicyTooLargeException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"PackedPolicyTooLargeException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, PackedPolicyTooLargeException.prototype);\n    }\n}\nexport class RegionDisabledException extends __BaseException {\n    name = \"RegionDisabledException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"RegionDisabledException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, RegionDisabledException.prototype);\n    }\n}\nexport class IDPRejectedClaimException extends __BaseException {\n    name = \"IDPRejectedClaimException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"IDPRejectedClaimException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, IDPRejectedClaimException.prototype);\n    }\n}\nexport class InvalidIdentityTokenException extends __BaseException {\n    name = \"InvalidIdentityTokenException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"InvalidIdentityTokenException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidIdentityTokenException.prototype);\n    }\n}\nexport const AssumeRoleWithWebIdentityRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.WebIdentityToken && { WebIdentityToken: SENSITIVE_STRING }),\n});\nexport const AssumeRoleWithWebIdentityResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Credentials && { Credentials: CredentialsFilterSensitiveLog(obj.Credentials) }),\n});\nexport class IDPCommunicationErrorException extends __BaseException {\n    name = \"IDPCommunicationErrorException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"IDPCommunicationErrorException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, IDPCommunicationErrorException.prototype);\n    }\n}\n", "import { parseXmlBody as parseBody, parseXmlErrorBody as parseErrorBody } from \"@aws-sdk/core\";\nimport { HttpRequest as __HttpRequest } from \"@smithy/protocol-http\";\nimport { collectBody, decorateServiceException as __decorateServiceException, expectNonNull as __expectNonNull, expectString as __expectString, extendedEncodeURIComponent as __extendedEncodeURIComponent, parseRfc3339DateTimeWithOffset as __parseRfc3339DateTimeWithOffset, strictParseInt32 as __strictParseInt32, withBaseException, } from \"@smithy/smithy-client\";\nimport { ExpiredTokenException, IDPCommunicationErrorException, IDPRejectedClaimException, InvalidIdentityTokenException, MalformedPolicyDocumentException, PackedPolicyTooLargeException, RegionDisabledException, } from \"../models/models_0\";\nimport { STSServiceException as __BaseException } from \"../models/STSServiceException\";\nexport const se_AssumeRoleCommand = async (input, context) => {\n    const headers = SHARED_HEADERS;\n    let body;\n    body = buildFormUrlencodedString({\n        ...se_AssumeRoleRequest(input, context),\n        [_A]: _AR,\n        [_V]: _,\n    });\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_AssumeRoleWithWebIdentityCommand = async (input, context) => {\n    const headers = SHARED_HEADERS;\n    let body;\n    body = buildFormUrlencodedString({\n        ...se_AssumeRoleWithWebIdentityRequest(input, context),\n        [_A]: _ARWWI,\n        [_V]: _,\n    });\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const de_AssumeRoleCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_AssumeRoleResponse(data.AssumeRoleResult, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_AssumeRoleWithWebIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_AssumeRoleWithWebIdentityResponse(data.AssumeRoleWithWebIdentityResult, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await parseErrorBody(output.body, context),\n    };\n    const errorCode = loadQueryErrorCode(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"ExpiredTokenException\":\n        case \"com.amazonaws.sts#ExpiredTokenException\":\n            throw await de_ExpiredTokenExceptionRes(parsedOutput, context);\n        case \"MalformedPolicyDocument\":\n        case \"com.amazonaws.sts#MalformedPolicyDocumentException\":\n            throw await de_MalformedPolicyDocumentExceptionRes(parsedOutput, context);\n        case \"PackedPolicyTooLarge\":\n        case \"com.amazonaws.sts#PackedPolicyTooLargeException\":\n            throw await de_PackedPolicyTooLargeExceptionRes(parsedOutput, context);\n        case \"RegionDisabledException\":\n        case \"com.amazonaws.sts#RegionDisabledException\":\n            throw await de_RegionDisabledExceptionRes(parsedOutput, context);\n        case \"IDPCommunicationError\":\n        case \"com.amazonaws.sts#IDPCommunicationErrorException\":\n            throw await de_IDPCommunicationErrorExceptionRes(parsedOutput, context);\n        case \"IDPRejectedClaim\":\n        case \"com.amazonaws.sts#IDPRejectedClaimException\":\n            throw await de_IDPRejectedClaimExceptionRes(parsedOutput, context);\n        case \"InvalidIdentityToken\":\n        case \"com.amazonaws.sts#InvalidIdentityTokenException\":\n            throw await de_InvalidIdentityTokenExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody: parsedBody.Error,\n                errorCode,\n            });\n    }\n};\nconst de_ExpiredTokenExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_ExpiredTokenException(body.Error, context);\n    const exception = new ExpiredTokenException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_IDPCommunicationErrorExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_IDPCommunicationErrorException(body.Error, context);\n    const exception = new IDPCommunicationErrorException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_IDPRejectedClaimExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_IDPRejectedClaimException(body.Error, context);\n    const exception = new IDPRejectedClaimException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InvalidIdentityTokenExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_InvalidIdentityTokenException(body.Error, context);\n    const exception = new InvalidIdentityTokenException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_MalformedPolicyDocumentExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_MalformedPolicyDocumentException(body.Error, context);\n    const exception = new MalformedPolicyDocumentException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_PackedPolicyTooLargeExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_PackedPolicyTooLargeException(body.Error, context);\n    const exception = new PackedPolicyTooLargeException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_RegionDisabledExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_RegionDisabledException(body.Error, context);\n    const exception = new RegionDisabledException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst se_AssumeRoleRequest = (input, context) => {\n    const entries = {};\n    if (input[_RA] != null) {\n        entries[_RA] = input[_RA];\n    }\n    if (input[_RSN] != null) {\n        entries[_RSN] = input[_RSN];\n    }\n    if (input[_PA] != null) {\n        const memberEntries = se_policyDescriptorListType(input[_PA], context);\n        if (input[_PA]?.length === 0) {\n            entries.PolicyArns = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `PolicyArns.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_P] != null) {\n        entries[_P] = input[_P];\n    }\n    if (input[_DS] != null) {\n        entries[_DS] = input[_DS];\n    }\n    if (input[_T] != null) {\n        const memberEntries = se_tagListType(input[_T], context);\n        if (input[_T]?.length === 0) {\n            entries.Tags = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `Tags.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_TTK] != null) {\n        const memberEntries = se_tagKeyListType(input[_TTK], context);\n        if (input[_TTK]?.length === 0) {\n            entries.TransitiveTagKeys = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `TransitiveTagKeys.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_EI] != null) {\n        entries[_EI] = input[_EI];\n    }\n    if (input[_SN] != null) {\n        entries[_SN] = input[_SN];\n    }\n    if (input[_TC] != null) {\n        entries[_TC] = input[_TC];\n    }\n    if (input[_SI] != null) {\n        entries[_SI] = input[_SI];\n    }\n    if (input[_PC] != null) {\n        const memberEntries = se_ProvidedContextsListType(input[_PC], context);\n        if (input[_PC]?.length === 0) {\n            entries.ProvidedContexts = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `ProvidedContexts.${key}`;\n            entries[loc] = value;\n        });\n    }\n    return entries;\n};\nconst se_AssumeRoleWithWebIdentityRequest = (input, context) => {\n    const entries = {};\n    if (input[_RA] != null) {\n        entries[_RA] = input[_RA];\n    }\n    if (input[_RSN] != null) {\n        entries[_RSN] = input[_RSN];\n    }\n    if (input[_WIT] != null) {\n        entries[_WIT] = input[_WIT];\n    }\n    if (input[_PI] != null) {\n        entries[_PI] = input[_PI];\n    }\n    if (input[_PA] != null) {\n        const memberEntries = se_policyDescriptorListType(input[_PA], context);\n        if (input[_PA]?.length === 0) {\n            entries.PolicyArns = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `PolicyArns.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_P] != null) {\n        entries[_P] = input[_P];\n    }\n    if (input[_DS] != null) {\n        entries[_DS] = input[_DS];\n    }\n    return entries;\n};\nconst se_policyDescriptorListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        const memberEntries = se_PolicyDescriptorType(entry, context);\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            entries[`member.${counter}.${key}`] = value;\n        });\n        counter++;\n    }\n    return entries;\n};\nconst se_PolicyDescriptorType = (input, context) => {\n    const entries = {};\n    if (input[_a] != null) {\n        entries[_a] = input[_a];\n    }\n    return entries;\n};\nconst se_ProvidedContext = (input, context) => {\n    const entries = {};\n    if (input[_PAr] != null) {\n        entries[_PAr] = input[_PAr];\n    }\n    if (input[_CA] != null) {\n        entries[_CA] = input[_CA];\n    }\n    return entries;\n};\nconst se_ProvidedContextsListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        const memberEntries = se_ProvidedContext(entry, context);\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            entries[`member.${counter}.${key}`] = value;\n        });\n        counter++;\n    }\n    return entries;\n};\nconst se_Tag = (input, context) => {\n    const entries = {};\n    if (input[_K] != null) {\n        entries[_K] = input[_K];\n    }\n    if (input[_Va] != null) {\n        entries[_Va] = input[_Va];\n    }\n    return entries;\n};\nconst se_tagKeyListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        entries[`member.${counter}`] = entry;\n        counter++;\n    }\n    return entries;\n};\nconst se_tagListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        const memberEntries = se_Tag(entry, context);\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            entries[`member.${counter}.${key}`] = value;\n        });\n        counter++;\n    }\n    return entries;\n};\nconst de_AssumedRoleUser = (output, context) => {\n    const contents = {};\n    if (output[_ARI] != null) {\n        contents[_ARI] = __expectString(output[_ARI]);\n    }\n    if (output[_Ar] != null) {\n        contents[_Ar] = __expectString(output[_Ar]);\n    }\n    return contents;\n};\nconst de_AssumeRoleResponse = (output, context) => {\n    const contents = {};\n    if (output[_C] != null) {\n        contents[_C] = de_Credentials(output[_C], context);\n    }\n    if (output[_ARU] != null) {\n        contents[_ARU] = de_AssumedRoleUser(output[_ARU], context);\n    }\n    if (output[_PPS] != null) {\n        contents[_PPS] = __strictParseInt32(output[_PPS]);\n    }\n    if (output[_SI] != null) {\n        contents[_SI] = __expectString(output[_SI]);\n    }\n    return contents;\n};\nconst de_AssumeRoleWithWebIdentityResponse = (output, context) => {\n    const contents = {};\n    if (output[_C] != null) {\n        contents[_C] = de_Credentials(output[_C], context);\n    }\n    if (output[_SFWIT] != null) {\n        contents[_SFWIT] = __expectString(output[_SFWIT]);\n    }\n    if (output[_ARU] != null) {\n        contents[_ARU] = de_AssumedRoleUser(output[_ARU], context);\n    }\n    if (output[_PPS] != null) {\n        contents[_PPS] = __strictParseInt32(output[_PPS]);\n    }\n    if (output[_Pr] != null) {\n        contents[_Pr] = __expectString(output[_Pr]);\n    }\n    if (output[_Au] != null) {\n        contents[_Au] = __expectString(output[_Au]);\n    }\n    if (output[_SI] != null) {\n        contents[_SI] = __expectString(output[_SI]);\n    }\n    return contents;\n};\nconst de_Credentials = (output, context) => {\n    const contents = {};\n    if (output[_AKI] != null) {\n        contents[_AKI] = __expectString(output[_AKI]);\n    }\n    if (output[_SAK] != null) {\n        contents[_SAK] = __expectString(output[_SAK]);\n    }\n    if (output[_ST] != null) {\n        contents[_ST] = __expectString(output[_ST]);\n    }\n    if (output[_E] != null) {\n        contents[_E] = __expectNonNull(__parseRfc3339DateTimeWithOffset(output[_E]));\n    }\n    return contents;\n};\nconst de_ExpiredTokenException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst de_IDPCommunicationErrorException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst de_IDPRejectedClaimException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst de_InvalidIdentityTokenException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst de_MalformedPolicyDocumentException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst de_PackedPolicyTooLargeException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst de_RegionDisabledException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = __expectString(output[_m]);\n    }\n    return contents;\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));\nconst throwDefaultError = withBaseException(__BaseException);\nconst buildHttpRpcRequest = async (context, headers, path, resolvedHostname, body) => {\n    const { hostname, protocol = \"https\", port, path: basePath } = await context.endpoint();\n    const contents = {\n        protocol,\n        hostname,\n        port,\n        method: \"POST\",\n        path: basePath.endsWith(\"/\") ? basePath.slice(0, -1) + path : basePath + path,\n        headers,\n    };\n    if (resolvedHostname !== undefined) {\n        contents.hostname = resolvedHostname;\n    }\n    if (body !== undefined) {\n        contents.body = body;\n    }\n    return new __HttpRequest(contents);\n};\nconst SHARED_HEADERS = {\n    \"content-type\": \"application/x-www-form-urlencoded\",\n};\nconst _ = \"2011-06-15\";\nconst _A = \"Action\";\nconst _AKI = \"AccessKeyId\";\nconst _AR = \"AssumeRole\";\nconst _ARI = \"AssumedRoleId\";\nconst _ARU = \"AssumedRoleUser\";\nconst _ARWWI = \"AssumeRoleWithWebIdentity\";\nconst _Ar = \"Arn\";\nconst _Au = \"Audience\";\nconst _C = \"Credentials\";\nconst _CA = \"ContextAssertion\";\nconst _DS = \"DurationSeconds\";\nconst _E = \"Expiration\";\nconst _EI = \"ExternalId\";\nconst _K = \"Key\";\nconst _P = \"Policy\";\nconst _PA = \"PolicyArns\";\nconst _PAr = \"ProviderArn\";\nconst _PC = \"ProvidedContexts\";\nconst _PI = \"ProviderId\";\nconst _PPS = \"PackedPolicySize\";\nconst _Pr = \"Provider\";\nconst _RA = \"RoleArn\";\nconst _RSN = \"RoleSessionName\";\nconst _SAK = \"SecretAccessKey\";\nconst _SFWIT = \"SubjectFromWebIdentityToken\";\nconst _SI = \"SourceIdentity\";\nconst _SN = \"SerialNumber\";\nconst _ST = \"SessionToken\";\nconst _T = \"Tags\";\nconst _TC = \"TokenCode\";\nconst _TTK = \"TransitiveTagKeys\";\nconst _V = \"Version\";\nconst _Va = \"Value\";\nconst _WIT = \"WebIdentityToken\";\nconst _a = \"arn\";\nconst _m = \"message\";\nconst buildFormUrlencodedString = (formEntries) => Object.entries(formEntries)\n    .map(([key, value]) => __extendedEncodeURIComponent(key) + \"=\" + __extendedEncodeURIComponent(value))\n    .join(\"&\");\nconst loadQueryErrorCode = (output, data) => {\n    if (data.Error?.Code !== undefined) {\n        return data.Error.Code;\n    }\n    if (output.statusCode == 404) {\n        return \"NotFound\";\n    }\n};\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { AssumeRoleResponseFilterSensitiveLog } from \"../models/models_0\";\nimport { de_AssumeRoleCommand, se_AssumeRoleCommand } from \"../protocols/Aws_query\";\nexport { $Command };\nexport class AssumeRoleCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSSecurityTokenServiceV20110615\", \"AssumeRole\", {})\n    .n(\"STSClient\", \"AssumeRoleCommand\")\n    .f(void 0, AssumeRoleResponseFilterSensitiveLog)\n    .ser(se_AssumeRoleCommand)\n    .de(de_AssumeRoleCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { AssumeRoleWithWebIdentityRequestFilterSensitiveLog, AssumeRoleWithWebIdentityResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_AssumeRoleWithWebIdentityCommand, se_AssumeRoleWithWebIdentityCommand } from \"../protocols/Aws_query\";\nexport { $Command };\nexport class AssumeRoleWithWebIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSSecurityTokenServiceV20110615\", \"AssumeRoleWithWebIdentity\", {})\n    .n(\"STSClient\", \"AssumeRoleWithWebIdentityCommand\")\n    .f(AssumeRoleWithWebIdentityRequestFilterSensitiveLog, AssumeRoleWithWebIdentityResponseFilterSensitiveLog)\n    .ser(se_AssumeRoleWithWebIdentityCommand)\n    .de(de_AssumeRoleWithWebIdentityCommand)\n    .build() {\n}\n", "import { createAggregatedClient } from \"@smithy/smithy-client\";\nimport { AssumeRoleCommand } from \"./commands/AssumeRoleCommand\";\nimport { AssumeRoleWithWebIdentityCommand, } from \"./commands/AssumeRoleWithWebIdentityCommand\";\nimport { STSClient } from \"./STSClient\";\nconst commands = {\n    AssumeRoleCommand,\n    AssumeRoleWithWebIdentityCommand,\n};\nexport class STS extends STSClient {\n}\ncreateAggregatedClient(commands, STS);\n", "export * from \"./AssumeRoleCommand\";\nexport * from \"./AssumeRoleWithWebIdentityCommand\";\n", "export * from \"./models_0\";\n", "import { setCredentialFeature } from \"@aws-sdk/core/client\";\nimport { AssumeRoleCommand } from \"./commands/AssumeRoleCommand\";\nimport { AssumeRoleWithWebIdentityCommand, } from \"./commands/AssumeRoleWithWebIdentityCommand\";\nconst ASSUME_ROLE_DEFAULT_REGION = \"us-east-1\";\nconst getAccountIdFromAssumedRoleUser = (assumedRoleUser) => {\n    if (typeof assumedRoleUser?.Arn === \"string\") {\n        const arnComponents = assumedRoleUser.Arn.split(\":\");\n        if (arnComponents.length > 4 && arnComponents[4] !== \"\") {\n            return arnComponents[4];\n        }\n    }\n    return undefined;\n};\nconst resolveRegion = async (_region, _parentRegion, credentialProviderLogger) => {\n    const region = typeof _region === \"function\" ? await _region() : _region;\n    const parentRegion = typeof _parentRegion === \"function\" ? await _parentRegion() : _parentRegion;\n    credentialProviderLogger?.debug?.(\"@aws-sdk/client-sts::resolveRegion\", \"accepting first of:\", `${region} (provider)`, `${parentRegion} (parent client)`, `${ASSUME_ROLE_DEFAULT_REGION} (STS default)`);\n    return region ?? parentRegion ?? ASSUME_ROLE_DEFAULT_REGION;\n};\nexport const getDefaultRoleAssumer = (stsOptions, STSClient) => {\n    let stsClient;\n    let closureSourceCreds;\n    return async (sourceCreds, params) => {\n        closureSourceCreds = sourceCreds;\n        if (!stsClient) {\n            const { logger = stsOptions?.parentClientConfig?.logger, region, requestHandler = stsOptions?.parentClientConfig?.requestHandler, credentialProviderLogger, } = stsOptions;\n            const resolvedRegion = await resolveRegion(region, stsOptions?.parentClientConfig?.region, credentialProviderLogger);\n            const isCompatibleRequestHandler = !isH2(requestHandler);\n            stsClient = new STSClient({\n                profile: stsOptions?.parentClientConfig?.profile,\n                credentialDefaultProvider: () => async () => closureSourceCreds,\n                region: resolvedRegion,\n                requestHandler: isCompatibleRequestHandler ? requestHandler : undefined,\n                logger: logger,\n            });\n        }\n        const { Credentials, AssumedRoleUser } = await stsClient.send(new AssumeRoleCommand(params));\n        if (!Credentials || !Credentials.AccessKeyId || !Credentials.SecretAccessKey) {\n            throw new Error(`Invalid response from STS.assumeRole call with role ${params.RoleArn}`);\n        }\n        const accountId = getAccountIdFromAssumedRoleUser(AssumedRoleUser);\n        const credentials = {\n            accessKeyId: Credentials.AccessKeyId,\n            secretAccessKey: Credentials.SecretAccessKey,\n            sessionToken: Credentials.SessionToken,\n            expiration: Credentials.Expiration,\n            ...(Credentials.CredentialScope && { credentialScope: Credentials.CredentialScope }),\n            ...(accountId && { accountId }),\n        };\n        setCredentialFeature(credentials, \"CREDENTIALS_STS_ASSUME_ROLE\", \"i\");\n        return credentials;\n    };\n};\nexport const getDefaultRoleAssumerWithWebIdentity = (stsOptions, STSClient) => {\n    let stsClient;\n    return async (params) => {\n        if (!stsClient) {\n            const { logger = stsOptions?.parentClientConfig?.logger, region, requestHandler = stsOptions?.parentClientConfig?.requestHandler, credentialProviderLogger, } = stsOptions;\n            const resolvedRegion = await resolveRegion(region, stsOptions?.parentClientConfig?.region, credentialProviderLogger);\n            const isCompatibleRequestHandler = !isH2(requestHandler);\n            stsClient = new STSClient({\n                profile: stsOptions?.parentClientConfig?.profile,\n                region: resolvedRegion,\n                requestHandler: isCompatibleRequestHandler ? requestHandler : undefined,\n                logger: logger,\n            });\n        }\n        const { Credentials, AssumedRoleUser } = await stsClient.send(new AssumeRoleWithWebIdentityCommand(params));\n        if (!Credentials || !Credentials.AccessKeyId || !Credentials.SecretAccessKey) {\n            throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${params.RoleArn}`);\n        }\n        const accountId = getAccountIdFromAssumedRoleUser(AssumedRoleUser);\n        const credentials = {\n            accessKeyId: Credentials.AccessKeyId,\n            secretAccessKey: Credentials.SecretAccessKey,\n            sessionToken: Credentials.SessionToken,\n            expiration: Credentials.Expiration,\n            ...(Credentials.CredentialScope && { credentialScope: Credentials.CredentialScope }),\n            ...(accountId && { accountId }),\n        };\n        if (accountId) {\n            setCredentialFeature(credentials, \"RESOLVED_ACCOUNT_ID\", \"T\");\n        }\n        setCredentialFeature(credentials, \"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID\", \"k\");\n        return credentials;\n    };\n};\nexport const decorateDefaultCredentialProvider = (provider) => (input) => provider({\n    roleAssumer: getDefaultRoleAssumer(input, input.stsClientCtor),\n    roleAssumerWithWebIdentity: getDefaultRoleAssumerWithWebIdentity(input, input.stsClientCtor),\n    ...input,\n});\nconst isH2 = (requestHandler) => {\n    return requestHandler?.metadata?.handlerProtocol === \"h2\";\n};\n", "import { getDefaultRoleAssumer as StsGetDefaultRoleAssumer, getDefaultRoleAssumerWithWebIdentity as StsGetDefaultRoleAssumerWithWebIdentity, } from \"./defaultStsRoleAssumers\";\nimport { STSClient } from \"./STSClient\";\nconst getCustomizableStsClientCtor = (baseCtor, customizations) => {\n    if (!customizations)\n        return baseCtor;\n    else\n        return class CustomizableSTSClient extends baseCtor {\n            constructor(config) {\n                super(config);\n                for (const customization of customizations) {\n                    this.middlewareStack.use(customization);\n                }\n            }\n        };\n};\nexport const getDefaultRoleAssumer = (stsOptions = {}, stsPlugins) => StsGetDefaultRoleAssumer(stsOptions, getCustomizableStsClientCtor(STSClient, stsPlugins));\nexport const getDefaultRoleAssumerWithWebIdentity = (stsOptions = {}, stsPlugins) => StsGetDefaultRoleAssumerWithWebIdentity(stsOptions, getCustomizableStsClientCtor(STSClient, stsPlugins));\nexport const decorateDefaultCredentialProvider = (provider) => (input) => provider({\n    roleAssumer: getDefaultRoleAssumer(input),\n    roleAssumerWithWebIdentity: getDefaultRoleAssumerWithWebIdentity(input),\n    ...input,\n});\n", "export * from \"./STSClient\";\nexport * from \"./STS\";\nexport * from \"./commands\";\nexport * from \"./models\";\nexport * from \"./defaultRoleAssumers\";\nexport { STSServiceException } from \"./models/STSServiceException\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,SAAS,iCAAiC,gBAAgB;AACtD,SAAO;AAAA,IACH,UAAU;AAAA,IACV,mBAAmB;AAAA,MACf,MAAM;AAAA,MACN,QAAQ,eAAe;AAAA,IAC3B;AAAA,IACA,qBAAqB,CAAC,QAAQ,aAAa;AAAA,MACvC,mBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,oCAAoC,gBAAgB;AACzD,SAAO;AAAA,IACH,UAAU;AAAA,EACd;AACJ;AA/BA,IAGa,4CA6BA,kCAaA,sBAGA;AAhDb;AAAA;AAAA,IAAAA;AACA,IAAAA;AACA;AACO,IAAM,6CAA6C,OAAO,QAAQ,SAAS,UAAU;AACxF,aAAO;AAAA,QACH,WAAW,iBAAiB,OAAO,EAAE;AAAA,QACrC,QAAS,MAAM,kBAAkB,OAAO,MAAM,EAAE,MAC3C,MAAM;AACH,gBAAM,IAAI,MAAM,yDAAyD;AAAA,QAC7E,GAAG;AAAA,MACX;AAAA,IACJ;AAqBO,IAAM,mCAAmC,CAAC,mBAAmB;AAChE,YAAM,UAAU,CAAC;AACjB,cAAQ,eAAe,WAAW;AAAA,QAC9B,KAAK,6BAA6B;AAC9B,kBAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,QACJ;AAAA,QACA,SAAS;AACL,kBAAQ,KAAK,iCAAiC,cAAc,CAAC;AAAA,QACjE;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACO,IAAM,uBAAuB,CAAC,UAAU,OAAO,OAAO,OAAO;AAAA,MAChE,eAAe;AAAA,IACnB,CAAC;AACM,IAAM,8BAA8B,CAAC,WAAW;AACnD,YAAM,WAAW,qBAAqB,MAAM;AAC5C,YAAM,WAAW,yBAAyB,QAAQ;AAClD,aAAO,OAAO,OAAO,UAAU;AAAA,QAC3B,sBAAsB,kBAAkB,OAAO,wBAAwB,CAAC,CAAC;AAAA,MAC7E,CAAC;AAAA,IACL;AAAA;AAAA;;;ACtDA,IAAa,iCAQA;AARb;AAAA;AAAO,IAAM,kCAAkC,CAAC,YAAY;AACxD,aAAO,OAAO,OAAO,SAAS;AAAA,QAC1B,sBAAsB,QAAQ,wBAAwB;AAAA,QACtD,iBAAiB,QAAQ,mBAAmB;AAAA,QAC5C,mBAAmB,QAAQ,qBAAqB;AAAA,QAChD,oBAAoB;AAAA,MACxB,CAAC;AAAA,IACL;AACO,IAAM,eAAe;AAAA,MACxB,mBAAmB,EAAE,MAAM,iBAAiB,MAAM,oBAAoB;AAAA,MACtE,SAAS,EAAE,MAAM,iBAAiB,MAAM,kBAAkB;AAAA,MAC1D,UAAU,EAAE,MAAM,iBAAiB,MAAM,WAAW;AAAA,MACpD,QAAQ,EAAE,MAAM,iBAAiB,MAAM,SAAS;AAAA,MAChD,cAAc,EAAE,MAAM,iBAAiB,MAAM,uBAAuB;AAAA,IACxE;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,OAAS;AAAA,MACT,SAAW;AAAA,QACT,OAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,sBAAsB;AAAA,QACtB,eAAe;AAAA,QACf,yBAAyB;AAAA,QACzB,OAAS;AAAA,QACT,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,cAAc;AAAA,MAChB;AAAA,MACA,SAAW;AAAA,QACT,MAAQ;AAAA,MACV;AAAA,MACA,QAAU;AAAA,QACR,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,SAAW;AAAA,MACX,cAAgB;AAAA,QACd,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,mCAAmC;AAAA,QACnC,8BAA8B;AAAA,QAC9B,2CAA2C;AAAA,QAC3C,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,kBAAkB;AAAA,QAClB,2BAA2B;AAAA,QAC3B,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,2BAA2B;AAAA,QAC3B,gBAAgB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,qBAAqB;AAAA,QACrB,8BAA8B;AAAA,QAC9B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,4BAA4B;AAAA,QAC5B,4BAA4B;AAAA,QAC5B,4BAA4B;AAAA,QAC5B,gCAAgC;AAAA,QAChC,6BAA6B;AAAA,QAC7B,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,QACvB,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,sCAAsC;AAAA,QACtC,mCAAmC;AAAA,QACnC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,OAAS;AAAA,MACX;AAAA,MACA,iBAAmB;AAAA,QACjB,cAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,QAAU;AAAA,QACV,YAAc;AAAA,MAChB;AAAA,MACA,eAAiB;AAAA,QACf,QAAQ;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT,+CAA+C;AAAA,QAC/C,0CAA0C;AAAA,MAC5C;AAAA,MACA,gBAAgB,CAAC;AAAA,MACjB,UAAY;AAAA,MACZ,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,WAAa;AAAA,MACf;AAAA,MACA,SAAW;AAAA,QACT,cAAc;AAAA,UACZ,OAAS;AAAA,UACT,QAAU;AAAA,UACV,MAAQ;AAAA,UACR,QAAU;AAAA,UACV,SAAW;AAAA,QACb;AAAA,QACA,SAAS;AAAA,UACP,OAAS;AAAA,UACT,QAAU;AAAA,UACV,MAAQ;AAAA,UACR,QAAU;AAAA,UACV,SAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AClHA,IAAM,GAAgB,GAAY,GAAU,GAAY,GAClD,GAAW,GAAU,GAAqB,GAAoB,GAAa,GAAW,GAAiB,GAAgB,GAAwD,GAAY,GAAa,GAAe,GAAmC,GAAqD,GAAyB,GAAgD,GAAuB,GAAqE,GAAwB,GAA6B,GAAmJ,GAAQ,GAA4E,GAAgC,GAAgC,GAAmE,GAAgC,GAAwE,GAAkC,GAAS,GACtgC,OACO;AAHb;AAAA;AAAA,IAAM,IAAI;AAAV,IAAsB,IAAI;AAA1B,IAAkC,IAAI;AAAtC,IAA4C,IAAI;AAAhD,IAAwD,IAAI;AAC5D,IAAM,IAAI;AAAV,IAAiB,IAAI;AAArB,IAA2B,IAAI;AAA/B,IAAgD,IAAI;AAApD,IAAoE,IAAI;AAAxE,IAAiF,IAAI;AAArF,IAA4F,IAAI;AAAhG,IAA6G,IAAI;AAAjH,IAA6H,IAAI;AAAjI,IAAqL,IAAI;AAAzL,IAAiM,IAAI;AAArM,IAA8M,IAAI;AAAlN,IAA6N,IAAI,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,SAAS;AAA7P,IAAgQ,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,WAAW,OAAO,CAAC,CAAC,GAAG,UAAU;AAAlT,IAAqT,IAAI,EAAE,CAAC,CAAC,GAAG,WAAW;AAA3U,IAA8U,IAAI,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE;AAA3X,IAA8X,IAAI,EAAE,CAAC,CAAC,GAAG,SAAS;AAAlZ,IAAqZ,IAAI,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,kBAAkB;AAAvd,IAA0d,IAAI,EAAE,CAAC,CAAC,GAAG,UAAU;AAA/e,IAAkf,IAAI,EAAE,CAAC,CAAC,GAAG,eAAe;AAA5gB,IAA+gB,IAAI,EAAE,OAAO,6BAA6B,cAAc,EAAE,eAAe,CAAC,EAAE,QAAQ,GAAG,eAAe,GAAG,iBAAiB,EAAE,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;AAA/pB,IAAkqB,IAAI,CAAC;AAAvqB,IAA0qB,IAAI,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE;AAAnvB,IAAsvB,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;AAAnxB,IAAsxB,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;AAAnzB,IAAszB,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,kBAAkB,GAAG,cAAc,EAAE;AAAt3B,IAAy3B,IAAI,EAAE,CAAC,CAAC,GAAG,kBAAkB;AAAt5B,IAAy5B,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,mBAAmB,EAAE,CAAC,EAAE;AAA99B,IAAi+B,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AAAhgC,IAAmgC,IAAI,CAAC,CAAC;AAAzgC,IAA4gC,IAAI,CAAC,CAAC;AAClhC,IAAM,QAAQ,EAAE,SAAS,OAAO,YAAY,EAAE,QAAQ,GAAG,cAAc,GAAG,SAAS,GAAG,UAAU,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,oBAAoB,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,GAAG,YAAY,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,aAAa,GAAG,eAAe,WAAW,CAAC,EAAE,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,qEAAqE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,0EAA0E,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,GAAG,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,kEAAkE,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,mFAAmF,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,UAAU,EAAE,KAAK,sCAAsC,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,yDAAyD,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,4DAA4D,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,6DAA6D,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,sEAAsE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,UAAU,EAAE,KAAK,GAAG,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,yCAAyC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE;AAC7jG,IAAM,UAAU;AAAA;AAAA;;;ACHvB,IAGM,OAIO;AAPb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACA,IAAM,QAAQ,IAAI,cAAc;AAAA,MAC5B,MAAM;AAAA,MACN,QAAQ,CAAC,YAAY,UAAU,gBAAgB,WAAW,mBAAmB;AAAA,IACjF,CAAC;AACM,IAAM,0BAA0B,CAAC,gBAAgB,UAAU,CAAC,MAAM;AACrE,aAAO,MAAM,IAAI,gBAAgB,MAAM,gBAAgB,SAAS;AAAA,QAC5D;AAAA,QACA,QAAQ,QAAQ;AAAA,MACpB,CAAC,CAAC;AAAA,IACN;AACA,4BAAwB,MAAM;AAAA;AAAA;;;ACb9B,IAQa;AARb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACO,IAAM,mBAAmB,CAAC,WAAW;AACxC,aAAO;AAAA,QACH,YAAY;AAAA,QACZ,gBAAe,iCAAQ,kBAAiB;AAAA,QACxC,gBAAe,iCAAQ,kBAAiB;AAAA,QACxC,oBAAmB,iCAAQ,sBAAqB;AAAA,QAChD,mBAAkB,iCAAQ,qBAAoB;AAAA,QAC9C,aAAY,iCAAQ,eAAc,CAAC;AAAA,QACnC,yBAAwB,iCAAQ,2BAA0B;AAAA,QAC1D,kBAAiB,iCAAQ,oBAAmB;AAAA,UACxC;AAAA,YACI,UAAU;AAAA,YACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,gBAAgB;AAAA,YACnE,QAAQ,IAAI,kBAAkB;AAAA,UAClC;AAAA,UACA;AAAA,YACI,UAAU;AAAA,YACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,mBAAmB,MAAM,aAAa,CAAC;AAAA,YAC1F,QAAQ,IAAI,aAAa;AAAA,UAC7B;AAAA,QACJ;AAAA,QACA,SAAQ,iCAAQ,WAAU,IAAI,WAAW;AAAA,QACzC,YAAW,iCAAQ,cAAa;AAAA,QAChC,YAAW,iCAAQ,cAAa;AAAA,QAChC,cAAa,iCAAQ,gBAAe;AAAA,QACpC,cAAa,iCAAQ,gBAAe;AAAA,MACxC;AAAA,IACJ;AAAA;AAAA;;;ACnCA,IAWaC;AAXb;AAAA;AAAA;AACA;AACA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA,IAAAA;AACA,IAAAA;AACO,IAAMD,oBAAmB,CAAC,WAAW;AACxC,YAAM,eAAe,0BAA0B,MAAM;AACrD,YAAM,wBAAwB,MAAM,aAAa,EAAE,KAAK,yBAAyB;AACjF,YAAM,qBAAqB,iBAAuB,MAAM;AACxD,aAAO;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,SAAS;AAAA,QACT;AAAA,QACA,oBAAmB,iCAAQ,sBAAqB;AAAA,QAChD,4BAA2B,iCAAQ,+BAA8B,CAACE,OAAM,MAAM,QAAQ,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,QAC/H,2BAA0B,iCAAQ,6BAC9B,+BAA+B,EAAE,WAAW,mBAAmB,WAAW,eAAe,gBAAY,QAAQ,CAAC;AAAA,QAClH,cAAa,iCAAQ,gBAAe;AAAA,QACpC,SAAQ,iCAAQ,WAAU,gBAAgB,mBAAmB;AAAA,QAC7D,gBAAgB,iBAAe,QAAO,iCAAQ,mBAAkB,qBAAqB;AAAA,QACrF,YAAW,iCAAQ,eAAc,aAAa,MAAM,sBAAsB,GAAG,aAAa;AAAA,QAC1F,SAAQ,iCAAQ,WAAU;AAAA,QAC1B,kBAAiB,iCAAQ,oBAAmB;AAAA,QAC5C,uBAAsB,iCAAQ,0BAAyB,MAAM,QAAQ,QAAQ,8BAA8B;AAAA,QAC3G,kBAAiB,iCAAQ,qBAAoB,MAAM,QAAQ,QAAQ,yBAAyB;AAAA,MAChG;AAAA,IACJ;AAAA;AAAA;;;ACjCA,IAAa,mCA+BA;AA/Bb;AAAA;AAAO,IAAM,oCAAoC,CAAC,kBAAkB;AAChE,YAAM,mBAAmB,cAAc;AACvC,UAAI,0BAA0B,cAAc;AAC5C,UAAI,eAAe,cAAc;AACjC,aAAO;AAAA,QACH,kBAAkB,gBAAgB;AAC9B,gBAAM,QAAQ,iBAAiB,UAAU,CAAC,WAAW,OAAO,aAAa,eAAe,QAAQ;AAChG,cAAI,UAAU,IAAI;AACd,6BAAiB,KAAK,cAAc;AAAA,UACxC,OACK;AACD,6BAAiB,OAAO,OAAO,GAAG,cAAc;AAAA,UACpD;AAAA,QACJ;AAAA,QACA,kBAAkB;AACd,iBAAO;AAAA,QACX;AAAA,QACA,0BAA0B,wBAAwB;AAC9C,oCAA0B;AAAA,QAC9B;AAAA,QACA,yBAAyB;AACrB,iBAAO;AAAA,QACX;AAAA,QACA,eAAe,aAAa;AACxB,yBAAe;AAAA,QACnB;AAAA,QACA,cAAc;AACV,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACO,IAAM,+BAA+B,CAAC,WAAW;AACpD,aAAO;AAAA,QACH,iBAAiB,OAAO,gBAAgB;AAAA,QACxC,wBAAwB,OAAO,uBAAuB;AAAA,QACtD,aAAa,OAAO,YAAY;AAAA,MACpC;AAAA,IACJ;AAAA;AAAA;;;ACrCA,IAIa;AAJb;AAAA;AAAA,IAAAC;AACA;AACA,IAAAA;AACA;AACO,IAAM,2BAA2B,CAAC,eAAe,eAAe;AACnE,YAAM,yBAAyB,OAAO,OAAO,mCAAmC,aAAa,GAAG,iCAAiC,aAAa,GAAG,qCAAqC,aAAa,GAAG,kCAAkC,aAAa,CAAC;AACtP,iBAAW,QAAQ,CAAC,cAAc,UAAU,UAAU,sBAAsB,CAAC;AAC7E,aAAO,OAAO,OAAO,eAAe,uCAAuC,sBAAsB,GAAG,4BAA4B,sBAAsB,GAAG,gCAAgC,sBAAsB,GAAG,6BAA6B,sBAAsB,CAAC;AAAA,IAC1Q;AAAA;AAAA;;;ACRA,IAea;AAfb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AACA;AAEO,IAAM,YAAN,cAAwB,OAAS;AAAA,MAEpC,eAAe,CAAC,aAAa,GAAG;AAC5B,cAAM,YAAYC,kBAAmB,iBAAiB,CAAC,CAAC;AACxD,cAAM,SAAS;AAHnB;AAII,aAAK,aAAa;AAClB,cAAM,YAAY,gCAAgC,SAAS;AAC3D,cAAM,YAAY,uBAAuB,SAAS;AAClD,cAAM,YAAY,mBAAmB,SAAS;AAC9C,cAAM,YAAY,oBAAoB,SAAS;AAC/C,cAAM,YAAY,wBAAwB,SAAS;AACnD,cAAM,YAAY,sBAAsB,SAAS;AACjD,cAAM,YAAY,4BAA4B,SAAS;AACvD,cAAM,YAAY,yBAAyB,YAAW,+CAAe,eAAc,CAAC,CAAC;AACrF,aAAK,SAAS;AACd,aAAK,gBAAgB,IAAI,mBAAmB,KAAK,MAAM,CAAC;AACxD,aAAK,gBAAgB,IAAI,eAAe,KAAK,MAAM,CAAC;AACpD,aAAK,gBAAgB,IAAI,uBAAuB,KAAK,MAAM,CAAC;AAC5D,aAAK,gBAAgB,IAAI,oBAAoB,KAAK,MAAM,CAAC;AACzD,aAAK,gBAAgB,IAAI,gBAAgB,KAAK,MAAM,CAAC;AACrD,aAAK,gBAAgB,IAAI,4BAA4B,KAAK,MAAM,CAAC;AACjE,aAAK,gBAAgB,IAAI,uCAAuC,KAAK,QAAQ;AAAA,UACzE,kCAAkC;AAAA,UAClC,gCAAgC,OAAO,WAAW,IAAI,8BAA8B;AAAA,YAChF,kBAAkB,OAAO;AAAA,UAC7B,CAAC;AAAA,QACL,CAAC,CAAC;AACF,aAAK,gBAAgB,IAAI,qBAAqB,KAAK,MAAM,CAAC;AAAA,MAC9D;AAAA,MACA,UAAU;AACN,cAAM,QAAQ;AAAA,MAClB;AAAA,IACJ;AAAA;AAAA;;;AC/CA,IAEa;AAFb;AAAA;AAAA,IAAAC;AAEO,IAAM,sBAAN,MAAM,6BAA4B,iBAAmB;AAAA,MACxD,YAAY,SAAS;AACjB,cAAM,OAAO;AACb,eAAO,eAAe,MAAM,qBAAoB,SAAS;AAAA,MAC7D;AAAA,IACJ;AAAA;AAAA;;;ACPA,IAEa,+BAIA,sCAIA,uBAYA,kCAYA,+BAYA,yBAYA,2BAYA,+BAYA,oDAIA,qDAIA;AA1Fb;AAAA;AAAA,IAAAC;AACA;AACO,IAAM,gCAAgC,CAAC,SAAS;AAAA,MACnD,GAAG;AAAA,MACH,GAAI,IAAI,mBAAmB,EAAE,iBAAiB,iBAAiB;AAAA,IACnE;AACO,IAAM,uCAAuC,CAAC,SAAS;AAAA,MAC1D,GAAG;AAAA,MACH,GAAI,IAAI,eAAe,EAAE,aAAa,8BAA8B,IAAI,WAAW,EAAE;AAAA,IACzF;AACO,IAAM,wBAAN,MAAM,+BAA8B,oBAAgB;AAAA,MAGvD,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,uBAAsB,SAAS;AAAA,MAC/D;AAAA,IACJ;AACO,IAAM,mCAAN,MAAM,0CAAyC,oBAAgB;AAAA,MAGlE,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,kCAAiC,SAAS;AAAA,MAC1E;AAAA,IACJ;AACO,IAAM,gCAAN,MAAM,uCAAsC,oBAAgB;AAAA,MAG/D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,+BAA8B,SAAS;AAAA,MACvE;AAAA,IACJ;AACO,IAAM,0BAAN,MAAM,iCAAgC,oBAAgB;AAAA,MAGzD,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,yBAAwB,SAAS;AAAA,MACjE;AAAA,IACJ;AACO,IAAM,4BAAN,MAAM,mCAAkC,oBAAgB;AAAA,MAG3D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,MACnE;AAAA,IACJ;AACO,IAAM,gCAAN,MAAM,uCAAsC,oBAAgB;AAAA,MAG/D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,+BAA8B,SAAS;AAAA,MACvE;AAAA,IACJ;AACO,IAAM,qDAAqD,CAAC,SAAS;AAAA,MACxE,GAAG;AAAA,MACH,GAAI,IAAI,oBAAoB,EAAE,kBAAkB,iBAAiB;AAAA,IACrE;AACO,IAAM,sDAAsD,CAAC,SAAS;AAAA,MACzE,GAAG;AAAA,MACH,GAAI,IAAI,eAAe,EAAE,aAAa,8BAA8B,IAAI,WAAW,EAAE;AAAA,IACzF;AACO,IAAM,iCAAN,MAAM,wCAAuC,oBAAgB;AAAA,MAGhE,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,gCAA+B,SAAS;AAAA,MACxE;AAAA,IACJ;AAAA;AAAA;;;ACrGA,IAKa,sBAUA,qCAUA,sBAaA,qCAaP,iBAqCA,6BASA,sCASA,iCASA,qCASA,wCASA,qCASA,+BASA,sBAoEA,qCAgCA,6BAeA,yBAOA,oBAUA,6BAeA,QAUA,mBAYA,gBAeA,oBAUA,uBAgBA,sCAyBA,gBAgBA,0BAOA,mCAOA,8BAOA,kCAOA,qCAOA,kCAOA,4BAOA,qBAOA,mBACA,qBAkBA,gBAGA,GACA,IACA,MACA,KACA,MACA,MACA,QACA,KACA,KACA,IACA,KACA,KACA,IACA,KACA,IACA,IACA,KACA,MACA,KACA,KACA,MACA,KACA,KACA,MACA,MACA,QACA,KACA,KACA,KACA,IACA,KACA,MACA,IACA,KACA,MACA,IACA,IACA,2BAGA;AAxgBN;AAAA;AAAA,IAAAC;AACA;AACA,IAAAA;AACA;AACA;AACO,IAAM,uBAAuB,OAAO,OAAO,YAAY;AAC1D,YAAM,UAAU;AAChB,UAAI;AACJ,aAAO,0BAA0B;AAAA,QAC7B,GAAG,qBAAqB,OAAO,OAAO;AAAA,QACtC,CAAC,EAAE,GAAG;AAAA,QACN,CAAC,EAAE,GAAG;AAAA,MACV,CAAC;AACD,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,sCAAsC,OAAO,OAAO,YAAY;AACzE,YAAM,UAAU;AAChB,UAAI;AACJ,aAAO,0BAA0B;AAAA,QAC7B,GAAG,oCAAoC,OAAO,OAAO;AAAA,QACrD,CAAC,EAAE,GAAG;AAAA,QACN,CAAC,EAAE,GAAG;AAAA,MACV,CAAC;AACD,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,uBAAuB,OAAO,QAAQ,YAAY;AAC3D,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,aAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,sBAAsB,KAAK,kBAAkB,OAAO;AAC/D,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,sCAAsC,OAAO,QAAQ,YAAY;AAC1E,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,aAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,qCAAqC,KAAK,iCAAiC,OAAO;AAC7F,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACA,IAAM,kBAAkB,OAAO,QAAQ,YAAY;AAC/C,YAAM,eAAe;AAAA,QACjB,GAAG;AAAA,QACH,MAAM,MAAM,kBAAe,OAAO,MAAM,OAAO;AAAA,MACnD;AACA,YAAM,YAAY,mBAAmB,QAAQ,aAAa,IAAI;AAC9D,cAAQ,WAAW;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,4BAA4B,cAAc,OAAO;AAAA,QACjE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,uCAAuC,cAAc,OAAO;AAAA,QAC5E,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,oCAAoC,cAAc,OAAO;AAAA,QACzE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,8BAA8B,cAAc,OAAO;AAAA,QACnE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,qCAAqC,cAAc,OAAO;AAAA,QAC1E,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,QACrE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,oCAAoC,cAAc,OAAO;AAAA,QACzE;AACI,gBAAM,aAAa,aAAa;AAChC,iBAAO,kBAAkB;AAAA,YACrB;AAAA,YACA,YAAY,WAAW;AAAA,YACvB;AAAA,UACJ,CAAC;AAAA,MACT;AAAA,IACJ;AACA,IAAM,8BAA8B,OAAO,cAAc,YAAY;AACjE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,yBAAyB,KAAK,OAAO,OAAO;AACjE,YAAM,YAAY,IAAI,sBAAsB;AAAA,QACxC,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,uCAAuC,OAAO,cAAc,YAAY;AAC1E,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,kCAAkC,KAAK,OAAO,OAAO;AAC1E,YAAM,YAAY,IAAI,+BAA+B;AAAA,QACjD,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,6BAA6B,KAAK,OAAO,OAAO;AACrE,YAAM,YAAY,IAAI,0BAA0B;AAAA,QAC5C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,sCAAsC,OAAO,cAAc,YAAY;AACzE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,iCAAiC,KAAK,OAAO,OAAO;AACzE,YAAM,YAAY,IAAI,8BAA8B;AAAA,QAChD,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,yCAAyC,OAAO,cAAc,YAAY;AAC5E,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,oCAAoC,KAAK,OAAO,OAAO;AAC5E,YAAM,YAAY,IAAI,iCAAiC;AAAA,QACnD,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,sCAAsC,OAAO,cAAc,YAAY;AACzE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,iCAAiC,KAAK,OAAO,OAAO;AACzE,YAAM,YAAY,IAAI,8BAA8B;AAAA,QAChD,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,gCAAgC,OAAO,cAAc,YAAY;AACnE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,2BAA2B,KAAK,OAAO,OAAO;AACnE,YAAM,YAAY,IAAI,wBAAwB;AAAA,QAC1C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,uBAAuB,CAAC,OAAO,YAAY;AAvJjD,UAAAC,KAAA;AAwJI,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,gBAAQ,IAAI,IAAI,MAAM,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,cAAM,gBAAgB,4BAA4B,MAAM,GAAG,GAAG,OAAO;AACrE,cAAIA,MAAA,MAAM,GAAG,MAAT,gBAAAA,IAAY,YAAW,GAAG;AAC1B,kBAAQ,aAAa,CAAC;AAAA,QAC1B;AACA,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,gBAAM,MAAM,cAAc,GAAG;AAC7B,kBAAQ,GAAG,IAAI;AAAA,QACnB,CAAC;AAAA,MACL;AACA,UAAI,MAAM,EAAE,KAAK,MAAM;AACnB,gBAAQ,EAAE,IAAI,MAAM,EAAE;AAAA,MAC1B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,EAAE,KAAK,MAAM;AACnB,cAAM,gBAAgB,eAAe,MAAM,EAAE,GAAG,OAAO;AACvD,cAAI,WAAM,EAAE,MAAR,mBAAW,YAAW,GAAG;AACzB,kBAAQ,OAAO,CAAC;AAAA,QACpB;AACA,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,gBAAM,MAAM,QAAQ,GAAG;AACvB,kBAAQ,GAAG,IAAI;AAAA,QACnB,CAAC;AAAA,MACL;AACA,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,cAAM,gBAAgB,kBAAkB,MAAM,IAAI,GAAG,OAAO;AAC5D,cAAI,WAAM,IAAI,MAAV,mBAAa,YAAW,GAAG;AAC3B,kBAAQ,oBAAoB,CAAC;AAAA,QACjC;AACA,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,gBAAM,MAAM,qBAAqB,GAAG;AACpC,kBAAQ,GAAG,IAAI;AAAA,QACnB,CAAC;AAAA,MACL;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,cAAM,gBAAgB,4BAA4B,MAAM,GAAG,GAAG,OAAO;AACrE,cAAI,WAAM,GAAG,MAAT,mBAAY,YAAW,GAAG;AAC1B,kBAAQ,mBAAmB,CAAC;AAAA,QAChC;AACA,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,gBAAM,MAAM,oBAAoB,GAAG;AACnC,kBAAQ,GAAG,IAAI;AAAA,QACnB,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX;AACA,IAAM,sCAAsC,CAAC,OAAO,YAAY;AA3NhE,UAAAA;AA4NI,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,gBAAQ,IAAI,IAAI,MAAM,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,gBAAQ,IAAI,IAAI,MAAM,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,cAAM,gBAAgB,4BAA4B,MAAM,GAAG,GAAG,OAAO;AACrE,cAAIA,MAAA,MAAM,GAAG,MAAT,gBAAAA,IAAY,YAAW,GAAG;AAC1B,kBAAQ,aAAa,CAAC;AAAA,QAC1B;AACA,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,gBAAM,MAAM,cAAc,GAAG;AAC7B,kBAAQ,GAAG,IAAI;AAAA,QACnB,CAAC;AAAA,MACL;AACA,UAAI,MAAM,EAAE,KAAK,MAAM;AACnB,gBAAQ,EAAE,IAAI,MAAM,EAAE;AAAA,MAC1B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AACA,IAAM,8BAA8B,CAAC,OAAO,YAAY;AACpD,YAAM,UAAU,CAAC;AACjB,UAAI,UAAU;AACd,iBAAW,SAAS,OAAO;AACvB,YAAI,UAAU,MAAM;AAChB;AAAA,QACJ;AACA,cAAM,gBAAgB,wBAAwB,OAAO,OAAO;AAC5D,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,kBAAQ,UAAU,OAAO,IAAI,GAAG,EAAE,IAAI;AAAA,QAC1C,CAAC;AACD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,0BAA0B,CAAC,OAAO,YAAY;AAChD,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM,EAAE,KAAK,MAAM;AACnB,gBAAQ,EAAE,IAAI,MAAM,EAAE;AAAA,MAC1B;AACA,aAAO;AAAA,IACX;AACA,IAAM,qBAAqB,CAAC,OAAO,YAAY;AAC3C,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,gBAAQ,IAAI,IAAI,MAAM,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AACA,IAAM,8BAA8B,CAAC,OAAO,YAAY;AACpD,YAAM,UAAU,CAAC;AACjB,UAAI,UAAU;AACd,iBAAW,SAAS,OAAO;AACvB,YAAI,UAAU,MAAM;AAChB;AAAA,QACJ;AACA,cAAM,gBAAgB,mBAAmB,OAAO,OAAO;AACvD,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,kBAAQ,UAAU,OAAO,IAAI,GAAG,EAAE,IAAI;AAAA,QAC1C,CAAC;AACD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,SAAS,CAAC,OAAO,YAAY;AAC/B,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM,EAAE,KAAK,MAAM;AACnB,gBAAQ,EAAE,IAAI,MAAM,EAAE;AAAA,MAC1B;AACA,UAAI,MAAM,GAAG,KAAK,MAAM;AACpB,gBAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AACA,IAAM,oBAAoB,CAAC,OAAO,YAAY;AAC1C,YAAM,UAAU,CAAC;AACjB,UAAI,UAAU;AACd,iBAAW,SAAS,OAAO;AACvB,YAAI,UAAU,MAAM;AAChB;AAAA,QACJ;AACA,gBAAQ,UAAU,OAAO,EAAE,IAAI;AAC/B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,iBAAiB,CAAC,OAAO,YAAY;AACvC,YAAM,UAAU,CAAC;AACjB,UAAI,UAAU;AACd,iBAAW,SAAS,OAAO;AACvB,YAAI,UAAU,MAAM;AAChB;AAAA,QACJ;AACA,cAAM,gBAAgB,OAAO,OAAO,OAAO;AAC3C,eAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACpD,kBAAQ,UAAU,OAAO,IAAI,GAAG,EAAE,IAAI;AAAA,QAC1C,CAAC;AACD;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,qBAAqB,CAAC,QAAQ,YAAY;AAC5C,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,aAAe,OAAO,IAAI,CAAC;AAAA,MAChD;AACA,UAAI,OAAO,GAAG,KAAK,MAAM;AACrB,iBAAS,GAAG,IAAI,aAAe,OAAO,GAAG,CAAC;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AACA,IAAM,wBAAwB,CAAC,QAAQ,YAAY;AAC/C,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,eAAe,OAAO,EAAE,GAAG,OAAO;AAAA,MACrD;AACA,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,mBAAmB,OAAO,IAAI,GAAG,OAAO;AAAA,MAC7D;AACA,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,iBAAmB,OAAO,IAAI,CAAC;AAAA,MACpD;AACA,UAAI,OAAO,GAAG,KAAK,MAAM;AACrB,iBAAS,GAAG,IAAI,aAAe,OAAO,GAAG,CAAC;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AACA,IAAM,uCAAuC,CAAC,QAAQ,YAAY;AAC9D,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,eAAe,OAAO,EAAE,GAAG,OAAO;AAAA,MACrD;AACA,UAAI,OAAO,MAAM,KAAK,MAAM;AACxB,iBAAS,MAAM,IAAI,aAAe,OAAO,MAAM,CAAC;AAAA,MACpD;AACA,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,mBAAmB,OAAO,IAAI,GAAG,OAAO;AAAA,MAC7D;AACA,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,iBAAmB,OAAO,IAAI,CAAC;AAAA,MACpD;AACA,UAAI,OAAO,GAAG,KAAK,MAAM;AACrB,iBAAS,GAAG,IAAI,aAAe,OAAO,GAAG,CAAC;AAAA,MAC9C;AACA,UAAI,OAAO,GAAG,KAAK,MAAM;AACrB,iBAAS,GAAG,IAAI,aAAe,OAAO,GAAG,CAAC;AAAA,MAC9C;AACA,UAAI,OAAO,GAAG,KAAK,MAAM;AACrB,iBAAS,GAAG,IAAI,aAAe,OAAO,GAAG,CAAC;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AACA,IAAM,iBAAiB,CAAC,QAAQ,YAAY;AACxC,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,aAAe,OAAO,IAAI,CAAC;AAAA,MAChD;AACA,UAAI,OAAO,IAAI,KAAK,MAAM;AACtB,iBAAS,IAAI,IAAI,aAAe,OAAO,IAAI,CAAC;AAAA,MAChD;AACA,UAAI,OAAO,GAAG,KAAK,MAAM;AACrB,iBAAS,GAAG,IAAI,aAAe,OAAO,GAAG,CAAC;AAAA,MAC9C;AACA,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,cAAgB,+BAAiC,OAAO,EAAE,CAAC,CAAC;AAAA,MAC/E;AACA,aAAO;AAAA,IACX;AACA,IAAM,2BAA2B,CAAC,QAAQ,YAAY;AAClD,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,oCAAoC,CAAC,QAAQ,YAAY;AAC3D,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,+BAA+B,CAAC,QAAQ,YAAY;AACtD,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,mCAAmC,CAAC,QAAQ,YAAY;AAC1D,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,sCAAsC,CAAC,QAAQ,YAAY;AAC7D,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,mCAAmC,CAAC,QAAQ,YAAY;AAC1D,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,6BAA6B,CAAC,QAAQ,YAAY;AACpD,YAAM,WAAW,CAAC;AAClB,UAAI,OAAO,EAAE,KAAK,MAAM;AACpB,iBAAS,EAAE,IAAI,aAAe,OAAO,EAAE,CAAC;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,IAAM,sBAAsB,CAAC,YAAY;AAAA,MACrC,gBAAgB,OAAO;AAAA,MACvB,WAAW,OAAO,QAAQ,kBAAkB,KAAK,OAAO,QAAQ,mBAAmB,KAAK,OAAO,QAAQ,kBAAkB;AAAA,MACzH,mBAAmB,OAAO,QAAQ,YAAY;AAAA,MAC9C,MAAM,OAAO,QAAQ,aAAa;AAAA,IACtC;AAEA,IAAM,oBAAoB,kBAAkB,mBAAe;AAC3D,IAAM,sBAAsB,OAAO,SAAS,SAAS,MAAM,kBAAkB,SAAS;AAClF,YAAM,EAAE,UAAU,WAAW,SAAS,MAAM,MAAM,SAAS,IAAI,MAAM,QAAQ,SAAS;AACtF,YAAM,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,MAAM,SAAS,SAAS,GAAG,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,OAAO,WAAW;AAAA,QACzE;AAAA,MACJ;AACA,UAAI,qBAAqB,QAAW;AAChC,iBAAS,WAAW;AAAA,MACxB;AACA,UAAI,SAAS,QAAW;AACpB,iBAAS,OAAO;AAAA,MACpB;AACA,aAAO,IAAI,YAAc,QAAQ;AAAA,IACrC;AACA,IAAM,iBAAiB;AAAA,MACnB,gBAAgB;AAAA,IACpB;AACA,IAAM,IAAI;AACV,IAAM,KAAK;AACX,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,4BAA4B,CAAC,gBAAgB,OAAO,QAAQ,WAAW,EACxE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,2BAA6B,GAAG,IAAI,MAAM,2BAA6B,KAAK,CAAC,EACnG,KAAK,GAAG;AACb,IAAM,qBAAqB,CAAC,QAAQ,SAAS;AAxgB7C,UAAAA;AAygBI,YAAIA,MAAA,KAAK,UAAL,gBAAAA,IAAY,UAAS,QAAW;AAChC,eAAO,KAAK,MAAM;AAAA,MACtB;AACA,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO;AAAA,MACX;AAAA,IACJ;AAAA;AAAA;;;AC/gBA,IAOa;AAPb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,oBAAN,cAAgC,QAClC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,oCAAoC,cAAc,CAAC,CAAC,EACtD,EAAE,aAAa,mBAAmB,EAClC,EAAE,QAAQ,oCAAoC,EAC9C,IAAI,oBAAoB,EACxB,GAAG,oBAAoB,EACvB,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAOa;AAPb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,mCAAN,cAA+C,QACjD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,oCAAoC,6BAA6B,CAAC,CAAC,EACrE,EAAE,aAAa,kCAAkC,EACjD,EAAE,oDAAoD,mDAAmD,EACzG,IAAI,mCAAmC,EACvC,GAAG,mCAAmC,EACtC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAIM,UAIO;AARb;AAAA;AAAA,IAAAE;AACA;AACA;AACA;AACA,IAAM,WAAW;AAAA,MACb;AAAA,MACA;AAAA,IACJ;AACO,IAAM,MAAN,cAAkB,UAAU;AAAA,IACnC;AACA,2BAAuB,UAAU,GAAG;AAAA;AAAA;;;ACVpC;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAGM,4BACA,iCASA,eAMO,uBAkCA,sCAuCP;AA5FN;AAAA;AAAA;AACA;AACA;AACA,IAAM,6BAA6B;AACnC,IAAM,kCAAkC,CAAC,oBAAoB;AACzD,UAAI,QAAO,mDAAiB,SAAQ,UAAU;AAC1C,cAAM,gBAAgB,gBAAgB,IAAI,MAAM,GAAG;AACnD,YAAI,cAAc,SAAS,KAAK,cAAc,CAAC,MAAM,IAAI;AACrD,iBAAO,cAAc,CAAC;AAAA,QAC1B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAM,gBAAgB,OAAO,SAAS,eAAe,6BAA6B;AAblF,UAAAC;AAcI,YAAM,SAAS,OAAO,YAAY,aAAa,MAAM,QAAQ,IAAI;AACjE,YAAM,eAAe,OAAO,kBAAkB,aAAa,MAAM,cAAc,IAAI;AACnF,OAAAA,MAAA,qEAA0B,UAA1B,gBAAAA,IAAA,+BAAkC,sCAAsC,uBAAuB,GAAG,MAAM,eAAe,GAAG,YAAY,oBAAoB,GAAG,0BAA0B;AACvL,aAAO,UAAU,gBAAgB;AAAA,IACrC;AACO,IAAM,wBAAwB,CAAC,YAAYC,eAAc;AAC5D,UAAI;AACJ,UAAI;AACJ,aAAO,OAAO,aAAa,WAAW;AAtB1C,YAAAD,KAAA;AAuBQ,6BAAqB;AACrB,YAAI,CAAC,WAAW;AACZ,gBAAM,EAAE,UAASA,MAAA,yCAAY,uBAAZ,gBAAAA,IAAgC,QAAQ,QAAQ,kBAAiB,8CAAY,uBAAZ,mBAAgC,gBAAgB,yBAA0B,IAAI;AAChK,gBAAM,iBAAiB,MAAM,cAAc,SAAQ,8CAAY,uBAAZ,mBAAgC,QAAQ,wBAAwB;AACnH,gBAAM,6BAA6B,CAAC,KAAK,cAAc;AACvD,sBAAY,IAAIC,WAAU;AAAA,YACtB,UAAS,8CAAY,uBAAZ,mBAAgC;AAAA,YACzC,2BAA2B,MAAM,YAAY;AAAA,YAC7C,QAAQ;AAAA,YACR,gBAAgB,6BAA6B,iBAAiB;AAAA,YAC9D;AAAA,UACJ,CAAC;AAAA,QACL;AACA,cAAM,EAAE,aAAa,gBAAgB,IAAI,MAAM,UAAU,KAAK,IAAI,kBAAkB,MAAM,CAAC;AAC3F,YAAI,CAAC,eAAe,CAAC,YAAY,eAAe,CAAC,YAAY,iBAAiB;AAC1E,gBAAM,IAAI,MAAM,uDAAuD,OAAO,OAAO,EAAE;AAAA,QAC3F;AACA,cAAM,YAAY,gCAAgC,eAAe;AACjE,cAAM,cAAc;AAAA,UAChB,aAAa,YAAY;AAAA,UACzB,iBAAiB,YAAY;AAAA,UAC7B,cAAc,YAAY;AAAA,UAC1B,YAAY,YAAY;AAAA,UACxB,GAAI,YAAY,mBAAmB,EAAE,iBAAiB,YAAY,gBAAgB;AAAA,UAClF,GAAI,aAAa,EAAE,UAAU;AAAA,QACjC;AACA,6BAAqB,aAAa,+BAA+B,GAAG;AACpE,eAAO;AAAA,MACX;AAAA,IACJ;AACO,IAAM,uCAAuC,CAAC,YAAYA,eAAc;AAC3E,UAAI;AACJ,aAAO,OAAO,WAAW;AAvD7B,YAAAD,KAAA;AAwDQ,YAAI,CAAC,WAAW;AACZ,gBAAM,EAAE,UAASA,MAAA,yCAAY,uBAAZ,gBAAAA,IAAgC,QAAQ,QAAQ,kBAAiB,8CAAY,uBAAZ,mBAAgC,gBAAgB,yBAA0B,IAAI;AAChK,gBAAM,iBAAiB,MAAM,cAAc,SAAQ,8CAAY,uBAAZ,mBAAgC,QAAQ,wBAAwB;AACnH,gBAAM,6BAA6B,CAAC,KAAK,cAAc;AACvD,sBAAY,IAAIC,WAAU;AAAA,YACtB,UAAS,8CAAY,uBAAZ,mBAAgC;AAAA,YACzC,QAAQ;AAAA,YACR,gBAAgB,6BAA6B,iBAAiB;AAAA,YAC9D;AAAA,UACJ,CAAC;AAAA,QACL;AACA,cAAM,EAAE,aAAa,gBAAgB,IAAI,MAAM,UAAU,KAAK,IAAI,iCAAiC,MAAM,CAAC;AAC1G,YAAI,CAAC,eAAe,CAAC,YAAY,eAAe,CAAC,YAAY,iBAAiB;AAC1E,gBAAM,IAAI,MAAM,sEAAsE,OAAO,OAAO,EAAE;AAAA,QAC1G;AACA,cAAM,YAAY,gCAAgC,eAAe;AACjE,cAAM,cAAc;AAAA,UAChB,aAAa,YAAY;AAAA,UACzB,iBAAiB,YAAY;AAAA,UAC7B,cAAc,YAAY;AAAA,UAC1B,YAAY,YAAY;AAAA,UACxB,GAAI,YAAY,mBAAmB,EAAE,iBAAiB,YAAY,gBAAgB;AAAA,UAClF,GAAI,aAAa,EAAE,UAAU;AAAA,QACjC;AACA,YAAI,WAAW;AACX,+BAAqB,aAAa,uBAAuB,GAAG;AAAA,QAChE;AACA,6BAAqB,aAAa,sCAAsC,GAAG;AAC3E,eAAO;AAAA,MACX;AAAA,IACJ;AAMA,IAAM,OAAO,CAAC,mBAAmB;AA5FjC,UAAAD;AA6FI,eAAOA,MAAA,iDAAgB,aAAhB,gBAAAA,IAA0B,qBAAoB;AAAA,IACzD;AAAA;AAAA;;;AC9FA,IAEM,8BAaOE,wBACAC,uCACA;AAjBb;AAAA;AAAA;AACA;AACA,IAAM,+BAA+B,CAAC,UAAU,mBAAmB;AAC/D,UAAI,CAAC;AACD,eAAO;AAAA;AAEP,eAAO,MAAM,8BAA8B,SAAS;AAAA,UAChD,YAAY,QAAQ;AAChB,kBAAM,MAAM;AACZ,uBAAW,iBAAiB,gBAAgB;AACxC,mBAAK,gBAAgB,IAAI,aAAa;AAAA,YAC1C;AAAA,UACJ;AAAA,QACJ;AAAA,IACR;AACO,IAAMD,yBAAwB,CAAC,aAAa,CAAC,GAAG,eAAe,sBAAyB,YAAY,6BAA6B,WAAW,UAAU,CAAC;AACvJ,IAAMC,wCAAuC,CAAC,aAAa,CAAC,GAAG,eAAe,qCAAwC,YAAY,6BAA6B,WAAW,UAAU,CAAC;AACrL,IAAM,oCAAoC,CAAC,aAAa,CAAC,UAAU,SAAS;AAAA,MAC/E,aAAaD,uBAAsB,KAAK;AAAA,MACxC,4BAA4BC,sCAAqC,KAAK;AAAA,MACtE,GAAG;AAAA,IACP,CAAC;AAAA;AAAA;;;ACrBD;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;", "names": ["init_dist_es", "init_dist_es", "init_dist_es", "getRuntimeConfig", "init_dist_es", "_", "init_dist_es", "init_dist_es", "getRuntimeConfig", "init_dist_es", "init_dist_es", "init_dist_es", "_a", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "_a", "STSClient", "getDefaultRoleAssumer", "getDefaultRoleAssumerWithWebIdentity"]}