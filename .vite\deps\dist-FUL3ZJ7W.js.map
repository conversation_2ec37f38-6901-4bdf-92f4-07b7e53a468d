{"version": 3, "sources": ["browser-external:node:fs", "browser-external:node:path", "../../node_modules/ollama/dist/index.mjs", "../../node_modules/ollama/dist/browser.mjs"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:fs\" has been externalized for browser compatibility. Cannot access \"node:fs.${key}\" in client code. See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:path\" has been externalized for browser compatibility. Cannot access \"node:path.${key}\" in client code. See https://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "import fs, { promises } from 'node:fs';\nimport { resolve } from 'node:path';\nimport { Ollama as Ollama$1 } from './browser.mjs';\nimport 'whatwg-fetch';\n\nclass Ollama extends Ollama$1 {\n  async encodeImage(image) {\n    if (typeof image !== \"string\") {\n      return Buffer.from(image).toString(\"base64\");\n    }\n    try {\n      if (fs.existsSync(image)) {\n        const fileBuffer = await promises.readFile(resolve(image));\n        return Buffer.from(fileBuffer).toString(\"base64\");\n      }\n    } catch {\n    }\n    return image;\n  }\n  /**\n   * checks if a file exists\n   * @param path {string} - The path to the file\n   * @private @internal\n   * @returns {Promise<boolean>} - Whether the file exists or not\n   */\n  async fileExists(path) {\n    try {\n      await promises.access(path);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n  async create(request) {\n    if (request.from && await this.fileExists(resolve(request.from))) {\n      throw Error(\"Creating with a local path is not currently supported from ollama-js\");\n    }\n    if (request.stream) {\n      return super.create(request);\n    } else {\n      return super.create(request);\n    }\n  }\n}\nconst index = new Ollama();\n\nexport { Ollama, index as default };\n", "import 'whatwg-fetch';\n\nconst defaultPort = \"11434\";\nconst defaultHost = `http://127.0.0.1:${defaultPort}`;\n\nconst version = \"0.5.15\";\n\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => {\n  __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass ResponseError extends Error {\n  constructor(error, status_code) {\n    super(error);\n    this.error = error;\n    this.status_code = status_code;\n    this.name = \"ResponseError\";\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ResponseError);\n    }\n  }\n}\nclass AbortableAsyncIterator {\n  constructor(abortController, itr, doneCallback) {\n    __publicField$1(this, \"abortController\");\n    __publicField$1(this, \"itr\");\n    __publicField$1(this, \"doneCallback\");\n    this.abortController = abortController;\n    this.itr = itr;\n    this.doneCallback = doneCallback;\n  }\n  abort() {\n    this.abortController.abort();\n  }\n  async *[Symbol.asyncIterator]() {\n    for await (const message of this.itr) {\n      if (\"error\" in message) {\n        throw new Error(message.error);\n      }\n      yield message;\n      if (message.done || message.status === \"success\") {\n        this.doneCallback();\n        return;\n      }\n    }\n    throw new Error(\"Did not receive done or success response in stream.\");\n  }\n}\nconst checkOk = async (response) => {\n  if (response.ok) {\n    return;\n  }\n  let message = `Error ${response.status}: ${response.statusText}`;\n  let errorData = null;\n  if (response.headers.get(\"content-type\")?.includes(\"application/json\")) {\n    try {\n      errorData = await response.json();\n      message = errorData.error || message;\n    } catch (error) {\n      console.log(\"Failed to parse error response as JSON\");\n    }\n  } else {\n    try {\n      console.log(\"Getting text from response\");\n      const textResponse = await response.text();\n      message = textResponse || message;\n    } catch (error) {\n      console.log(\"Failed to get text from error response\");\n    }\n  }\n  throw new ResponseError(message, response.status);\n};\nfunction getPlatform() {\n  if (typeof window !== \"undefined\" && window.navigator) {\n    const nav = navigator;\n    if (\"userAgentData\" in nav && nav.userAgentData?.platform) {\n      return `${nav.userAgentData.platform.toLowerCase()} Browser/${navigator.userAgent};`;\n    }\n    if (navigator.platform) {\n      return `${navigator.platform.toLowerCase()} Browser/${navigator.userAgent};`;\n    }\n    return `unknown Browser/${navigator.userAgent};`;\n  } else if (typeof process !== \"undefined\") {\n    return `${process.arch} ${process.platform} Node.js/${process.version}`;\n  }\n  return \"\";\n}\nfunction normalizeHeaders(headers) {\n  if (headers instanceof Headers) {\n    const obj = {};\n    headers.forEach((value, key) => {\n      obj[key] = value;\n    });\n    return obj;\n  } else if (Array.isArray(headers)) {\n    return Object.fromEntries(headers);\n  } else {\n    return headers || {};\n  }\n}\nconst fetchWithHeaders = async (fetch, url, options = {}) => {\n  const defaultHeaders = {\n    \"Content-Type\": \"application/json\",\n    Accept: \"application/json\",\n    \"User-Agent\": `ollama-js/${version} (${getPlatform()})`\n  };\n  options.headers = normalizeHeaders(options.headers);\n  const customHeaders = Object.fromEntries(\n    Object.entries(options.headers).filter(([key]) => !Object.keys(defaultHeaders).some((defaultKey) => defaultKey.toLowerCase() === key.toLowerCase()))\n  );\n  options.headers = {\n    ...defaultHeaders,\n    ...customHeaders\n  };\n  return fetch(url, options);\n};\nconst get = async (fetch, host, options) => {\n  const response = await fetchWithHeaders(fetch, host, {\n    headers: options?.headers\n  });\n  await checkOk(response);\n  return response;\n};\nconst post = async (fetch, host, data, options) => {\n  const isRecord = (input) => {\n    return input !== null && typeof input === \"object\" && !Array.isArray(input);\n  };\n  const formattedData = isRecord(data) ? JSON.stringify(data) : data;\n  const response = await fetchWithHeaders(fetch, host, {\n    method: \"POST\",\n    body: formattedData,\n    signal: options?.signal,\n    headers: options?.headers\n  });\n  await checkOk(response);\n  return response;\n};\nconst del = async (fetch, host, data, options) => {\n  const response = await fetchWithHeaders(fetch, host, {\n    method: \"DELETE\",\n    body: JSON.stringify(data),\n    headers: options?.headers\n  });\n  await checkOk(response);\n  return response;\n};\nconst parseJSON = async function* (itr) {\n  const decoder = new TextDecoder(\"utf-8\");\n  let buffer = \"\";\n  const reader = itr.getReader();\n  while (true) {\n    const { done, value: chunk } = await reader.read();\n    if (done) {\n      break;\n    }\n    buffer += decoder.decode(chunk);\n    const parts = buffer.split(\"\\n\");\n    buffer = parts.pop() ?? \"\";\n    for (const part of parts) {\n      try {\n        yield JSON.parse(part);\n      } catch (error) {\n        console.warn(\"invalid json: \", part);\n      }\n    }\n  }\n  for (const part of buffer.split(\"\\n\").filter((p) => p !== \"\")) {\n    try {\n      yield JSON.parse(part);\n    } catch (error) {\n      console.warn(\"invalid json: \", part);\n    }\n  }\n};\nconst formatHost = (host) => {\n  if (!host) {\n    return defaultHost;\n  }\n  let isExplicitProtocol = host.includes(\"://\");\n  if (host.startsWith(\":\")) {\n    host = `http://127.0.0.1${host}`;\n    isExplicitProtocol = true;\n  }\n  if (!isExplicitProtocol) {\n    host = `http://${host}`;\n  }\n  const url = new URL(host);\n  let port = url.port;\n  if (!port) {\n    if (!isExplicitProtocol) {\n      port = defaultPort;\n    } else {\n      port = url.protocol === \"https:\" ? \"443\" : \"80\";\n    }\n  }\n  let auth = \"\";\n  if (url.username) {\n    auth = url.username;\n    if (url.password) {\n      auth += `:${url.password}`;\n    }\n    auth += \"@\";\n  }\n  let formattedHost = `${url.protocol}//${auth}${url.hostname}:${port}${url.pathname}`;\n  if (formattedHost.endsWith(\"/\")) {\n    formattedHost = formattedHost.slice(0, -1);\n  }\n  return formattedHost;\n};\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nlet Ollama$1 = class Ollama {\n  constructor(config) {\n    __publicField(this, \"config\");\n    __publicField(this, \"fetch\");\n    __publicField(this, \"ongoingStreamedRequests\", []);\n    this.config = {\n      host: \"\",\n      headers: config?.headers\n    };\n    if (!config?.proxy) {\n      this.config.host = formatHost(config?.host ?? defaultHost);\n    }\n    this.fetch = config?.fetch ?? fetch;\n  }\n  // Abort any ongoing streamed requests to Ollama\n  abort() {\n    for (const request of this.ongoingStreamedRequests) {\n      request.abort();\n    }\n    this.ongoingStreamedRequests.length = 0;\n  }\n  /**\n   * Processes a request to the Ollama server. If the request is streamable, it will return a\n   * AbortableAsyncIterator that yields the response messages. Otherwise, it will return the response\n   * object.\n   * @param endpoint {string} - The endpoint to send the request to.\n   * @param request {object} - The request object to send to the endpoint.\n   * @protected {T | AbortableAsyncIterator<T>} - The response object or a AbortableAsyncIterator that yields\n   * response messages.\n   * @throws {Error} - If the response body is missing or if the response is an error.\n   * @returns {Promise<T | AbortableAsyncIterator<T>>} - The response object or a AbortableAsyncIterator that yields the streamed response.\n   */\n  async processStreamableRequest(endpoint, request) {\n    request.stream = request.stream ?? false;\n    const host = `${this.config.host}/api/${endpoint}`;\n    if (request.stream) {\n      const abortController = new AbortController();\n      const response2 = await post(this.fetch, host, request, {\n        signal: abortController.signal,\n        headers: this.config.headers\n      });\n      if (!response2.body) {\n        throw new Error(\"Missing body\");\n      }\n      const itr = parseJSON(response2.body);\n      const abortableAsyncIterator = new AbortableAsyncIterator(\n        abortController,\n        itr,\n        () => {\n          const i = this.ongoingStreamedRequests.indexOf(abortableAsyncIterator);\n          if (i > -1) {\n            this.ongoingStreamedRequests.splice(i, 1);\n          }\n        }\n      );\n      this.ongoingStreamedRequests.push(abortableAsyncIterator);\n      return abortableAsyncIterator;\n    }\n    const response = await post(this.fetch, host, request, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Encodes an image to base64 if it is a Uint8Array.\n   * @param image {Uint8Array | string} - The image to encode.\n   * @returns {Promise<string>} - The base64 encoded image.\n   */\n  async encodeImage(image) {\n    if (typeof image !== \"string\") {\n      const uint8Array = new Uint8Array(image);\n      let byteString = \"\";\n      const len = uint8Array.byteLength;\n      for (let i = 0; i < len; i++) {\n        byteString += String.fromCharCode(uint8Array[i]);\n      }\n      return btoa(byteString);\n    }\n    return image;\n  }\n  /**\n   * Generates a response from a text prompt.\n   * @param request {GenerateRequest} - The request object.\n   * @returns {Promise<GenerateResponse | AbortableAsyncIterator<GenerateResponse>>} - The response object or\n   * an AbortableAsyncIterator that yields response messages.\n   */\n  async generate(request) {\n    if (request.images) {\n      request.images = await Promise.all(request.images.map(this.encodeImage.bind(this)));\n    }\n    return this.processStreamableRequest(\"generate\", request);\n  }\n  /**\n   * Chats with the model. The request object can contain messages with images that are either\n   * Uint8Arrays or base64 encoded strings. The images will be base64 encoded before sending the\n   * request.\n   * @param request {ChatRequest} - The request object.\n   * @returns {Promise<ChatResponse | AbortableAsyncIterator<ChatResponse>>} - The response object or an\n   * AbortableAsyncIterator that yields response messages.\n   */\n  async chat(request) {\n    if (request.messages) {\n      for (const message of request.messages) {\n        if (message.images) {\n          message.images = await Promise.all(\n            message.images.map(this.encodeImage.bind(this))\n          );\n        }\n      }\n    }\n    return this.processStreamableRequest(\"chat\", request);\n  }\n  /**\n   * Creates a new model from a stream of data.\n   * @param request {CreateRequest} - The request object.\n   * @returns {Promise<ProgressResponse | AbortableAsyncIterator<ProgressResponse>>} - The response object or a stream of progress responses.\n   */\n  async create(request) {\n    return this.processStreamableRequest(\"create\", {\n      ...request\n    });\n  }\n  /**\n   * Pulls a model from the Ollama registry. The request object can contain a stream flag to indicate if the\n   * response should be streamed.\n   * @param request {PullRequest} - The request object.\n   * @returns {Promise<ProgressResponse | AbortableAsyncIterator<ProgressResponse>>} - The response object or\n   * an AbortableAsyncIterator that yields response messages.\n   */\n  async pull(request) {\n    return this.processStreamableRequest(\"pull\", {\n      name: request.model,\n      stream: request.stream,\n      insecure: request.insecure\n    });\n  }\n  /**\n   * Pushes a model to the Ollama registry. The request object can contain a stream flag to indicate if the\n   * response should be streamed.\n   * @param request {PushRequest} - The request object.\n   * @returns {Promise<ProgressResponse | AbortableAsyncIterator<ProgressResponse>>} - The response object or\n   * an AbortableAsyncIterator that yields response messages.\n   */\n  async push(request) {\n    return this.processStreamableRequest(\"push\", {\n      name: request.model,\n      stream: request.stream,\n      insecure: request.insecure\n    });\n  }\n  /**\n   * Deletes a model from the server. The request object should contain the name of the model to\n   * delete.\n   * @param request {DeleteRequest} - The request object.\n   * @returns {Promise<StatusResponse>} - The response object.\n   */\n  async delete(request) {\n    await del(\n      this.fetch,\n      `${this.config.host}/api/delete`,\n      { name: request.model },\n      { headers: this.config.headers }\n    );\n    return { status: \"success\" };\n  }\n  /**\n   * Copies a model from one name to another. The request object should contain the name of the\n   * model to copy and the new name.\n   * @param request {CopyRequest} - The request object.\n   * @returns {Promise<StatusResponse>} - The response object.\n   */\n  async copy(request) {\n    await post(this.fetch, `${this.config.host}/api/copy`, { ...request }, {\n      headers: this.config.headers\n    });\n    return { status: \"success\" };\n  }\n  /**\n   * Lists the models on the server.\n   * @returns {Promise<ListResponse>} - The response object.\n   * @throws {Error} - If the response body is missing.\n   */\n  async list() {\n    const response = await get(this.fetch, `${this.config.host}/api/tags`, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Shows the metadata of a model. The request object should contain the name of the model.\n   * @param request {ShowRequest} - The request object.\n   * @returns {Promise<ShowResponse>} - The response object.\n   */\n  async show(request) {\n    const response = await post(this.fetch, `${this.config.host}/api/show`, {\n      ...request\n    }, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Embeds text input into vectors.\n   * @param request {EmbedRequest} - The request object.\n   * @returns {Promise<EmbedResponse>} - The response object.\n   */\n  async embed(request) {\n    const response = await post(this.fetch, `${this.config.host}/api/embed`, {\n      ...request\n    }, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Embeds a text prompt into a vector.\n   * @param request {EmbeddingsRequest} - The request object.\n   * @returns {Promise<EmbeddingsResponse>} - The response object.\n   */\n  async embeddings(request) {\n    const response = await post(this.fetch, `${this.config.host}/api/embeddings`, {\n      ...request\n    }, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n  /**\n   * Lists the running models on the server\n   * @returns {Promise<ListResponse>} - The response object.\n   * @throws {Error} - If the response body is missing.\n   */\n  async ps() {\n    const response = await get(this.fetch, `${this.config.host}/api/ps`, {\n      headers: this.config.headers\n    });\n    return await response.json();\n  }\n};\nconst browser = new Ollama$1();\n\nexport { Ollama$1 as Ollama, browser as default };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,4FAA4F,GAAG,qIAAqI;AAAA,QACnP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,gGAAgG,GAAG,qIAAqI;AAAA,QACvP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF,qBAA6B;AAC7B,uBAAwB;;;ACDxB;AAEA,IAAM,cAAc;AACpB,IAAM,cAAc,oBAAoB,WAAW;AAEnD,IAAM,UAAU;AAEhB,IAAI,cAAc,OAAO;AACzB,IAAI,oBAAoB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC9J,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU;AACzC,oBAAkB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACtE,SAAO;AACT;AACA,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,EAChC,YAAY,OAAO,aAAa;AAC9B,UAAM,KAAK;AACX,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,cAAa;AAAA,IAC7C;AAAA,EACF;AACF;AACA,IAAM,yBAAN,MAA6B;AAAA,EAC3B,YAAY,iBAAiB,KAAK,cAAc;AAC9C,oBAAgB,MAAM,iBAAiB;AACvC,oBAAgB,MAAM,KAAK;AAC3B,oBAAgB,MAAM,cAAc;AACpC,SAAK,kBAAkB;AACvB,SAAK,MAAM;AACX,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAAA,EACA,QAAQ,OAAO,aAAa,IAAI;AAC9B,qBAAiB,WAAW,KAAK,KAAK;AACpC,UAAI,WAAW,SAAS;AACtB,cAAM,IAAI,MAAM,QAAQ,KAAK;AAAA,MAC/B;AACA,YAAM;AACN,UAAI,QAAQ,QAAQ,QAAQ,WAAW,WAAW;AAChD,aAAK,aAAa;AAClB;AAAA,MACF;AAAA,IACF;AACA,UAAM,IAAI,MAAM,qDAAqD;AAAA,EACvE;AACF;AACA,IAAM,UAAU,OAAO,aAAa;AAlDpC;AAmDE,MAAI,SAAS,IAAI;AACf;AAAA,EACF;AACA,MAAI,UAAU,SAAS,SAAS,MAAM,KAAK,SAAS,UAAU;AAC9D,MAAI,YAAY;AAChB,OAAI,cAAS,QAAQ,IAAI,cAAc,MAAnC,mBAAsC,SAAS,qBAAqB;AACtE,QAAI;AACF,kBAAY,MAAM,SAAS,KAAK;AAChC,gBAAU,UAAU,SAAS;AAAA,IAC/B,SAAS,OAAO;AACd,cAAQ,IAAI,wCAAwC;AAAA,IACtD;AAAA,EACF,OAAO;AACL,QAAI;AACF,cAAQ,IAAI,4BAA4B;AACxC,YAAM,eAAe,MAAM,SAAS,KAAK;AACzC,gBAAU,gBAAgB;AAAA,IAC5B,SAAS,OAAO;AACd,cAAQ,IAAI,wCAAwC;AAAA,IACtD;AAAA,EACF;AACA,QAAM,IAAI,cAAc,SAAS,SAAS,MAAM;AAClD;AACA,SAAS,cAAc;AA1EvB;AA2EE,MAAI,OAAO,WAAW,eAAe,OAAO,WAAW;AACrD,UAAM,MAAM;AACZ,QAAI,mBAAmB,SAAO,SAAI,kBAAJ,mBAAmB,WAAU;AACzD,aAAO,GAAG,IAAI,cAAc,SAAS,YAAY,CAAC,YAAY,UAAU,SAAS;AAAA,IACnF;AACA,QAAI,UAAU,UAAU;AACtB,aAAO,GAAG,UAAU,SAAS,YAAY,CAAC,YAAY,UAAU,SAAS;AAAA,IAC3E;AACA,WAAO,mBAAmB,UAAU,SAAS;AAAA,EAC/C,WAAW,OAAO,YAAY,aAAa;AACzC,WAAO,GAAG,QAAQ,IAAI,IAAI,QAAQ,QAAQ,YAAY,QAAQ,OAAO;AAAA,EACvE;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS;AACjC,MAAI,mBAAmB,SAAS;AAC9B,UAAM,MAAM,CAAC;AACb,YAAQ,QAAQ,CAAC,OAAO,QAAQ;AAC9B,UAAI,GAAG,IAAI;AAAA,IACb,CAAC;AACD,WAAO;AAAA,EACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,WAAO,OAAO,YAAY,OAAO;AAAA,EACnC,OAAO;AACL,WAAO,WAAW,CAAC;AAAA,EACrB;AACF;AACA,IAAM,mBAAmB,OAAOA,QAAO,KAAK,UAAU,CAAC,MAAM;AAC3D,QAAM,iBAAiB;AAAA,IACrB,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,cAAc,aAAa,OAAO,KAAK,YAAY,CAAC;AAAA,EACtD;AACA,UAAQ,UAAU,iBAAiB,QAAQ,OAAO;AAClD,QAAM,gBAAgB,OAAO;AAAA,IAC3B,OAAO,QAAQ,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,KAAK,cAAc,EAAE,KAAK,CAAC,eAAe,WAAW,YAAY,MAAM,IAAI,YAAY,CAAC,CAAC;AAAA,EACrJ;AACA,UAAQ,UAAU;AAAA,IAChB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,SAAOA,OAAM,KAAK,OAAO;AAC3B;AACA,IAAM,MAAM,OAAOA,QAAO,MAAM,YAAY;AAC1C,QAAM,WAAW,MAAM,iBAAiBA,QAAO,MAAM;AAAA,IACnD,SAAS,mCAAS;AAAA,EACpB,CAAC;AACD,QAAM,QAAQ,QAAQ;AACtB,SAAO;AACT;AACA,IAAM,OAAO,OAAOA,QAAO,MAAM,MAAM,YAAY;AACjD,QAAM,WAAW,CAAC,UAAU;AAC1B,WAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK;AAAA,EAC5E;AACA,QAAM,gBAAgB,SAAS,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI;AAC9D,QAAM,WAAW,MAAM,iBAAiBA,QAAO,MAAM;AAAA,IACnD,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ,mCAAS;AAAA,IACjB,SAAS,mCAAS;AAAA,EACpB,CAAC;AACD,QAAM,QAAQ,QAAQ;AACtB,SAAO;AACT;AACA,IAAM,MAAM,OAAOA,QAAO,MAAM,MAAM,YAAY;AAChD,QAAM,WAAW,MAAM,iBAAiBA,QAAO,MAAM;AAAA,IACnD,QAAQ;AAAA,IACR,MAAM,KAAK,UAAU,IAAI;AAAA,IACzB,SAAS,mCAAS;AAAA,EACpB,CAAC;AACD,QAAM,QAAQ,QAAQ;AACtB,SAAO;AACT;AACA,IAAM,YAAY,iBAAiB,KAAK;AACtC,QAAM,UAAU,IAAI,YAAY,OAAO;AACvC,MAAI,SAAS;AACb,QAAM,SAAS,IAAI,UAAU;AAC7B,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,OAAO,MAAM,IAAI,MAAM,OAAO,KAAK;AACjD,QAAI,MAAM;AACR;AAAA,IACF;AACA,cAAU,QAAQ,OAAO,KAAK;AAC9B,UAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,aAAS,MAAM,IAAI,KAAK;AACxB,eAAW,QAAQ,OAAO;AACxB,UAAI;AACF,cAAM,KAAK,MAAM,IAAI;AAAA,MACvB,SAAS,OAAO;AACd,gBAAQ,KAAK,kBAAkB,IAAI;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACA,aAAW,QAAQ,OAAO,MAAM,IAAI,EAAE,OAAO,CAAC,MAAM,MAAM,EAAE,GAAG;AAC7D,QAAI;AACF,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB,SAAS,OAAO;AACd,cAAQ,KAAK,kBAAkB,IAAI;AAAA,IACrC;AAAA,EACF;AACF;AACA,IAAM,aAAa,CAAC,SAAS;AAC3B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,qBAAqB,KAAK,SAAS,KAAK;AAC5C,MAAI,KAAK,WAAW,GAAG,GAAG;AACxB,WAAO,mBAAmB,IAAI;AAC9B,yBAAqB;AAAA,EACvB;AACA,MAAI,CAAC,oBAAoB;AACvB,WAAO,UAAU,IAAI;AAAA,EACvB;AACA,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,OAAO,IAAI;AACf,MAAI,CAAC,MAAM;AACT,QAAI,CAAC,oBAAoB;AACvB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,IAAI,aAAa,WAAW,QAAQ;AAAA,IAC7C;AAAA,EACF;AACA,MAAI,OAAO;AACX,MAAI,IAAI,UAAU;AAChB,WAAO,IAAI;AACX,QAAI,IAAI,UAAU;AAChB,cAAQ,IAAI,IAAI,QAAQ;AAAA,IAC1B;AACA,YAAQ;AAAA,EACV;AACA,MAAI,gBAAgB,GAAG,IAAI,QAAQ,KAAK,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI,GAAG,IAAI,QAAQ;AAClF,MAAI,cAAc,SAAS,GAAG,GAAG;AAC/B,oBAAgB,cAAc,MAAM,GAAG,EAAE;AAAA,EAC3C;AACA,SAAO;AACT;AAEA,IAAI,YAAY,OAAO;AACvB,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,kBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,SAAO;AACT;AACA,IAAI,WAAW,MAAM,OAAO;AAAA,EAC1B,YAAY,QAAQ;AAClB,kBAAc,MAAM,QAAQ;AAC5B,kBAAc,MAAM,OAAO;AAC3B,kBAAc,MAAM,2BAA2B,CAAC,CAAC;AACjD,SAAK,SAAS;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,iCAAQ;AAAA,IACnB;AACA,QAAI,EAAC,iCAAQ,QAAO;AAClB,WAAK,OAAO,OAAO,YAAW,iCAAQ,SAAQ,WAAW;AAAA,IAC3D;AACA,SAAK,SAAQ,iCAAQ,UAAS;AAAA,EAChC;AAAA;AAAA,EAEA,QAAQ;AACN,eAAW,WAAW,KAAK,yBAAyB;AAClD,cAAQ,MAAM;AAAA,IAChB;AACA,SAAK,wBAAwB,SAAS;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,yBAAyB,UAAU,SAAS;AAChD,YAAQ,SAAS,QAAQ,UAAU;AACnC,UAAM,OAAO,GAAG,KAAK,OAAO,IAAI,QAAQ,QAAQ;AAChD,QAAI,QAAQ,QAAQ;AAClB,YAAM,kBAAkB,IAAI,gBAAgB;AAC5C,YAAM,YAAY,MAAM,KAAK,KAAK,OAAO,MAAM,SAAS;AAAA,QACtD,QAAQ,gBAAgB;AAAA,QACxB,SAAS,KAAK,OAAO;AAAA,MACvB,CAAC;AACD,UAAI,CAAC,UAAU,MAAM;AACnB,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC;AACA,YAAM,MAAM,UAAU,UAAU,IAAI;AACpC,YAAM,yBAAyB,IAAI;AAAA,QACjC;AAAA,QACA;AAAA,QACA,MAAM;AACJ,gBAAM,IAAI,KAAK,wBAAwB,QAAQ,sBAAsB;AACrE,cAAI,IAAI,IAAI;AACV,iBAAK,wBAAwB,OAAO,GAAG,CAAC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AACA,WAAK,wBAAwB,KAAK,sBAAsB;AACxD,aAAO;AAAA,IACT;AACA,UAAM,WAAW,MAAM,KAAK,KAAK,OAAO,MAAM,SAAS;AAAA,MACrD,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY,OAAO;AACvB,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,aAAa,IAAI,WAAW,KAAK;AACvC,UAAI,aAAa;AACjB,YAAM,MAAM,WAAW;AACvB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,sBAAc,OAAO,aAAa,WAAW,CAAC,CAAC;AAAA,MACjD;AACA,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,SAAS,SAAS;AACtB,QAAI,QAAQ,QAAQ;AAClB,cAAQ,SAAS,MAAM,QAAQ,IAAI,QAAQ,OAAO,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,CAAC;AAAA,IACpF;AACA,WAAO,KAAK,yBAAyB,YAAY,OAAO;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,KAAK,SAAS;AAClB,QAAI,QAAQ,UAAU;AACpB,iBAAW,WAAW,QAAQ,UAAU;AACtC,YAAI,QAAQ,QAAQ;AAClB,kBAAQ,SAAS,MAAM,QAAQ;AAAA,YAC7B,QAAQ,OAAO,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,yBAAyB,QAAQ,OAAO;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,SAAS;AACpB,WAAO,KAAK,yBAAyB,UAAU;AAAA,MAC7C,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAK,SAAS;AAClB,WAAO,KAAK,yBAAyB,QAAQ;AAAA,MAC3C,MAAM,QAAQ;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB,UAAU,QAAQ;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAK,SAAS;AAClB,WAAO,KAAK,yBAAyB,QAAQ;AAAA,MAC3C,MAAM,QAAQ;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB,UAAU,QAAQ;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,SAAS;AACpB,UAAM;AAAA,MACJ,KAAK;AAAA,MACL,GAAG,KAAK,OAAO,IAAI;AAAA,MACnB,EAAE,MAAM,QAAQ,MAAM;AAAA,MACtB,EAAE,SAAS,KAAK,OAAO,QAAQ;AAAA,IACjC;AACA,WAAO,EAAE,QAAQ,UAAU;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAK,SAAS;AAClB,UAAM,KAAK,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,aAAa,EAAE,GAAG,QAAQ,GAAG;AAAA,MACrE,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,EAAE,QAAQ,UAAU;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO;AACX,UAAM,WAAW,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,aAAa;AAAA,MACrE,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAK,SAAS;AAClB,UAAM,WAAW,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,aAAa;AAAA,MACtE,GAAG;AAAA,IACL,GAAG;AAAA,MACD,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAM,SAAS;AACnB,UAAM,WAAW,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,cAAc;AAAA,MACvE,GAAG;AAAA,IACL,GAAG;AAAA,MACD,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW,SAAS;AACxB,UAAM,WAAW,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,mBAAmB;AAAA,MAC5E,GAAG;AAAA,IACL,GAAG;AAAA,MACD,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAK;AACT,UAAM,WAAW,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI,WAAW;AAAA,MACnE,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO,MAAM,SAAS,KAAK;AAAA,EAC7B;AACF;AACA,IAAM,UAAU,IAAI,SAAS;;;ADtc7B;AAEA,IAAMC,UAAN,cAAqB,SAAS;AAAA,EAC5B,MAAM,YAAY,OAAO;AACvB,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,OAAO,KAAK,KAAK,EAAE,SAAS,QAAQ;AAAA,IAC7C;AACA,QAAI;AACF,UAAI,eAAAC,QAAG,WAAW,KAAK,GAAG;AACxB,cAAM,aAAa,MAAM,wBAAS,aAAS,0BAAQ,KAAK,CAAC;AACzD,eAAO,OAAO,KAAK,UAAU,EAAE,SAAS,QAAQ;AAAA,MAClD;AAAA,IACF,QAAQ;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,MAAM;AACrB,QAAI;AACF,YAAM,wBAAS,OAAO,IAAI;AAC1B,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,MAAM,OAAO,SAAS;AACpB,QAAI,QAAQ,QAAQ,MAAM,KAAK,eAAW,0BAAQ,QAAQ,IAAI,CAAC,GAAG;AAChE,YAAM,MAAM,sEAAsE;AAAA,IACpF;AACA,QAAI,QAAQ,QAAQ;AAClB,aAAO,MAAM,OAAO,OAAO;AAAA,IAC7B,OAAO;AACL,aAAO,MAAM,OAAO,OAAO;AAAA,IAC7B;AAAA,EACF;AACF;AACA,IAAM,QAAQ,IAAID,QAAO;", "names": ["fetch", "Ollama", "fs"]}