{"version": 3, "sources": ["../../node_modules/@aws-sdk/client-cognito-identity/dist-es/auth/httpAuthSchemeProvider.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/endpoint/EndpointParameters.js", "../../node_modules/@aws-sdk/client-cognito-identity/package.json", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/endpoint/ruleset.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/endpoint/endpointResolver.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/runtimeConfig.shared.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/runtimeConfig.browser.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/auth/httpAuthExtensionConfiguration.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/runtimeExtensions.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/CognitoIdentityClient.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/models/CognitoIdentityServiceException.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/models/models_0.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/protocols/Aws_json1_1.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/CreateIdentityPoolCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/DeleteIdentitiesCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/DeleteIdentityPoolCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/DescribeIdentityCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/DescribeIdentityPoolCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/GetCredentialsForIdentityCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/GetIdCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/GetIdentityPoolRolesCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/GetOpenIdTokenCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/GetOpenIdTokenForDeveloperIdentityCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/GetPrincipalTagAttributeMapCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/ListIdentitiesCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/ListIdentityPoolsCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/ListTagsForResourceCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/LookupDeveloperIdentityCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/MergeDeveloperIdentitiesCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/SetIdentityPoolRolesCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/SetPrincipalTagAttributeMapCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/TagResourceCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/UnlinkDeveloperIdentityCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/UnlinkIdentityCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/UntagResourceCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/UpdateIdentityPoolCommand.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/CognitoIdentity.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/commands/index.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/pagination/Interfaces.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/pagination/ListIdentityPoolsPaginator.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/pagination/index.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/models/index.js", "../../node_modules/@aws-sdk/client-cognito-identity/dist-es/index.js", "../../node_modules/@aws-sdk/credential-provider-cognito-identity/dist-es/loadCognitoIdentity.js"], "sourcesContent": ["import { resolveAwsSdkSigV4Config, } from \"@aws-sdk/core\";\nimport { getSmithyContext, normalizeProvider } from \"@smithy/util-middleware\";\nexport const defaultCognitoIdentityHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: getSmithyContext(context).operation,\n        region: (await normalizeProvider(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"cognito-identity\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nfunction createSmithyApiNoAuthHttpAuthOption(authParameters) {\n    return {\n        schemeId: \"smithy.api#noAuth\",\n    };\n}\nexport const defaultCognitoIdentityHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        case \"GetCredentialsForIdentity\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"GetId\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"GetOpenIdToken\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"UnlinkIdentity\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nexport const resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = resolveAwsSdkSigV4Config(config);\n    return Object.assign(config_0, {\n        authSchemePreference: normalizeProvider(config.authSchemePreference ?? []),\n    });\n};\n", "export const resolveClientEndpointParameters = (options) => {\n    return Object.assign(options, {\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        defaultSigningName: \"cognito-identity\",\n    });\n};\nexport const commonParams = {\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n", "{\n  \"name\": \"@aws-sdk/client-cognito-identity\",\n  \"description\": \"AWS SDK for JavaScript Cognito Identity Client for Node.js, Browser and React Native\",\n  \"version\": \"3.804.0\",\n  \"scripts\": {\n    \"build\": \"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'\",\n    \"build:cjs\": \"node ../../scripts/compilation/inline client-cognito-identity\",\n    \"build:es\": \"tsc -p tsconfig.es.json\",\n    \"build:include:deps\": \"lerna run --scope $npm_package_name --include-dependencies build\",\n    \"build:types\": \"tsc -p tsconfig.types.json\",\n    \"build:types:downlevel\": \"downlevel-dts dist-types dist-types/ts3.4\",\n    \"clean\": \"rimraf ./dist-* && rimraf *.tsbuildinfo\",\n    \"extract:docs\": \"api-extractor run --local\",\n    \"generate:client\": \"node ../../scripts/generate-clients/single-service --solo cognito-identity\",\n    \"test:e2e\": \"yarn g:vitest run -c vitest.config.e2e.ts --mode development\",\n    \"test:e2e:watch\": \"yarn g:vitest watch -c vitest.config.e2e.ts\"\n  },\n  \"main\": \"./dist-cjs/index.js\",\n  \"types\": \"./dist-types/index.d.ts\",\n  \"module\": \"./dist-es/index.js\",\n  \"sideEffects\": false,\n  \"dependencies\": {\n    \"@aws-crypto/sha256-browser\": \"5.2.0\",\n    \"@aws-crypto/sha256-js\": \"5.2.0\",\n    \"@aws-sdk/core\": \"3.804.0\",\n    \"@aws-sdk/credential-provider-node\": \"3.804.0\",\n    \"@aws-sdk/middleware-host-header\": \"3.804.0\",\n    \"@aws-sdk/middleware-logger\": \"3.804.0\",\n    \"@aws-sdk/middleware-recursion-detection\": \"3.804.0\",\n    \"@aws-sdk/middleware-user-agent\": \"3.804.0\",\n    \"@aws-sdk/region-config-resolver\": \"3.804.0\",\n    \"@aws-sdk/types\": \"3.804.0\",\n    \"@aws-sdk/util-endpoints\": \"3.804.0\",\n    \"@aws-sdk/util-user-agent-browser\": \"3.804.0\",\n    \"@aws-sdk/util-user-agent-node\": \"3.804.0\",\n    \"@smithy/config-resolver\": \"^4.1.0\",\n    \"@smithy/core\": \"^3.3.1\",\n    \"@smithy/fetch-http-handler\": \"^5.0.2\",\n    \"@smithy/hash-node\": \"^4.0.2\",\n    \"@smithy/invalid-dependency\": \"^4.0.2\",\n    \"@smithy/middleware-content-length\": \"^4.0.2\",\n    \"@smithy/middleware-endpoint\": \"^4.1.2\",\n    \"@smithy/middleware-retry\": \"^4.1.3\",\n    \"@smithy/middleware-serde\": \"^4.0.3\",\n    \"@smithy/middleware-stack\": \"^4.0.2\",\n    \"@smithy/node-config-provider\": \"^4.0.2\",\n    \"@smithy/node-http-handler\": \"^4.0.4\",\n    \"@smithy/protocol-http\": \"^5.1.0\",\n    \"@smithy/smithy-client\": \"^4.2.2\",\n    \"@smithy/types\": \"^4.2.0\",\n    \"@smithy/url-parser\": \"^4.0.2\",\n    \"@smithy/util-base64\": \"^4.0.0\",\n    \"@smithy/util-body-length-browser\": \"^4.0.0\",\n    \"@smithy/util-body-length-node\": \"^4.0.0\",\n    \"@smithy/util-defaults-mode-browser\": \"^4.0.10\",\n    \"@smithy/util-defaults-mode-node\": \"^4.0.10\",\n    \"@smithy/util-endpoints\": \"^3.0.2\",\n    \"@smithy/util-middleware\": \"^4.0.2\",\n    \"@smithy/util-retry\": \"^4.0.3\",\n    \"@smithy/util-utf8\": \"^4.0.0\",\n    \"tslib\": \"^2.6.2\"\n  },\n  \"devDependencies\": {\n    \"@aws-sdk/client-iam\": \"3.804.0\",\n    \"@tsconfig/node18\": \"18.2.4\",\n    \"@types/chai\": \"^4.2.11\",\n    \"@types/node\": \"^18.19.69\",\n    \"concurrently\": \"7.0.0\",\n    \"downlevel-dts\": \"0.10.1\",\n    \"rimraf\": \"3.0.2\",\n    \"typescript\": \"~5.8.3\"\n  },\n  \"engines\": {\n    \"node\": \">=18.0.0\"\n  },\n  \"typesVersions\": {\n    \"<4.0\": {\n      \"dist-types/*\": [\n        \"dist-types/ts3.4/*\"\n      ]\n    }\n  },\n  \"files\": [\n    \"dist-*/**\"\n  ],\n  \"author\": {\n    \"name\": \"AWS SDK for JavaScript Team\",\n    \"url\": \"https://aws.amazon.com/javascript/\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"browser\": {\n    \"./dist-es/runtimeConfig\": \"./dist-es/runtimeConfig.browser\"\n  },\n  \"react-native\": {\n    \"./dist-es/runtimeConfig\": \"./dist-es/runtimeConfig.native\"\n  },\n  \"homepage\": \"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-cognito-identity\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/aws/aws-sdk-js-v3.git\",\n    \"directory\": \"clients/client-cognito-identity\"\n  }\n}\n", "const w = \"required\", x = \"fn\", y = \"argv\", z = \"ref\";\nconst a = true, b = \"isSet\", c = \"booleanEquals\", d = \"error\", e = \"endpoint\", f = \"tree\", g = \"PartitionResult\", h = \"getAttr\", i = \"stringEquals\", j = { [w]: false, \"type\": \"String\" }, k = { [w]: true, \"default\": false, \"type\": \"Boolean\" }, l = { [z]: \"Endpoint\" }, m = { [x]: c, [y]: [{ [z]: \"UseFIPS\" }, true] }, n = { [x]: c, [y]: [{ [z]: \"UseDualStack\" }, true] }, o = {}, p = { [z]: \"Region\" }, q = { [x]: h, [y]: [{ [z]: g }, \"supportsFIPS\"] }, r = { [z]: g }, s = { [x]: c, [y]: [true, { [x]: h, [y]: [r, \"supportsDualStack\"] }] }, t = [m], u = [n], v = [p];\nconst _data = { version: \"1.0\", parameters: { Region: j, UseDualStack: k, UseFIPS: k, Endpoint: j }, rules: [{ conditions: [{ [x]: b, [y]: [l] }], rules: [{ conditions: t, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", type: d }, { conditions: u, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", type: d }, { endpoint: { url: l, properties: o, headers: o }, type: e }], type: f }, { conditions: [{ [x]: b, [y]: v }], rules: [{ conditions: [{ [x]: \"aws.partition\", [y]: v, assign: g }], rules: [{ conditions: [m, n], rules: [{ conditions: [{ [x]: c, [y]: [a, q] }, s], rules: [{ conditions: [{ [x]: i, [y]: [p, \"us-east-1\"] }], endpoint: { url: \"https://cognito-identity-fips.us-east-1.amazonaws.com\", properties: o, headers: o }, type: e }, { conditions: [{ [x]: i, [y]: [p, \"us-east-2\"] }], endpoint: { url: \"https://cognito-identity-fips.us-east-2.amazonaws.com\", properties: o, headers: o }, type: e }, { conditions: [{ [x]: i, [y]: [p, \"us-west-1\"] }], endpoint: { url: \"https://cognito-identity-fips.us-west-1.amazonaws.com\", properties: o, headers: o }, type: e }, { conditions: [{ [x]: i, [y]: [p, \"us-west-2\"] }], endpoint: { url: \"https://cognito-identity-fips.us-west-2.amazonaws.com\", properties: o, headers: o }, type: e }, { endpoint: { url: \"https://cognito-identity-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: o, headers: o }, type: e }], type: f }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", type: d }], type: f }, { conditions: t, rules: [{ conditions: [{ [x]: c, [y]: [q, a] }], rules: [{ endpoint: { url: \"https://cognito-identity-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: o, headers: o }, type: e }], type: f }, { error: \"FIPS is enabled but this partition does not support FIPS\", type: d }], type: f }, { conditions: u, rules: [{ conditions: [s], rules: [{ conditions: [{ [x]: i, [y]: [\"aws\", { [x]: h, [y]: [r, \"name\"] }] }], endpoint: { url: \"https://cognito-identity.{Region}.amazonaws.com\", properties: o, headers: o }, type: e }, { endpoint: { url: \"https://cognito-identity.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: o, headers: o }, type: e }], type: f }, { error: \"DualStack is enabled but this partition does not support DualStack\", type: d }], type: f }, { endpoint: { url: \"https://cognito-identity.{Region}.{PartitionResult#dnsSuffix}\", properties: o, headers: o }, type: e }], type: f }], type: f }, { error: \"Invalid Configuration: Missing Region\", type: d }] };\nexport const ruleSet = _data;\n", "import { awsEndpointFunctions } from \"@aws-sdk/util-endpoints\";\nimport { customEndpointFunctions, EndpointCache, resolveEndpoint } from \"@smithy/util-endpoints\";\nimport { ruleSet } from \"./ruleset\";\nconst cache = new EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\"],\n});\nexport const defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => resolveEndpoint(ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\ncustomEndpointFunctions.aws = awsEndpointFunctions;\n", "import { AwsSdkSigV4Signer } from \"@aws-sdk/core\";\nimport { NoAuthSigner } from \"@smithy/core\";\nimport { NoOpLogger } from \"@smithy/smithy-client\";\nimport { parseUrl } from \"@smithy/url-parser\";\nimport { fromBase64, toBase64 } from \"@smithy/util-base64\";\nimport { fromUtf8, toUtf8 } from \"@smithy/util-utf8\";\nimport { defaultCognitoIdentityHttpAuthSchemeProvider } from \"./auth/httpAuthSchemeProvider\";\nimport { defaultEndpointResolver } from \"./endpoint/endpointResolver\";\nexport const getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2014-06-30\",\n        base64Decoder: config?.base64Decoder ?? fromBase64,\n        base64Encoder: config?.base64Encoder ?? toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? defaultCognitoIdentityHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new NoAuthSigner(),\n            },\n        ],\n        logger: config?.logger ?? new NoOpLogger(),\n        serviceId: config?.serviceId ?? \"Cognito Identity\",\n        urlParser: config?.urlParser ?? parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? toUtf8,\n    };\n};\n", "import packageInfo from \"../package.json\";\nimport { Sha256 } from \"@aws-crypto/sha256-browser\";\nimport { createDefaultUserAgentProvider } from \"@aws-sdk/util-user-agent-browser\";\nimport { DEFAULT_USE_DUALSTACK_ENDPOINT, DEFAULT_USE_FIPS_ENDPOINT } from \"@smithy/config-resolver\";\nimport { FetchHttpHandler as RequestHandler, streamCollector } from \"@smithy/fetch-http-handler\";\nimport { invalidProvider } from \"@smithy/invalid-dependency\";\nimport { calculateBodyLength } from \"@smithy/util-body-length-browser\";\nimport { DEFAULT_MAX_ATTEMPTS, DEFAULT_RETRY_MODE } from \"@smithy/util-retry\";\nimport { getRuntimeConfig as getSharedRuntimeConfig } from \"./runtimeConfig.shared\";\nimport { loadConfigsForDefaultMode } from \"@smithy/smithy-client\";\nimport { resolveDefaultsModeConfig } from \"@smithy/util-defaults-mode-browser\";\nexport const getRuntimeConfig = (config) => {\n    const defaultsMode = resolveDefaultsModeConfig(config);\n    const defaultConfigProvider = () => defaultsMode().then(loadConfigsForDefaultMode);\n    const clientSharedValues = getSharedRuntimeConfig(config);\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"browser\",\n        defaultsMode,\n        bodyLengthChecker: config?.bodyLengthChecker ?? calculateBodyLength,\n        credentialDefaultProvider: config?.credentialDefaultProvider ?? ((_) => () => Promise.reject(new Error(\"Credential is missing\"))),\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            createDefaultUserAgentProvider({ serviceId: clientSharedValues.serviceId, clientVersion: packageInfo.version }),\n        maxAttempts: config?.maxAttempts ?? DEFAULT_MAX_ATTEMPTS,\n        region: config?.region ?? invalidProvider(\"Region is missing\"),\n        requestHandler: RequestHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ?? (async () => (await defaultConfigProvider()).retryMode || DEFAULT_RETRY_MODE),\n        sha256: config?.sha256 ?? Sha256,\n        streamCollector: config?.streamCollector ?? streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (() => Promise.resolve(DEFAULT_USE_DUALSTACK_ENDPOINT)),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (() => Promise.resolve(DEFAULT_USE_FIPS_ENDPOINT)),\n    };\n};\n", "export const getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nexport const resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n", "import { getAwsRegionExtensionConfiguration, resolveAwsRegionExtensionConfiguration, } from \"@aws-sdk/region-config-resolver\";\nimport { getHttpHandlerExtensionConfiguration, resolveHttpHandlerRuntimeConfig } from \"@smithy/protocol-http\";\nimport { getDefaultExtensionConfiguration, resolveDefaultRuntimeConfig } from \"@smithy/smithy-client\";\nimport { getHttpAuthExtensionConfiguration, resolveHttpAuthRuntimeConfig } from \"./auth/httpAuthExtensionConfiguration\";\nexport const resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = Object.assign(getAwsRegionExtensionConfiguration(runtimeConfig), getDefaultExtensionConfiguration(runtimeConfig), getHttpHandlerExtensionConfiguration(runtimeConfig), getHttpAuthExtensionConfiguration(runtimeConfig));\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return Object.assign(runtimeConfig, resolveAwsRegionExtensionConfiguration(extensionConfiguration), resolveDefaultRuntimeConfig(extensionConfiguration), resolveHttpHandlerRuntimeConfig(extensionConfiguration), resolveHttpAuthRuntimeConfig(extensionConfiguration));\n};\n", "import { getHostHeaderPlugin, resolveHostHeaderConfig, } from \"@aws-sdk/middleware-host-header\";\nimport { getLoggerPlugin } from \"@aws-sdk/middleware-logger\";\nimport { getRecursionDetectionPlugin } from \"@aws-sdk/middleware-recursion-detection\";\nimport { getUserAgentPlugin, resolveUserAgentConfig, } from \"@aws-sdk/middleware-user-agent\";\nimport { resolveRegionConfig } from \"@smithy/config-resolver\";\nimport { DefaultIdentityProviderConfig, getHttpAuthSchemeEndpointRuleSetPlugin, getHttpSigningPlugin, } from \"@smithy/core\";\nimport { getContentLengthPlugin } from \"@smithy/middleware-content-length\";\nimport { resolveEndpointConfig } from \"@smithy/middleware-endpoint\";\nimport { getRetryPlugin, resolveRetryConfig } from \"@smithy/middleware-retry\";\nimport { Client as __Client, } from \"@smithy/smithy-client\";\nimport { defaultCognitoIdentityHttpAuthSchemeParametersProvider, resolveHttpAuthSchemeConfig, } from \"./auth/httpAuthSchemeProvider\";\nimport { resolveClientEndpointParameters, } from \"./endpoint/EndpointParameters\";\nimport { getRuntimeConfig as __getRuntimeConfig } from \"./runtimeConfig\";\nimport { resolveRuntimeExtensions } from \"./runtimeExtensions\";\nexport { __Client };\nexport class CognitoIdentityClient extends __Client {\n    config;\n    constructor(...[configuration]) {\n        const _config_0 = __getRuntimeConfig(configuration || {});\n        super(_config_0);\n        this.initConfig = _config_0;\n        const _config_1 = resolveClientEndpointParameters(_config_0);\n        const _config_2 = resolveUserAgentConfig(_config_1);\n        const _config_3 = resolveRetryConfig(_config_2);\n        const _config_4 = resolveRegionConfig(_config_3);\n        const _config_5 = resolveHostHeaderConfig(_config_4);\n        const _config_6 = resolveEndpointConfig(_config_5);\n        const _config_7 = resolveHttpAuthSchemeConfig(_config_6);\n        const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);\n        this.config = _config_8;\n        this.middlewareStack.use(getUserAgentPlugin(this.config));\n        this.middlewareStack.use(getRetryPlugin(this.config));\n        this.middlewareStack.use(getContentLengthPlugin(this.config));\n        this.middlewareStack.use(getHostHeaderPlugin(this.config));\n        this.middlewareStack.use(getLoggerPlugin(this.config));\n        this.middlewareStack.use(getRecursionDetectionPlugin(this.config));\n        this.middlewareStack.use(getHttpAuthSchemeEndpointRuleSetPlugin(this.config, {\n            httpAuthSchemeParametersProvider: defaultCognitoIdentityHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use(getHttpSigningPlugin(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n", "import { ServiceException as __ServiceException, } from \"@smithy/smithy-client\";\nexport { __ServiceException };\nexport class CognitoIdentityServiceException extends __ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, CognitoIdentityServiceException.prototype);\n    }\n}\n", "import { SENSITIVE_STRING } from \"@smithy/smithy-client\";\nimport { CognitoIdentityServiceException as __BaseException } from \"./CognitoIdentityServiceException\";\nexport const AmbiguousRoleResolutionType = {\n    AUTHENTICATED_ROLE: \"AuthenticatedRole\",\n    DENY: \"Deny\",\n};\nexport class InternalErrorException extends __BaseException {\n    name = \"InternalErrorException\";\n    $fault = \"server\";\n    constructor(opts) {\n        super({\n            name: \"InternalErrorException\",\n            $fault: \"server\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InternalErrorException.prototype);\n    }\n}\nexport class InvalidParameterException extends __BaseException {\n    name = \"InvalidParameterException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"InvalidParameterException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidParameterException.prototype);\n    }\n}\nexport class LimitExceededException extends __BaseException {\n    name = \"LimitExceededException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"LimitExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, LimitExceededException.prototype);\n    }\n}\nexport class NotAuthorizedException extends __BaseException {\n    name = \"NotAuthorizedException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"NotAuthorizedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, NotAuthorizedException.prototype);\n    }\n}\nexport class ResourceConflictException extends __BaseException {\n    name = \"ResourceConflictException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ResourceConflictException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ResourceConflictException.prototype);\n    }\n}\nexport class TooManyRequestsException extends __BaseException {\n    name = \"TooManyRequestsException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"TooManyRequestsException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, TooManyRequestsException.prototype);\n    }\n}\nexport const ErrorCode = {\n    ACCESS_DENIED: \"AccessDenied\",\n    INTERNAL_SERVER_ERROR: \"InternalServerError\",\n};\nexport class ResourceNotFoundException extends __BaseException {\n    name = \"ResourceNotFoundException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ResourceNotFoundException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);\n    }\n}\nexport class ExternalServiceException extends __BaseException {\n    name = \"ExternalServiceException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ExternalServiceException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ExternalServiceException.prototype);\n    }\n}\nexport class InvalidIdentityPoolConfigurationException extends __BaseException {\n    name = \"InvalidIdentityPoolConfigurationException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"InvalidIdentityPoolConfigurationException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidIdentityPoolConfigurationException.prototype);\n    }\n}\nexport const MappingRuleMatchType = {\n    CONTAINS: \"Contains\",\n    EQUALS: \"Equals\",\n    NOT_EQUAL: \"NotEqual\",\n    STARTS_WITH: \"StartsWith\",\n};\nexport const RoleMappingType = {\n    RULES: \"Rules\",\n    TOKEN: \"Token\",\n};\nexport class DeveloperUserAlreadyRegisteredException extends __BaseException {\n    name = \"DeveloperUserAlreadyRegisteredException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"DeveloperUserAlreadyRegisteredException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, DeveloperUserAlreadyRegisteredException.prototype);\n    }\n}\nexport class ConcurrentModificationException extends __BaseException {\n    name = \"ConcurrentModificationException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ConcurrentModificationException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ConcurrentModificationException.prototype);\n    }\n}\nexport const GetCredentialsForIdentityInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const CredentialsFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.SecretKey && { SecretKey: SENSITIVE_STRING }),\n});\nexport const GetCredentialsForIdentityResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Credentials && { Credentials: CredentialsFilterSensitiveLog(obj.Credentials) }),\n});\nexport const GetIdInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Token && { Token: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\nexport const GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Token && { Token: SENSITIVE_STRING }),\n});\nexport const UnlinkIdentityInputFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Logins && { Logins: SENSITIVE_STRING }),\n});\n", "import { loadRestJsonErrorCode, parseJsonBody as parseBody, parseJsonErrorBody as parseErrorBody } from \"@aws-sdk/core\";\nimport { HttpRequest as __HttpRequest } from \"@smithy/protocol-http\";\nimport { _json, collectBody, decorateServiceException as __decorateServiceException, expectNonNull as __expectNonNull, expectNumber as __expectNumber, expectString as __expectString, parseEpochTimestamp as __parseEpochTimestamp, take, withBaseException, } from \"@smithy/smithy-client\";\nimport { CognitoIdentityServiceException as __BaseException } from \"../models/CognitoIdentityServiceException\";\nimport { ConcurrentModificationException, DeveloperUserAlreadyRegisteredException, ExternalServiceException, InternalErrorException, InvalidIdentityPoolConfigurationException, InvalidParameterException, LimitExceededException, NotAuthorizedException, ResourceConflictException, ResourceNotFoundException, TooManyRequestsException, } from \"../models/models_0\";\nexport const se_CreateIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"CreateIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DeleteIdentitiesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DeleteIdentities\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DeleteIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DeleteIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DescribeIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DescribeIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_DescribeIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"DescribeIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetCredentialsForIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetCredentialsForIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetIdCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetId\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetIdentityPoolRolesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetIdentityPoolRoles\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetOpenIdTokenCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetOpenIdToken\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetOpenIdTokenForDeveloperIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetOpenIdTokenForDeveloperIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_GetPrincipalTagAttributeMapCommand = async (input, context) => {\n    const headers = sharedHeaders(\"GetPrincipalTagAttributeMap\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_ListIdentitiesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"ListIdentities\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_ListIdentityPoolsCommand = async (input, context) => {\n    const headers = sharedHeaders(\"ListIdentityPools\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_ListTagsForResourceCommand = async (input, context) => {\n    const headers = sharedHeaders(\"ListTagsForResource\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_LookupDeveloperIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"LookupDeveloperIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_MergeDeveloperIdentitiesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"MergeDeveloperIdentities\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_SetIdentityPoolRolesCommand = async (input, context) => {\n    const headers = sharedHeaders(\"SetIdentityPoolRoles\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_SetPrincipalTagAttributeMapCommand = async (input, context) => {\n    const headers = sharedHeaders(\"SetPrincipalTagAttributeMap\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_TagResourceCommand = async (input, context) => {\n    const headers = sharedHeaders(\"TagResource\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UnlinkDeveloperIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UnlinkDeveloperIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UnlinkIdentityCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UnlinkIdentity\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UntagResourceCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UntagResource\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const se_UpdateIdentityPoolCommand = async (input, context) => {\n    const headers = sharedHeaders(\"UpdateIdentityPool\");\n    let body;\n    body = JSON.stringify(_json(input));\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nexport const de_CreateIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_DeleteIdentitiesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_DeleteIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_DescribeIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_IdentityDescription(data, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_DescribeIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetCredentialsForIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_GetCredentialsForIdentityResponse(data, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetIdCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetIdentityPoolRolesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetOpenIdTokenCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetOpenIdTokenForDeveloperIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_GetPrincipalTagAttributeMapCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_ListIdentitiesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = de_ListIdentitiesResponse(data, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_ListIdentityPoolsCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_ListTagsForResourceCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_LookupDeveloperIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_MergeDeveloperIdentitiesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_SetIdentityPoolRolesCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_SetPrincipalTagAttributeMapCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_TagResourceCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_UnlinkDeveloperIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_UnlinkIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    await collectBody(output.body, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n    };\n    return response;\n};\nexport const de_UntagResourceCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nexport const de_UpdateIdentityPoolCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await parseBody(output.body, context);\n    let contents = {};\n    contents = _json(data);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await parseErrorBody(output.body, context),\n    };\n    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"InternalErrorException\":\n        case \"com.amazonaws.cognitoidentity#InternalErrorException\":\n            throw await de_InternalErrorExceptionRes(parsedOutput, context);\n        case \"InvalidParameterException\":\n        case \"com.amazonaws.cognitoidentity#InvalidParameterException\":\n            throw await de_InvalidParameterExceptionRes(parsedOutput, context);\n        case \"LimitExceededException\":\n        case \"com.amazonaws.cognitoidentity#LimitExceededException\":\n            throw await de_LimitExceededExceptionRes(parsedOutput, context);\n        case \"NotAuthorizedException\":\n        case \"com.amazonaws.cognitoidentity#NotAuthorizedException\":\n            throw await de_NotAuthorizedExceptionRes(parsedOutput, context);\n        case \"ResourceConflictException\":\n        case \"com.amazonaws.cognitoidentity#ResourceConflictException\":\n            throw await de_ResourceConflictExceptionRes(parsedOutput, context);\n        case \"TooManyRequestsException\":\n        case \"com.amazonaws.cognitoidentity#TooManyRequestsException\":\n            throw await de_TooManyRequestsExceptionRes(parsedOutput, context);\n        case \"ResourceNotFoundException\":\n        case \"com.amazonaws.cognitoidentity#ResourceNotFoundException\":\n            throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);\n        case \"ExternalServiceException\":\n        case \"com.amazonaws.cognitoidentity#ExternalServiceException\":\n            throw await de_ExternalServiceExceptionRes(parsedOutput, context);\n        case \"InvalidIdentityPoolConfigurationException\":\n        case \"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException\":\n            throw await de_InvalidIdentityPoolConfigurationExceptionRes(parsedOutput, context);\n        case \"DeveloperUserAlreadyRegisteredException\":\n        case \"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException\":\n            throw await de_DeveloperUserAlreadyRegisteredExceptionRes(parsedOutput, context);\n        case \"ConcurrentModificationException\":\n        case \"com.amazonaws.cognitoidentity#ConcurrentModificationException\":\n            throw await de_ConcurrentModificationExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody,\n                errorCode,\n            });\n    }\n};\nconst de_ConcurrentModificationExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ConcurrentModificationException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_DeveloperUserAlreadyRegisteredExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new DeveloperUserAlreadyRegisteredException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_ExternalServiceExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ExternalServiceException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InternalErrorExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new InternalErrorException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InvalidIdentityPoolConfigurationExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new InvalidIdentityPoolConfigurationException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_InvalidParameterExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new InvalidParameterException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_LimitExceededExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new LimitExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_NotAuthorizedExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new NotAuthorizedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_ResourceConflictExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ResourceConflictException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_ResourceNotFoundExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new ResourceNotFoundException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_TooManyRequestsExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = _json(body);\n    const exception = new TooManyRequestsException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return __decorateServiceException(exception, body);\n};\nconst de_Credentials = (output, context) => {\n    return take(output, {\n        AccessKeyId: __expectString,\n        Expiration: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        SecretKey: __expectString,\n        SessionToken: __expectString,\n    });\n};\nconst de_GetCredentialsForIdentityResponse = (output, context) => {\n    return take(output, {\n        Credentials: (_) => de_Credentials(_, context),\n        IdentityId: __expectString,\n    });\n};\nconst de_IdentitiesList = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_IdentityDescription(entry, context);\n    });\n    return retVal;\n};\nconst de_IdentityDescription = (output, context) => {\n    return take(output, {\n        CreationDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        IdentityId: __expectString,\n        LastModifiedDate: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),\n        Logins: _json,\n    });\n};\nconst de_ListIdentitiesResponse = (output, context) => {\n    return take(output, {\n        Identities: (_) => de_IdentitiesList(_, context),\n        IdentityPoolId: __expectString,\n        NextToken: __expectString,\n    });\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));\nconst throwDefaultError = withBaseException(__BaseException);\nconst buildHttpRpcRequest = async (context, headers, path, resolvedHostname, body) => {\n    const { hostname, protocol = \"https\", port, path: basePath } = await context.endpoint();\n    const contents = {\n        protocol,\n        hostname,\n        port,\n        method: \"POST\",\n        path: basePath.endsWith(\"/\") ? basePath.slice(0, -1) + path : basePath + path,\n        headers,\n    };\n    if (resolvedHostname !== undefined) {\n        contents.hostname = resolvedHostname;\n    }\n    if (body !== undefined) {\n        contents.body = body;\n    }\n    return new __HttpRequest(contents);\n};\nfunction sharedHeaders(operation) {\n    return {\n        \"content-type\": \"application/x-amz-json-1.1\",\n        \"x-amz-target\": `AWSCognitoIdentityService.${operation}`,\n    };\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_CreateIdentityPoolCommand, se_CreateIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class CreateIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"CreateIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"CreateIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_CreateIdentityPoolCommand)\n    .de(de_CreateIdentityPoolCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DeleteIdentitiesCommand, se_DeleteIdentitiesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DeleteIdentitiesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DeleteIdentities\", {})\n    .n(\"CognitoIdentityClient\", \"DeleteIdentitiesCommand\")\n    .f(void 0, void 0)\n    .ser(se_DeleteIdentitiesCommand)\n    .de(de_DeleteIdentitiesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DeleteIdentityPoolCommand, se_DeleteIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DeleteIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DeleteIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"DeleteIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_DeleteIdentityPoolCommand)\n    .de(de_DeleteIdentityPoolCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DescribeIdentityCommand, se_DescribeIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DescribeIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DescribeIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"DescribeIdentityCommand\")\n    .f(void 0, void 0)\n    .ser(se_DescribeIdentityCommand)\n    .de(de_DescribeIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_DescribeIdentityPoolCommand, se_DescribeIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class DescribeIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"DescribeIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"DescribeIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_DescribeIdentityPoolCommand)\n    .de(de_DescribeIdentityPoolCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetCredentialsForIdentityInputFilterSensitiveLog, GetCredentialsForIdentityResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_GetCredentialsForIdentityCommand, se_GetCredentialsForIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetCredentialsForIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetCredentialsForIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"GetCredentialsForIdentityCommand\")\n    .f(GetCredentialsForIdentityInputFilterSensitiveLog, GetCredentialsForIdentityResponseFilterSensitiveLog)\n    .ser(se_GetCredentialsForIdentityCommand)\n    .de(de_GetCredentialsForIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetIdInputFilterSensitiveLog } from \"../models/models_0\";\nimport { de_GetIdCommand, se_GetIdCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetIdCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetId\", {})\n    .n(\"CognitoIdentityClient\", \"GetIdCommand\")\n    .f(GetIdInputFilterSensitiveLog, void 0)\n    .ser(se_GetIdCommand)\n    .de(de_GetIdCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_GetIdentityPoolRolesCommand, se_GetIdentityPoolRolesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetIdentityPoolRolesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetIdentityPoolRoles\", {})\n    .n(\"CognitoIdentityClient\", \"GetIdentityPoolRolesCommand\")\n    .f(void 0, void 0)\n    .ser(se_GetIdentityPoolRolesCommand)\n    .de(de_GetIdentityPoolRolesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetOpenIdTokenInputFilterSensitiveLog, GetOpenIdTokenResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_GetOpenIdTokenCommand, se_GetOpenIdTokenCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetOpenIdTokenCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetOpenIdToken\", {})\n    .n(\"CognitoIdentityClient\", \"GetOpenIdTokenCommand\")\n    .f(GetOpenIdTokenInputFilterSensitiveLog, GetOpenIdTokenResponseFilterSensitiveLog)\n    .ser(se_GetOpenIdTokenCommand)\n    .de(de_GetOpenIdTokenCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog, GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { de_GetOpenIdTokenForDeveloperIdentityCommand, se_GetOpenIdTokenForDeveloperIdentityCommand, } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetOpenIdTokenForDeveloperIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetOpenIdTokenForDeveloperIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"GetOpenIdTokenForDeveloperIdentityCommand\")\n    .f(GetOpenIdTokenForDeveloperIdentityInputFilterSensitiveLog, GetOpenIdTokenForDeveloperIdentityResponseFilterSensitiveLog)\n    .ser(se_GetOpenIdTokenForDeveloperIdentityCommand)\n    .de(de_GetOpenIdTokenForDeveloperIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_GetPrincipalTagAttributeMapCommand, se_GetPrincipalTagAttributeMapCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class GetPrincipalTagAttributeMapCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"GetPrincipalTagAttributeMap\", {})\n    .n(\"CognitoIdentityClient\", \"GetPrincipalTagAttributeMapCommand\")\n    .f(void 0, void 0)\n    .ser(se_GetPrincipalTagAttributeMapCommand)\n    .de(de_GetPrincipalTagAttributeMapCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListIdentitiesCommand, se_ListIdentitiesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class ListIdentitiesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"ListIdentities\", {})\n    .n(\"CognitoIdentityClient\", \"ListIdentitiesCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListIdentitiesCommand)\n    .de(de_ListIdentitiesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListIdentityPoolsCommand, se_ListIdentityPoolsCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class ListIdentityPoolsCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"ListIdentityPools\", {})\n    .n(\"CognitoIdentityClient\", \"ListIdentityPoolsCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListIdentityPoolsCommand)\n    .de(de_ListIdentityPoolsCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_ListTagsForResourceCommand, se_ListTagsForResourceCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class ListTagsForResourceCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"ListTagsForResource\", {})\n    .n(\"CognitoIdentityClient\", \"ListTagsForResourceCommand\")\n    .f(void 0, void 0)\n    .ser(se_ListTagsForResourceCommand)\n    .de(de_ListTagsForResourceCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_LookupDeveloperIdentityCommand, se_LookupDeveloperIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class LookupDeveloperIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"LookupDeveloperIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"LookupDeveloperIdentityCommand\")\n    .f(void 0, void 0)\n    .ser(se_LookupDeveloperIdentityCommand)\n    .de(de_LookupDeveloperIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_MergeDeveloperIdentitiesCommand, se_MergeDeveloperIdentitiesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class MergeDeveloperIdentitiesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"MergeDeveloperIdentities\", {})\n    .n(\"CognitoIdentityClient\", \"MergeDeveloperIdentitiesCommand\")\n    .f(void 0, void 0)\n    .ser(se_MergeDeveloperIdentitiesCommand)\n    .de(de_MergeDeveloperIdentitiesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_SetIdentityPoolRolesCommand, se_SetIdentityPoolRolesCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class SetIdentityPoolRolesCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"SetIdentityPoolRoles\", {})\n    .n(\"CognitoIdentityClient\", \"SetIdentityPoolRolesCommand\")\n    .f(void 0, void 0)\n    .ser(se_SetIdentityPoolRolesCommand)\n    .de(de_SetIdentityPoolRolesCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_SetPrincipalTagAttributeMapCommand, se_SetPrincipalTagAttributeMapCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class SetPrincipalTagAttributeMapCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"SetPrincipalTagAttributeMap\", {})\n    .n(\"CognitoIdentityClient\", \"SetPrincipalTagAttributeMapCommand\")\n    .f(void 0, void 0)\n    .ser(se_SetPrincipalTagAttributeMapCommand)\n    .de(de_SetPrincipalTagAttributeMapCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_TagResourceCommand, se_TagResourceCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class TagResourceCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"TagResource\", {})\n    .n(\"CognitoIdentityClient\", \"TagResourceCommand\")\n    .f(void 0, void 0)\n    .ser(se_TagResourceCommand)\n    .de(de_TagResourceCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_UnlinkDeveloperIdentityCommand, se_UnlinkDeveloperIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UnlinkDeveloperIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UnlinkDeveloperIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"UnlinkDeveloperIdentityCommand\")\n    .f(void 0, void 0)\n    .ser(se_UnlinkDeveloperIdentityCommand)\n    .de(de_UnlinkDeveloperIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { UnlinkIdentityInputFilterSensitiveLog } from \"../models/models_0\";\nimport { de_UnlinkIdentityCommand, se_UnlinkIdentityCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UnlinkIdentityCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UnlinkIdentity\", {})\n    .n(\"CognitoIdentityClient\", \"UnlinkIdentityCommand\")\n    .f(UnlinkIdentityInputFilterSensitiveLog, void 0)\n    .ser(se_UnlinkIdentityCommand)\n    .de(de_UnlinkIdentityCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_UntagResourceCommand, se_UntagResourceCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UntagResourceCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UntagResource\", {})\n    .n(\"CognitoIdentityClient\", \"UntagResourceCommand\")\n    .f(void 0, void 0)\n    .ser(se_UntagResourceCommand)\n    .de(de_UntagResourceCommand)\n    .build() {\n}\n", "import { getEndpointPlugin } from \"@smithy/middleware-endpoint\";\nimport { getSerdePlugin } from \"@smithy/middleware-serde\";\nimport { Command as $Command } from \"@smithy/smithy-client\";\nimport { commonParams } from \"../endpoint/EndpointParameters\";\nimport { de_UpdateIdentityPoolCommand, se_UpdateIdentityPoolCommand } from \"../protocols/Aws_json1_1\";\nexport { $Command };\nexport class UpdateIdentityPoolCommand extends $Command\n    .classBuilder()\n    .ep(commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        getSerdePlugin(config, this.serialize, this.deserialize),\n        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSCognitoIdentityService\", \"UpdateIdentityPool\", {})\n    .n(\"CognitoIdentityClient\", \"UpdateIdentityPoolCommand\")\n    .f(void 0, void 0)\n    .ser(se_UpdateIdentityPoolCommand)\n    .de(de_UpdateIdentityPoolCommand)\n    .build() {\n}\n", "import { createAggregatedClient } from \"@smithy/smithy-client\";\nimport { CognitoIdentityClient } from \"./CognitoIdentityClient\";\nimport { CreateIdentityPoolCommand, } from \"./commands/CreateIdentityPoolCommand\";\nimport { DeleteIdentitiesCommand, } from \"./commands/DeleteIdentitiesCommand\";\nimport { DeleteIdentityPoolCommand, } from \"./commands/DeleteIdentityPoolCommand\";\nimport { DescribeIdentityCommand, } from \"./commands/DescribeIdentityCommand\";\nimport { DescribeIdentityPoolCommand, } from \"./commands/DescribeIdentityPoolCommand\";\nimport { GetCredentialsForIdentityCommand, } from \"./commands/GetCredentialsForIdentityCommand\";\nimport { GetIdCommand } from \"./commands/GetIdCommand\";\nimport { GetIdentityPoolRolesCommand, } from \"./commands/GetIdentityPoolRolesCommand\";\nimport { GetOpenIdTokenCommand, } from \"./commands/GetOpenIdTokenCommand\";\nimport { GetOpenIdTokenForDeveloperIdentityCommand, } from \"./commands/GetOpenIdTokenForDeveloperIdentityCommand\";\nimport { GetPrincipalTagAttributeMapCommand, } from \"./commands/GetPrincipalTagAttributeMapCommand\";\nimport { ListIdentitiesCommand, } from \"./commands/ListIdentitiesCommand\";\nimport { ListIdentityPoolsCommand, } from \"./commands/ListIdentityPoolsCommand\";\nimport { ListTagsForResourceCommand, } from \"./commands/ListTagsForResourceCommand\";\nimport { LookupDeveloperIdentityCommand, } from \"./commands/LookupDeveloperIdentityCommand\";\nimport { MergeDeveloperIdentitiesCommand, } from \"./commands/MergeDeveloperIdentitiesCommand\";\nimport { SetIdentityPoolRolesCommand, } from \"./commands/SetIdentityPoolRolesCommand\";\nimport { SetPrincipalTagAttributeMapCommand, } from \"./commands/SetPrincipalTagAttributeMapCommand\";\nimport { TagResourceCommand } from \"./commands/TagResourceCommand\";\nimport { UnlinkDeveloperIdentityCommand, } from \"./commands/UnlinkDeveloperIdentityCommand\";\nimport { UnlinkIdentityCommand, } from \"./commands/UnlinkIdentityCommand\";\nimport { UntagResourceCommand, } from \"./commands/UntagResourceCommand\";\nimport { UpdateIdentityPoolCommand, } from \"./commands/UpdateIdentityPoolCommand\";\nconst commands = {\n    CreateIdentityPoolCommand,\n    DeleteIdentitiesCommand,\n    DeleteIdentityPoolCommand,\n    DescribeIdentityCommand,\n    DescribeIdentityPoolCommand,\n    GetCredentialsForIdentityCommand,\n    GetIdCommand,\n    GetIdentityPoolRolesCommand,\n    GetOpenIdTokenCommand,\n    GetOpenIdTokenForDeveloperIdentityCommand,\n    GetPrincipalTagAttributeMapCommand,\n    ListIdentitiesCommand,\n    ListIdentityPoolsCommand,\n    ListTagsForResourceCommand,\n    LookupDeveloperIdentityCommand,\n    MergeDeveloperIdentitiesCommand,\n    SetIdentityPoolRolesCommand,\n    SetPrincipalTagAttributeMapCommand,\n    TagResourceCommand,\n    UnlinkDeveloperIdentityCommand,\n    UnlinkIdentityCommand,\n    UntagResourceCommand,\n    UpdateIdentityPoolCommand,\n};\nexport class CognitoIdentity extends CognitoIdentityClient {\n}\ncreateAggregatedClient(commands, CognitoIdentity);\n", "export * from \"./CreateIdentityPoolCommand\";\nexport * from \"./DeleteIdentitiesCommand\";\nexport * from \"./DeleteIdentityPoolCommand\";\nexport * from \"./DescribeIdentityCommand\";\nexport * from \"./DescribeIdentityPoolCommand\";\nexport * from \"./GetCredentialsForIdentityCommand\";\nexport * from \"./GetIdCommand\";\nexport * from \"./GetIdentityPoolRolesCommand\";\nexport * from \"./GetOpenIdTokenCommand\";\nexport * from \"./GetOpenIdTokenForDeveloperIdentityCommand\";\nexport * from \"./GetPrincipalTagAttributeMapCommand\";\nexport * from \"./ListIdentitiesCommand\";\nexport * from \"./ListIdentityPoolsCommand\";\nexport * from \"./ListTagsForResourceCommand\";\nexport * from \"./LookupDeveloperIdentityCommand\";\nexport * from \"./MergeDeveloperIdentitiesCommand\";\nexport * from \"./SetIdentityPoolRolesCommand\";\nexport * from \"./SetPrincipalTagAttributeMapCommand\";\nexport * from \"./TagResourceCommand\";\nexport * from \"./UnlinkDeveloperIdentityCommand\";\nexport * from \"./UnlinkIdentityCommand\";\nexport * from \"./UntagResourceCommand\";\nexport * from \"./UpdateIdentityPoolCommand\";\n", "export {};\n", "import { createPaginator } from \"@smithy/core\";\nimport { CognitoIdentityClient } from \"../CognitoIdentityClient\";\nimport { ListIdentityPoolsCommand, } from \"../commands/ListIdentityPoolsCommand\";\nexport const paginateListIdentityPools = createPaginator(CognitoIdentityClient, ListIdentityPoolsCommand, \"NextToken\", \"NextToken\", \"MaxResults\");\n", "export * from \"./Interfaces\";\nexport * from \"./ListIdentityPoolsPaginator\";\n", "export * from \"./models_0\";\n", "export * from \"./CognitoIdentityClient\";\nexport * from \"./CognitoIdentity\";\nexport * from \"./commands\";\nexport * from \"./pagination\";\nexport * from \"./models\";\nexport { CognitoIdentityServiceException } from \"./models/CognitoIdentityServiceException\";\n", "import { CognitoIdentityClient, GetCredentialsForIdentityCommand, GetIdCommand, } from \"@aws-sdk/client-cognito-identity\";\nexport { CognitoIdentityClient, GetCredentialsForIdentityCommand, GetIdCommand };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,SAAS,iCAAiC,gBAAgB;AACtD,SAAO;AAAA,IACH,UAAU;AAAA,IACV,mBAAmB;AAAA,MACf,MAAM;AAAA,MACN,QAAQ,eAAe;AAAA,IAC3B;AAAA,IACA,qBAAqB,CAAC,QAAQ,aAAa;AAAA,MACvC,mBAAmB;AAAA,QACf;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,oCAAoC,gBAAgB;AACzD,SAAO;AAAA,IACH,UAAU;AAAA,EACd;AACJ;AA9BA,IAEa,wDA6BA,8CAyBA;AAxDb;AAAA;AAAA,IAAAA;AACA,IAAAA;AACO,IAAM,yDAAyD,OAAO,QAAQ,SAAS,UAAU;AACpG,aAAO;AAAA,QACH,WAAW,iBAAiB,OAAO,EAAE;AAAA,QACrC,QAAS,MAAM,kBAAkB,OAAO,MAAM,EAAE,MAC3C,MAAM;AACH,gBAAM,IAAI,MAAM,yDAAyD;AAAA,QAC7E,GAAG;AAAA,MACX;AAAA,IACJ;AAqBO,IAAM,+CAA+C,CAAC,mBAAmB;AAC5E,YAAM,UAAU,CAAC;AACjB,cAAQ,eAAe,WAAW;AAAA,QAC9B,KAAK,6BAA6B;AAC9B,kBAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,QACJ;AAAA,QACA,KAAK,SAAS;AACV,kBAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,QACJ;AAAA,QACA,KAAK,kBAAkB;AACnB,kBAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,QACJ;AAAA,QACA,KAAK,kBAAkB;AACnB,kBAAQ,KAAK,oCAAoC,cAAc,CAAC;AAChE;AAAA,QACJ;AAAA,QACA,SAAS;AACL,kBAAQ,KAAK,iCAAiC,cAAc,CAAC;AAAA,QACjE;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACO,IAAM,8BAA8B,CAAC,WAAW;AACnD,YAAM,WAAW,yBAAyB,MAAM;AAChD,aAAO,OAAO,OAAO,UAAU;AAAA,QAC3B,sBAAsB,kBAAkB,OAAO,wBAAwB,CAAC,CAAC;AAAA,MAC7E,CAAC;AAAA,IACL;AAAA;AAAA;;;AC7DA,IAAa,iCAOA;AAPb;AAAA;AAAO,IAAM,kCAAkC,CAAC,YAAY;AACxD,aAAO,OAAO,OAAO,SAAS;AAAA,QAC1B,sBAAsB,QAAQ,wBAAwB;AAAA,QACtD,iBAAiB,QAAQ,mBAAmB;AAAA,QAC5C,oBAAoB;AAAA,MACxB,CAAC;AAAA,IACL;AACO,IAAM,eAAe;AAAA,MACxB,SAAS,EAAE,MAAM,iBAAiB,MAAM,kBAAkB;AAAA,MAC1D,UAAU,EAAE,MAAM,iBAAiB,MAAM,WAAW;AAAA,MACpD,QAAQ,EAAE,MAAM,iBAAiB,MAAM,SAAS;AAAA,MAChD,cAAc,EAAE,MAAM,iBAAiB,MAAM,uBAAuB;AAAA,IACxE;AAAA;AAAA;;;ACZA;AAAA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,aAAe;AAAA,MACf,SAAW;AAAA,MACX,SAAW;AAAA,QACT,OAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,sBAAsB;AAAA,QACtB,eAAe;AAAA,QACf,yBAAyB;AAAA,QACzB,OAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB;AAAA,MACA,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,QAAU;AAAA,MACV,aAAe;AAAA,MACf,cAAgB;AAAA,QACd,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,qCAAqC;AAAA,QACrC,mCAAmC;AAAA,QACnC,8BAA8B;AAAA,QAC9B,2CAA2C;AAAA,QAC3C,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,kBAAkB;AAAA,QAClB,2BAA2B;AAAA,QAC3B,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,2BAA2B;AAAA,QAC3B,gBAAgB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,qBAAqB;AAAA,QACrB,8BAA8B;AAAA,QAC9B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,4BAA4B;AAAA,QAC5B,4BAA4B;AAAA,QAC5B,4BAA4B;AAAA,QAC5B,gCAAgC;AAAA,QAChC,6BAA6B;AAAA,QAC7B,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,QACvB,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,sCAAsC;AAAA,QACtC,mCAAmC;AAAA,QACnC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,OAAS;AAAA,MACX;AAAA,MACA,iBAAmB;AAAA,QACjB,uBAAuB;AAAA,QACvB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,eAAe;AAAA,QACf,cAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,QAAU;AAAA,QACV,YAAc;AAAA,MAChB;AAAA,MACA,SAAW;AAAA,QACT,MAAQ;AAAA,MACV;AAAA,MACA,eAAiB;AAAA,QACf,QAAQ;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,SAAW;AAAA,MACX,SAAW;AAAA,QACT,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,2BAA2B;AAAA,MAC7B;AAAA,MACA,UAAY;AAAA,MACZ,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,WAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACtGA,IAAM,GAAgB,GAAU,GAAY,GACtC,GAAU,GAAa,GAAqB,GAAa,GAAgB,GAAY,GAAuB,GAAe,GAAoB,GAAsC,GAAwD,GAAyB,GAAiD,GAAsD,GAAQ,GAAuB,GAAmD,GAAgB,GAAwE,GAAS,GAAS,GACziB,OACO;AAHb;AAAA;AAAA,IAAM,IAAI;AAAV,IAAsB,IAAI;AAA1B,IAAgC,IAAI;AAApC,IAA4C,IAAI;AAChD,IAAM,IAAI;AAAV,IAAgB,IAAI;AAApB,IAA6B,IAAI;AAAjC,IAAkD,IAAI;AAAtD,IAA+D,IAAI;AAAnE,IAA+E,IAAI;AAAnF,IAA2F,IAAI;AAA/F,IAAkH,IAAI;AAAtH,IAAiI,IAAI;AAArI,IAAqJ,IAAI,EAAE,CAAC,CAAC,GAAG,OAAO,QAAQ,SAAS;AAAxL,IAA2L,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,WAAW,OAAO,QAAQ,UAAU;AAAhP,IAAmP,IAAI,EAAE,CAAC,CAAC,GAAG,WAAW;AAAzQ,IAA4Q,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE;AAA1T,IAA6T,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,GAAG,IAAI,EAAE;AAAhX,IAAmX,IAAI,CAAC;AAAxX,IAA2X,IAAI,EAAE,CAAC,CAAC,GAAG,SAAS;AAA/Y,IAAkZ,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,cAAc,EAAE;AAAlc,IAAqc,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE;AAAld,IAAqd,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,mBAAmB,EAAE,CAAC,EAAE;AAA1hB,IAA6hB,IAAI,CAAC,CAAC;AAAniB,IAAsiB,IAAI,CAAC,CAAC;AAA5iB,IAA+iB,IAAI,CAAC,CAAC;AACrjB,IAAM,QAAQ,EAAE,SAAS,OAAO,YAAY,EAAE,QAAQ,GAAG,cAAc,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,qEAAqE,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,0EAA0E,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,GAAG,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,EAAE,KAAK,yDAAyD,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,EAAE,KAAK,yDAAyD,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,EAAE,KAAK,yDAAyD,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,UAAU,EAAE,KAAK,yDAAyD,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,+EAA+E,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,mFAAmF,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,sEAAsE,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,4DAA4D,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,KAAK,mDAAmD,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,0EAA0E,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,sEAAsE,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,iEAAiE,YAAY,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE,OAAO,yCAAyC,MAAM,EAAE,CAAC,EAAE;AACt/E,IAAM,UAAU;AAAA;AAAA;;;ACHvB,IAGM,OAIO;AAPb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA;AACA,IAAM,QAAQ,IAAI,cAAc;AAAA,MAC5B,MAAM;AAAA,MACN,QAAQ,CAAC,YAAY,UAAU,gBAAgB,SAAS;AAAA,IAC5D,CAAC;AACM,IAAM,0BAA0B,CAAC,gBAAgB,UAAU,CAAC,MAAM;AACrE,aAAO,MAAM,IAAI,gBAAgB,MAAM,gBAAgB,SAAS;AAAA,QAC5D;AAAA,QACA,QAAQ,QAAQ;AAAA,MACpB,CAAC,CAAC;AAAA,IACN;AACA,4BAAwB,MAAM;AAAA;AAAA;;;ACb9B,IAQa;AARb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACO,IAAM,mBAAmB,CAAC,WAAW;AACxC,aAAO;AAAA,QACH,YAAY;AAAA,QACZ,gBAAe,iCAAQ,kBAAiB;AAAA,QACxC,gBAAe,iCAAQ,kBAAiB;AAAA,QACxC,oBAAmB,iCAAQ,sBAAqB;AAAA,QAChD,mBAAkB,iCAAQ,qBAAoB;AAAA,QAC9C,aAAY,iCAAQ,eAAc,CAAC;AAAA,QACnC,yBAAwB,iCAAQ,2BAA0B;AAAA,QAC1D,kBAAiB,iCAAQ,oBAAmB;AAAA,UACxC;AAAA,YACI,UAAU;AAAA,YACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,gBAAgB;AAAA,YACnE,QAAQ,IAAI,kBAAkB;AAAA,UAClC;AAAA,UACA;AAAA,YACI,UAAU;AAAA,YACV,kBAAkB,CAAC,QAAQ,IAAI,oBAAoB,mBAAmB,MAAM,aAAa,CAAC;AAAA,YAC1F,QAAQ,IAAI,aAAa;AAAA,UAC7B;AAAA,QACJ;AAAA,QACA,SAAQ,iCAAQ,WAAU,IAAI,WAAW;AAAA,QACzC,YAAW,iCAAQ,cAAa;AAAA,QAChC,YAAW,iCAAQ,cAAa;AAAA,QAChC,cAAa,iCAAQ,gBAAe;AAAA,QACpC,cAAa,iCAAQ,gBAAe;AAAA,MACxC;AAAA,IACJ;AAAA;AAAA;;;ACnCA,IAWaC;AAXb;AAAA;AAAA;AACA;AACA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA,IAAAA;AACA,IAAAA;AACO,IAAMD,oBAAmB,CAAC,WAAW;AACxC,YAAM,eAAe,0BAA0B,MAAM;AACrD,YAAM,wBAAwB,MAAM,aAAa,EAAE,KAAK,yBAAyB;AACjF,YAAM,qBAAqB,iBAAuB,MAAM;AACxD,aAAO;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,SAAS;AAAA,QACT;AAAA,QACA,oBAAmB,iCAAQ,sBAAqB;AAAA,QAChD,4BAA2B,iCAAQ,+BAA8B,CAAC,MAAM,MAAM,QAAQ,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAAA,QAC/H,2BAA0B,iCAAQ,6BAC9B,+BAA+B,EAAE,WAAW,mBAAmB,WAAW,eAAe,gBAAY,QAAQ,CAAC;AAAA,QAClH,cAAa,iCAAQ,gBAAe;AAAA,QACpC,SAAQ,iCAAQ,WAAU,gBAAgB,mBAAmB;AAAA,QAC7D,gBAAgB,iBAAe,QAAO,iCAAQ,mBAAkB,qBAAqB;AAAA,QACrF,YAAW,iCAAQ,eAAc,aAAa,MAAM,sBAAsB,GAAG,aAAa;AAAA,QAC1F,SAAQ,iCAAQ,WAAU;AAAA,QAC1B,kBAAiB,iCAAQ,oBAAmB;AAAA,QAC5C,uBAAsB,iCAAQ,0BAAyB,MAAM,QAAQ,QAAQ,8BAA8B;AAAA,QAC3G,kBAAiB,iCAAQ,qBAAoB,MAAM,QAAQ,QAAQ,yBAAyB;AAAA,MAChG;AAAA,IACJ;AAAA;AAAA;;;ACjCA,IAAa,mCA+BA;AA/Bb;AAAA;AAAO,IAAM,oCAAoC,CAAC,kBAAkB;AAChE,YAAM,mBAAmB,cAAc;AACvC,UAAI,0BAA0B,cAAc;AAC5C,UAAI,eAAe,cAAc;AACjC,aAAO;AAAA,QACH,kBAAkB,gBAAgB;AAC9B,gBAAM,QAAQ,iBAAiB,UAAU,CAAC,WAAW,OAAO,aAAa,eAAe,QAAQ;AAChG,cAAI,UAAU,IAAI;AACd,6BAAiB,KAAK,cAAc;AAAA,UACxC,OACK;AACD,6BAAiB,OAAO,OAAO,GAAG,cAAc;AAAA,UACpD;AAAA,QACJ;AAAA,QACA,kBAAkB;AACd,iBAAO;AAAA,QACX;AAAA,QACA,0BAA0B,wBAAwB;AAC9C,oCAA0B;AAAA,QAC9B;AAAA,QACA,yBAAyB;AACrB,iBAAO;AAAA,QACX;AAAA,QACA,eAAe,aAAa;AACxB,yBAAe;AAAA,QACnB;AAAA,QACA,cAAc;AACV,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACO,IAAM,+BAA+B,CAAC,WAAW;AACpD,aAAO;AAAA,QACH,iBAAiB,OAAO,gBAAgB;AAAA,QACxC,wBAAwB,OAAO,uBAAuB;AAAA,QACtD,aAAa,OAAO,YAAY;AAAA,MACpC;AAAA,IACJ;AAAA;AAAA;;;ACrCA,IAIa;AAJb;AAAA;AAAA,IAAAE;AACA;AACA,IAAAA;AACA;AACO,IAAM,2BAA2B,CAAC,eAAe,eAAe;AACnE,YAAM,yBAAyB,OAAO,OAAO,mCAAmC,aAAa,GAAG,iCAAiC,aAAa,GAAG,qCAAqC,aAAa,GAAG,kCAAkC,aAAa,CAAC;AACtP,iBAAW,QAAQ,CAAC,cAAc,UAAU,UAAU,sBAAsB,CAAC;AAC7E,aAAO,OAAO,OAAO,eAAe,uCAAuC,sBAAsB,GAAG,4BAA4B,sBAAsB,GAAG,gCAAgC,sBAAsB,GAAG,6BAA6B,sBAAsB,CAAC;AAAA,IAC1Q;AAAA;AAAA;;;ACRA,IAea;AAfb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AACA;AAEO,IAAM,wBAAN,cAAoC,OAAS;AAAA,MAEhD,eAAe,CAAC,aAAa,GAAG;AAC5B,cAAM,YAAYC,kBAAmB,iBAAiB,CAAC,CAAC;AACxD,cAAM,SAAS;AAHnB;AAII,aAAK,aAAa;AAClB,cAAM,YAAY,gCAAgC,SAAS;AAC3D,cAAM,YAAY,uBAAuB,SAAS;AAClD,cAAM,YAAY,mBAAmB,SAAS;AAC9C,cAAM,YAAY,oBAAoB,SAAS;AAC/C,cAAM,YAAY,wBAAwB,SAAS;AACnD,cAAM,YAAY,sBAAsB,SAAS;AACjD,cAAM,YAAY,4BAA4B,SAAS;AACvD,cAAM,YAAY,yBAAyB,YAAW,+CAAe,eAAc,CAAC,CAAC;AACrF,aAAK,SAAS;AACd,aAAK,gBAAgB,IAAI,mBAAmB,KAAK,MAAM,CAAC;AACxD,aAAK,gBAAgB,IAAI,eAAe,KAAK,MAAM,CAAC;AACpD,aAAK,gBAAgB,IAAI,uBAAuB,KAAK,MAAM,CAAC;AAC5D,aAAK,gBAAgB,IAAI,oBAAoB,KAAK,MAAM,CAAC;AACzD,aAAK,gBAAgB,IAAI,gBAAgB,KAAK,MAAM,CAAC;AACrD,aAAK,gBAAgB,IAAI,4BAA4B,KAAK,MAAM,CAAC;AACjE,aAAK,gBAAgB,IAAI,uCAAuC,KAAK,QAAQ;AAAA,UACzE,kCAAkC;AAAA,UAClC,gCAAgC,OAAO,WAAW,IAAI,8BAA8B;AAAA,YAChF,kBAAkB,OAAO;AAAA,UAC7B,CAAC;AAAA,QACL,CAAC,CAAC;AACF,aAAK,gBAAgB,IAAI,qBAAqB,KAAK,MAAM,CAAC;AAAA,MAC9D;AAAA,MACA,UAAU;AACN,cAAM,QAAQ;AAAA,MAClB;AAAA,IACJ;AAAA;AAAA;;;AC/CA,IAEa;AAFb;AAAA;AAAA,IAAAC;AAEO,IAAM,kCAAN,MAAM,yCAAwC,iBAAmB;AAAA,MACpE,YAAY,SAAS;AACjB,cAAM,OAAO;AACb,eAAO,eAAe,MAAM,iCAAgC,SAAS;AAAA,MACzE;AAAA,IACJ;AAAA;AAAA;;;ACPA,IAMa,wBAYA,2BAYA,wBAYA,wBAYA,2BAYA,0BAgBA,2BAYA,0BAYA,2CAsBA,yCAYA,iCAYA,kDAIA,+BAIA,qDAIA,8BAIA,uCAIA,0CAIA,2DAIA,8DAIA;AAxLb;AAAA;AAAA,IAAAC;AACA;AAKO,IAAM,yBAAN,MAAM,gCAA+B,gCAAgB;AAAA,MAGxD,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,MAChE;AAAA,IACJ;AACO,IAAM,4BAAN,MAAM,mCAAkC,gCAAgB;AAAA,MAG3D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,MACnE;AAAA,IACJ;AACO,IAAM,yBAAN,MAAM,gCAA+B,gCAAgB;AAAA,MAGxD,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,MAChE;AAAA,IACJ;AACO,IAAM,yBAAN,MAAM,gCAA+B,gCAAgB;AAAA,MAGxD,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,wBAAuB,SAAS;AAAA,MAChE;AAAA,IACJ;AACO,IAAM,4BAAN,MAAM,mCAAkC,gCAAgB;AAAA,MAG3D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,MACnE;AAAA,IACJ;AACO,IAAM,2BAAN,MAAM,kCAAiC,gCAAgB;AAAA,MAG1D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,MAClE;AAAA,IACJ;AAKO,IAAM,4BAAN,MAAM,mCAAkC,gCAAgB;AAAA,MAG3D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,2BAA0B,SAAS;AAAA,MACnE;AAAA,IACJ;AACO,IAAM,2BAAN,MAAM,kCAAiC,gCAAgB;AAAA,MAG1D,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,0BAAyB,SAAS;AAAA,MAClE;AAAA,IACJ;AACO,IAAM,4CAAN,MAAM,mDAAkD,gCAAgB;AAAA,MAG3E,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,2CAA0C,SAAS;AAAA,MACnF;AAAA,IACJ;AAWO,IAAM,0CAAN,MAAM,iDAAgD,gCAAgB;AAAA,MAGzE,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,yCAAwC,SAAS;AAAA,MACjF;AAAA,IACJ;AACO,IAAM,kCAAN,MAAM,yCAAwC,gCAAgB;AAAA,MAGjE,YAAY,MAAM;AACd,cAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,GAAG;AAAA,QACP,CAAC;AAPL,oCAAO;AACP,sCAAS;AAOL,eAAO,eAAe,MAAM,iCAAgC,SAAS;AAAA,MACzE;AAAA,IACJ;AACO,IAAM,mDAAmD,CAAC,SAAS;AAAA,MACtE,GAAG;AAAA,MACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AAAA,IACjD;AACO,IAAM,gCAAgC,CAAC,SAAS;AAAA,MACnD,GAAG;AAAA,MACH,GAAI,IAAI,aAAa,EAAE,WAAW,iBAAiB;AAAA,IACvD;AACO,IAAM,sDAAsD,CAAC,SAAS;AAAA,MACzE,GAAG;AAAA,MACH,GAAI,IAAI,eAAe,EAAE,aAAa,8BAA8B,IAAI,WAAW,EAAE;AAAA,IACzF;AACO,IAAM,+BAA+B,CAAC,SAAS;AAAA,MAClD,GAAG;AAAA,MACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AAAA,IACjD;AACO,IAAM,wCAAwC,CAAC,SAAS;AAAA,MAC3D,GAAG;AAAA,MACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AAAA,IACjD;AACO,IAAM,2CAA2C,CAAC,SAAS;AAAA,MAC9D,GAAG;AAAA,MACH,GAAI,IAAI,SAAS,EAAE,OAAO,iBAAiB;AAAA,IAC/C;AACO,IAAM,4DAA4D,CAAC,SAAS;AAAA,MAC/E,GAAG;AAAA,MACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AAAA,IACjD;AACO,IAAM,+DAA+D,CAAC,SAAS;AAAA,MAClF,GAAG;AAAA,MACH,GAAI,IAAI,SAAS,EAAE,OAAO,iBAAiB;AAAA,IAC/C;AACO,IAAM,wCAAwC,CAAC,SAAS;AAAA,MAC3D,GAAG;AAAA,MACH,GAAI,IAAI,UAAU,EAAE,QAAQ,iBAAiB;AAAA,IACjD;AAAA;AAAA;;;ACscA,SAAS,cAAc,WAAW;AAC9B,SAAO;AAAA,IACH,gBAAgB;AAAA,IAChB,gBAAgB,6BAA6B,SAAS;AAAA,EAC1D;AACJ;AAtoBA,IAKa,8BAMA,4BAMA,8BAMA,4BAMA,gCAMA,qCAMA,iBAMA,gCAMA,0BAMA,8CAMA,uCAMA,0BAMA,6BAMA,+BAMA,mCAMA,oCAMA,gCAMA,uCAMA,uBAMA,mCAMA,0BAMA,yBAMA,8BAMA,8BAaA,4BAaA,8BAUA,4BAaA,gCAaA,qCAaA,iBAaA,gCAaA,0BAaA,8CAaA,uCAaA,0BAaA,6BAaA,+BAaA,mCAaA,oCAaA,gCAUA,uCAaA,uBAaA,mCAUA,0BAUA,yBAaA,8BAaP,iBAiDA,uCASA,+CASA,gCASA,8BASA,iDASA,iCASA,8BASA,8BASA,iCASA,iCASA,gCASA,gBAQA,sCAMA,mBAQA,wBAQA,2BAOA,qBAOA,mBACA;AA/mBN;AAAA;AAAA,IAAAC;AACA;AACA,IAAAA;AACA;AACA;AACO,IAAM,+BAA+B,OAAO,OAAO,YAAY;AAClE,YAAM,UAAU,cAAc,oBAAoB;AAClD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,6BAA6B,OAAO,OAAO,YAAY;AAChE,YAAM,UAAU,cAAc,kBAAkB;AAChD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,+BAA+B,OAAO,OAAO,YAAY;AAClE,YAAM,UAAU,cAAc,oBAAoB;AAClD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,6BAA6B,OAAO,OAAO,YAAY;AAChE,YAAM,UAAU,cAAc,kBAAkB;AAChD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,iCAAiC,OAAO,OAAO,YAAY;AACpE,YAAM,UAAU,cAAc,sBAAsB;AACpD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,sCAAsC,OAAO,OAAO,YAAY;AACzE,YAAM,UAAU,cAAc,2BAA2B;AACzD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,kBAAkB,OAAO,OAAO,YAAY;AACrD,YAAM,UAAU,cAAc,OAAO;AACrC,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,iCAAiC,OAAO,OAAO,YAAY;AACpE,YAAM,UAAU,cAAc,sBAAsB;AACpD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,YAAM,UAAU,cAAc,gBAAgB;AAC9C,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,+CAA+C,OAAO,OAAO,YAAY;AAClF,YAAM,UAAU,cAAc,oCAAoC;AAClE,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,wCAAwC,OAAO,OAAO,YAAY;AAC3E,YAAM,UAAU,cAAc,6BAA6B;AAC3D,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,YAAM,UAAU,cAAc,gBAAgB;AAC9C,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,8BAA8B,OAAO,OAAO,YAAY;AACjE,YAAM,UAAU,cAAc,mBAAmB;AACjD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,gCAAgC,OAAO,OAAO,YAAY;AACnE,YAAM,UAAU,cAAc,qBAAqB;AACnD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,oCAAoC,OAAO,OAAO,YAAY;AACvE,YAAM,UAAU,cAAc,yBAAyB;AACvD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,qCAAqC,OAAO,OAAO,YAAY;AACxE,YAAM,UAAU,cAAc,0BAA0B;AACxD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,iCAAiC,OAAO,OAAO,YAAY;AACpE,YAAM,UAAU,cAAc,sBAAsB;AACpD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,wCAAwC,OAAO,OAAO,YAAY;AAC3E,YAAM,UAAU,cAAc,6BAA6B;AAC3D,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,wBAAwB,OAAO,OAAO,YAAY;AAC3D,YAAM,UAAU,cAAc,aAAa;AAC3C,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,oCAAoC,OAAO,OAAO,YAAY;AACvE,YAAM,UAAU,cAAc,yBAAyB;AACvD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,2BAA2B,OAAO,OAAO,YAAY;AAC9D,YAAM,UAAU,cAAc,gBAAgB;AAC9C,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,0BAA0B,OAAO,OAAO,YAAY;AAC7D,YAAM,UAAU,cAAc,eAAe;AAC7C,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,+BAA+B,OAAO,OAAO,YAAY;AAClE,YAAM,UAAU,cAAc,oBAAoB;AAClD,UAAI;AACJ,aAAO,KAAK,UAAU,MAAM,KAAK,CAAC;AAClC,aAAO,oBAAoB,SAAS,SAAS,KAAK,QAAW,IAAI;AAAA,IACrE;AACO,IAAM,+BAA+B,OAAO,QAAQ,YAAY;AACnE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,6BAA6B,OAAO,QAAQ,YAAY;AACjE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,+BAA+B,OAAO,QAAQ,YAAY;AACnE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,YAAY,OAAO,MAAM,OAAO;AACtC,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,MACzC;AACA,aAAO;AAAA,IACX;AACO,IAAM,6BAA6B,OAAO,QAAQ,YAAY;AACjE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,uBAAuB,MAAM,OAAO;AAC/C,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,iCAAiC,OAAO,QAAQ,YAAY;AACrE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,sCAAsC,OAAO,QAAQ,YAAY;AAC1E,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,qCAAqC,MAAM,OAAO;AAC7D,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,kBAAkB,OAAO,QAAQ,YAAY;AACtD,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,iCAAiC,OAAO,QAAQ,YAAY;AACrE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,+CAA+C,OAAO,QAAQ,YAAY;AACnF,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,wCAAwC,OAAO,QAAQ,YAAY;AAC5E,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,0BAA0B,MAAM,OAAO;AAClD,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,8BAA8B,OAAO,QAAQ,YAAY;AAClE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,gCAAgC,OAAO,QAAQ,YAAY;AACpE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,oCAAoC,OAAO,QAAQ,YAAY;AACxE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,qCAAqC,OAAO,QAAQ,YAAY;AACzE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,iCAAiC,OAAO,QAAQ,YAAY;AACrE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,YAAY,OAAO,MAAM,OAAO;AACtC,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,MACzC;AACA,aAAO;AAAA,IACX;AACO,IAAM,wCAAwC,OAAO,QAAQ,YAAY;AAC5E,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,wBAAwB,OAAO,QAAQ,YAAY;AAC5D,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,oCAAoC,OAAO,QAAQ,YAAY;AACxE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,YAAY,OAAO,MAAM,OAAO;AACtC,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,MACzC;AACA,aAAO;AAAA,IACX;AACO,IAAM,2BAA2B,OAAO,QAAQ,YAAY;AAC/D,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,YAAY,OAAO,MAAM,OAAO;AACtC,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,MACzC;AACA,aAAO;AAAA,IACX;AACO,IAAM,0BAA0B,OAAO,QAAQ,YAAY;AAC9D,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACO,IAAM,+BAA+B,OAAO,QAAQ,YAAY;AACnE,UAAI,OAAO,cAAc,KAAK;AAC1B,eAAO,gBAAgB,QAAQ,OAAO;AAAA,MAC1C;AACA,YAAM,OAAO,MAAM,cAAU,OAAO,MAAM,OAAO;AACjD,UAAI,WAAW,CAAC;AAChB,iBAAW,MAAM,IAAI;AACrB,YAAM,WAAW;AAAA,QACb,WAAW,oBAAoB,MAAM;AAAA,QACrC,GAAG;AAAA,MACP;AACA,aAAO;AAAA,IACX;AACA,IAAM,kBAAkB,OAAO,QAAQ,YAAY;AAC/C,YAAM,eAAe;AAAA,QACjB,GAAG;AAAA,QACH,MAAM,MAAM,mBAAe,OAAO,MAAM,OAAO;AAAA,MACnD;AACA,YAAM,YAAY,sBAAsB,QAAQ,aAAa,IAAI;AACjE,cAAQ,WAAW;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,QAClE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,QACrE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,QAClE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,6BAA6B,cAAc,OAAO;AAAA,QAClE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,QACrE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,+BAA+B,cAAc,OAAO;AAAA,QACpE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,gCAAgC,cAAc,OAAO;AAAA,QACrE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,+BAA+B,cAAc,OAAO;AAAA,QACpE,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,gDAAgD,cAAc,OAAO;AAAA,QACrF,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,8CAA8C,cAAc,OAAO;AAAA,QACnF,KAAK;AAAA,QACL,KAAK;AACD,gBAAM,MAAM,sCAAsC,cAAc,OAAO;AAAA,QAC3E;AACI,gBAAM,aAAa,aAAa;AAChC,iBAAO,kBAAkB;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,MACT;AAAA,IACJ;AACA,IAAM,wCAAwC,OAAO,cAAc,YAAY;AAC3E,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,gCAAgC;AAAA,QAClD,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,gDAAgD,OAAO,cAAc,YAAY;AACnF,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,wCAAwC;AAAA,QAC1D,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,iCAAiC,OAAO,cAAc,YAAY;AACpE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,yBAAyB;AAAA,QAC3C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,uBAAuB;AAAA,QACzC,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,kDAAkD,OAAO,cAAc,YAAY;AACrF,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,0CAA0C;AAAA,QAC5D,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,0BAA0B;AAAA,QAC5C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,uBAAuB;AAAA,QACzC,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,+BAA+B,OAAO,cAAc,YAAY;AAClE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,uBAAuB;AAAA,QACzC,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,0BAA0B;AAAA,QAC5C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,kCAAkC,OAAO,cAAc,YAAY;AACrE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,0BAA0B;AAAA,QAC5C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,iCAAiC,OAAO,cAAc,YAAY;AACpE,YAAM,OAAO,aAAa;AAC1B,YAAM,eAAe,MAAM,IAAI;AAC/B,YAAM,YAAY,IAAI,yBAAyB;AAAA,QAC3C,WAAW,oBAAoB,YAAY;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC;AACD,aAAO,yBAA2B,WAAW,IAAI;AAAA,IACrD;AACA,IAAM,iBAAiB,CAAC,QAAQ,YAAY;AACxC,aAAO,KAAK,QAAQ;AAAA,QAChB,aAAa;AAAA,QACb,YAAY,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,QAC3E,WAAW;AAAA,QACX,cAAc;AAAA,MAClB,CAAC;AAAA,IACL;AACA,IAAM,uCAAuC,CAAC,QAAQ,YAAY;AAC9D,aAAO,KAAK,QAAQ;AAAA,QAChB,aAAa,CAAC,MAAM,eAAe,GAAG,OAAO;AAAA,QAC7C,YAAY;AAAA,MAChB,CAAC;AAAA,IACL;AACA,IAAM,oBAAoB,CAAC,QAAQ,YAAY;AAC3C,YAAM,UAAU,UAAU,CAAC,GACtB,OAAO,CAACC,OAAMA,MAAK,IAAI,EACvB,IAAI,CAAC,UAAU;AAChB,eAAO,uBAAuB,OAAO,OAAO;AAAA,MAChD,CAAC;AACD,aAAO;AAAA,IACX;AACA,IAAM,yBAAyB,CAAC,QAAQ,YAAY;AAChD,aAAO,KAAK,QAAQ;AAAA,QAChB,cAAc,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,QAC7E,YAAY;AAAA,QACZ,kBAAkB,CAAC,MAAM,cAAgB,oBAAsB,aAAe,CAAC,CAAC,CAAC;AAAA,QACjF,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AACA,IAAM,4BAA4B,CAAC,QAAQ,YAAY;AACnD,aAAO,KAAK,QAAQ;AAAA,QAChB,YAAY,CAAC,MAAM,kBAAkB,GAAG,OAAO;AAAA,QAC/C,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,IAAM,sBAAsB,CAAC,YAAY;AAAA,MACrC,gBAAgB,OAAO;AAAA,MACvB,WAAW,OAAO,QAAQ,kBAAkB,KAAK,OAAO,QAAQ,mBAAmB,KAAK,OAAO,QAAQ,kBAAkB;AAAA,MACzH,mBAAmB,OAAO,QAAQ,YAAY;AAAA,MAC9C,MAAM,OAAO,QAAQ,aAAa;AAAA,IACtC;AAEA,IAAM,oBAAoB,kBAAkB,+BAAe;AAC3D,IAAM,sBAAsB,OAAO,SAAS,SAAS,MAAM,kBAAkB,SAAS;AAClF,YAAM,EAAE,UAAU,WAAW,SAAS,MAAM,MAAM,SAAS,IAAI,MAAM,QAAQ,SAAS;AACtF,YAAM,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,MAAM,SAAS,SAAS,GAAG,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,OAAO,WAAW;AAAA,QACzE;AAAA,MACJ;AACA,UAAI,qBAAqB,QAAW;AAChC,iBAAS,WAAW;AAAA,MACxB;AACA,UAAI,SAAS,QAAW;AACpB,iBAAS,OAAO;AAAA,MACpB;AACA,aAAO,IAAI,YAAc,QAAQ;AAAA,IACrC;AAAA;AAAA;;;AChoBA,IAMa;AANb;AAAA;AAAA,IAAAC;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,4BAAN,cAAwC,QAC1C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,sBAAsB,CAAC,CAAC,EACvD,EAAE,yBAAyB,2BAA2B,EACtD,EAAE,QAAQ,MAAM,EAChB,IAAI,4BAA4B,EAChC,GAAG,4BAA4B,EAC/B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,0BAAN,cAAsC,QACxC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,oBAAoB,CAAC,CAAC,EACrD,EAAE,yBAAyB,yBAAyB,EACpD,EAAE,QAAQ,MAAM,EAChB,IAAI,0BAA0B,EAC9B,GAAG,0BAA0B,EAC7B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,4BAAN,cAAwC,QAC1C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,sBAAsB,CAAC,CAAC,EACvD,EAAE,yBAAyB,2BAA2B,EACtD,EAAE,QAAQ,MAAM,EAChB,IAAI,4BAA4B,EAChC,GAAG,4BAA4B,EAC/B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,0BAAN,cAAsC,QACxC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,oBAAoB,CAAC,CAAC,EACrD,EAAE,yBAAyB,yBAAyB,EACpD,EAAE,QAAQ,MAAM,EAChB,IAAI,0BAA0B,EAC9B,GAAG,0BAA0B,EAC7B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,8BAAN,cAA0C,QAC5C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,wBAAwB,CAAC,CAAC,EACzD,EAAE,yBAAyB,6BAA6B,EACxD,EAAE,QAAQ,MAAM,EAChB,IAAI,8BAA8B,EAClC,GAAG,8BAA8B,EACjC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAOa;AAPb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,mCAAN,cAA+C,QACjD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,6BAA6B,CAAC,CAAC,EAC9D,EAAE,yBAAyB,kCAAkC,EAC7D,EAAE,kDAAkD,mDAAmD,EACvG,IAAI,mCAAmC,EACvC,GAAG,mCAAmC,EACtC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAOa;AAPb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,eAAN,cAA2B,QAC7B,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,SAAS,CAAC,CAAC,EAC1C,EAAE,yBAAyB,cAAc,EACzC,EAAE,8BAA8B,MAAM,EACtC,IAAI,eAAe,EACnB,GAAG,eAAe,EAClB,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,8BAAN,cAA0C,QAC5C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,wBAAwB,CAAC,CAAC,EACzD,EAAE,yBAAyB,6BAA6B,EACxD,EAAE,QAAQ,MAAM,EAChB,IAAI,8BAA8B,EAClC,GAAG,8BAA8B,EACjC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAOa;AAPb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,kBAAkB,CAAC,CAAC,EACnD,EAAE,yBAAyB,uBAAuB,EAClD,EAAE,uCAAuC,wCAAwC,EACjF,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAOa;AAPb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,4CAAN,cAAwD,QAC1D,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,sCAAsC,CAAC,CAAC,EACvE,EAAE,yBAAyB,2CAA2C,EACtE,EAAE,2DAA2D,4DAA4D,EACzH,IAAI,4CAA4C,EAChD,GAAG,4CAA4C,EAC/C,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,qCAAN,cAAiD,QACnD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,+BAA+B,CAAC,CAAC,EAChE,EAAE,yBAAyB,oCAAoC,EAC/D,EAAE,QAAQ,MAAM,EAChB,IAAI,qCAAqC,EACzC,GAAG,qCAAqC,EACxC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,kBAAkB,CAAC,CAAC,EACnD,EAAE,yBAAyB,uBAAuB,EAClD,EAAE,QAAQ,MAAM,EAChB,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,2BAAN,cAAuC,QACzC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,qBAAqB,CAAC,CAAC,EACtD,EAAE,yBAAyB,0BAA0B,EACrD,EAAE,QAAQ,MAAM,EAChB,IAAI,2BAA2B,EAC/B,GAAG,2BAA2B,EAC9B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,6BAAN,cAAyC,QAC3C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,uBAAuB,CAAC,CAAC,EACxD,EAAE,yBAAyB,4BAA4B,EACvD,EAAE,QAAQ,MAAM,EAChB,IAAI,6BAA6B,EACjC,GAAG,6BAA6B,EAChC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,iCAAN,cAA6C,QAC/C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,2BAA2B,CAAC,CAAC,EAC5D,EAAE,yBAAyB,gCAAgC,EAC3D,EAAE,QAAQ,MAAM,EAChB,IAAI,iCAAiC,EACrC,GAAG,iCAAiC,EACpC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,kCAAN,cAA8C,QAChD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,4BAA4B,CAAC,CAAC,EAC7D,EAAE,yBAAyB,iCAAiC,EAC5D,EAAE,QAAQ,MAAM,EAChB,IAAI,kCAAkC,EACtC,GAAG,kCAAkC,EACrC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,8BAAN,cAA0C,QAC5C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,wBAAwB,CAAC,CAAC,EACzD,EAAE,yBAAyB,6BAA6B,EACxD,EAAE,QAAQ,MAAM,EAChB,IAAI,8BAA8B,EAClC,GAAG,8BAA8B,EACjC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,qCAAN,cAAiD,QACnD,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,+BAA+B,CAAC,CAAC,EAChE,EAAE,yBAAyB,oCAAoC,EAC/D,EAAE,QAAQ,MAAM,EAChB,IAAI,qCAAqC,EACzC,GAAG,qCAAqC,EACxC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,qBAAN,cAAiC,QACnC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,eAAe,CAAC,CAAC,EAChD,EAAE,yBAAyB,oBAAoB,EAC/C,EAAE,QAAQ,MAAM,EAChB,IAAI,qBAAqB,EACzB,GAAG,qBAAqB,EACxB,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,iCAAN,cAA6C,QAC/C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,2BAA2B,CAAC,CAAC,EAC5D,EAAE,yBAAyB,gCAAgC,EAC3D,EAAE,QAAQ,MAAM,EAChB,IAAI,iCAAiC,EACrC,GAAG,iCAAiC,EACpC,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAOa;AAPb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AACA;AAEO,IAAM,wBAAN,cAAoC,QACtC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,kBAAkB,CAAC,CAAC,EACnD,EAAE,yBAAyB,uBAAuB,EAClD,EAAE,uCAAuC,MAAM,EAC/C,IAAI,wBAAwB,EAC5B,GAAG,wBAAwB,EAC3B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACtBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,uBAAN,cAAmC,QACrC,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,iBAAiB,CAAC,CAAC,EAClD,EAAE,yBAAyB,sBAAsB,EACjD,EAAE,QAAQ,MAAM,EAChB,IAAI,uBAAuB,EAC3B,GAAG,uBAAuB,EAC1B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAMa;AANb;AAAA;AAAA,IAAAE;AACA,IAAAA;AACA,IAAAA;AACA;AACA;AAEO,IAAM,4BAAN,cAAwC,QAC1C,aAAa,EACb,GAAG,YAAY,EACf,EAAE,SAAUC,UAAS,IAAI,QAAQC,IAAG;AACrC,aAAO;AAAA,QACH,eAAe,QAAQ,KAAK,WAAW,KAAK,WAAW;AAAA,QACvD,kBAAkB,QAAQD,SAAQ,iCAAiC,CAAC;AAAA,MACxE;AAAA,IACJ,CAAC,EACI,EAAE,6BAA6B,sBAAsB,CAAC,CAAC,EACvD,EAAE,yBAAyB,2BAA2B,EACtD,EAAE,QAAQ,MAAM,EAChB,IAAI,4BAA4B,EAChC,GAAG,4BAA4B,EAC/B,MAAM,EAAE;AAAA,IACb;AAAA;AAAA;;;ACrBA,IAyBM,UAyBO;AAlDb;AAAA;AAAA,IAAAE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACO,IAAM,kBAAN,cAA8B,sBAAsB;AAAA,IAC3D;AACA,2BAAuB,UAAU,eAAe;AAAA;AAAA;;;ACpDhD;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACtBA;AAAA;AAAA;AAAA;;;ACAA,IAGa;AAHb;AAAA;AAAA,IAAAC;AACA;AACA;AACO,IAAM,4BAA4B,gBAAgB,uBAAuB,0BAA0B,aAAa,aAAa,YAAY;AAAA;AAAA;;;ACHhJ;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,iBAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACLA;AAAA;AAAA,IAAAC;AAAA;AAAA;", "names": ["init_dist_es", "init_dist_es", "init_dist_es", "getRuntimeConfig", "init_dist_es", "init_dist_es", "init_dist_es", "getRuntimeConfig", "init_dist_es", "init_dist_es", "init_dist_es", "e", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "Command", "o", "init_dist_es", "init_dist_es", "init_dist_es", "init_dist_es"]}