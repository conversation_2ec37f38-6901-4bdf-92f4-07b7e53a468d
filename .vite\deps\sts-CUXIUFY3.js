import {
  AssumeRoleCommand,
  AssumeRoleResponseFilterSensitiveLog,
  AssumeRoleWithWebIdentityCommand,
  AssumeRoleWithWebIdentityRequestFilterSensitiveLog,
  AssumeRoleWithWebIdentityResponseFilterSensitiveLog,
  CredentialsFilterSensitiveLog,
  ExpiredTokenException,
  IDPCommunicationErrorException,
  IDPRejectedClaimException,
  InvalidIdentityTokenException,
  MalformedPolicyDocumentException,
  PackedPolicyTooLargeException,
  RegionDisabledException,
  STS,
  STSClient,
  STSServiceException,
  decorateDefaultCredentialProvider,
  getDefaultRoleAssumer,
  getDefaultRoleAssumerWithWebIdentity,
  init_sts
} from "./chunk-CS7IYQNM.js";
import "./chunk-TJEVHZ2X.js";
import {
  Client,
  Command
} from "./chunk-YDYV3GMK.js";
import "./chunk-NOR3QQAA.js";
import "./chunk-EWTE5DHJ.js";
init_sts();
export {
  Command as $Command,
  AssumeRoleCommand,
  AssumeRoleResponseFilterSensitiveLog,
  AssumeRoleWithWebIdentityCommand,
  AssumeRoleWithWebIdentityRequestFilterSensitiveLog,
  AssumeRoleWithWebIdentityResponseFilterSensitiveLog,
  CredentialsFilterSensitiveLog,
  ExpiredTokenException,
  IDPCommunicationErrorException,
  IDPRejectedClaimException,
  InvalidIdentityTokenException,
  MalformedPolicyDocumentException,
  PackedPolicyTooLargeException,
  RegionDisabledException,
  STS,
  STSClient,
  STSServiceException,
  Client as __Client,
  decorateDefaultCredentialProvider,
  getDefaultRoleAssumer,
  getDefaultRoleAssumerWithWebIdentity
};
//# sourceMappingURL=sts-CUXIUFY3.js.map
